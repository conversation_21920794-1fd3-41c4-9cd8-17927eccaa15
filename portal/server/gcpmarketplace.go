package server

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"cloud.google.com/go/pubsub/v2"
	"cloud.google.com/go/pubsub/v2/apiv1/pubsubpb"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/api/cloudcommerceprocurement/v1"
	"google.golang.org/api/option"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/durationpb"
	"gopkg.in/gomail.v2"

	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
)

type gcpContactForm struct {
	contactForm

	GCPDetails *gcpCustomer
}

type gcpCustomer struct {
	AccountName string
	Provider    string
}

const (
	gcpProviderID              = "akuity-public"
	gcpAccountNameTemplate     = "providers/%v/accounts/%v"
	gcpEntitlementNameTemplate = "providers/%v/entitlements/%v"
	gcpTopicProjectID          = "cloudcommerceproc-prod"
	gcpProjectID               = "akuity-public"
	gcpTopicID                 = "akuity-public"
	gcpRedirectURL             = "gcp-marketplace-onboard"
	gcpBasePath                = "https://cloudcommerceprocurement.googleapis.com/"
	eventRetryThreshold        = 5
)

// getPublicKey returns the public key for the given key id from https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>
func getPublicKey(kin string) (string, error) {
	url := "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"
	res, err := http.Get(url)
	if err != nil {
		return "", err
	}
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}
	var data map[string]string
	if err := json.Unmarshal(body, &data); err != nil {
		return "", err
	}
	if _, ok := data[kin]; !ok {
		return "", fmt.Errorf("require key %v not found", kin)
	}
	return data[kin], nil
}

type gcpEvent struct {
	EventId     string `json:"eventId"`
	EventType   string `json:"eventType"`
	ProviderId  string `json:"providerId"`
	Entitlement struct {
		Id               string `json:"id"`
		NewPlan          string `json:"newPlan"`
		UpdateTime       string `json:"updateTime"`
		NewOfferDuration string `json:"newOfferDuration"`
		CancellationDate string `json:"cancellationDate"`
	} `json:"entitlement"`
}

func (s *AkuityPortalServer) RouteGCPMarketPlace(r *gin.RouterGroup) {
	r.Any("/redirect", func(c *gin.Context) {
		data, err := c.GetRawData()
		if err != nil {
			_ = c.AbortWithError(http.StatusBadRequest, err)
			return
		}
		c.Redirect(302, fmt.Sprintf("%v/%v?%v", s.cfg.PortalURL, gcpRedirectURL, string(data)))
	})

	r.POST("/contact-us", s.Handler(func(c *gin.Context) (interface{}, error) {
		formData := gcpContactForm{}
		if err := c.BindJSON(&formData); err != nil {
			s.Log.Error(err, "invalid gcp marketplace registration data")
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "Registration data not valid. Please try again, or contact support!")
		}

		accountName, err := s.approveAccount(c, formData.RegToken)
		if err != nil {
			s.Log.Error(err, "failed to approve account")
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "Registration failed!")
		}
		formData.GCPDetails = &gcpCustomer{
			AccountName: accountName,
			Provider:    gcpProviderID,
		}
		if err := s.sendMarketPlaceInviteMail(&formData, gcp); err != nil {
			s.Log.Error(err, "failed to send admin email")
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "Failed to send registration invite")
		}
		return nil, nil
	}))
}

func (s *AkuityPortalServer) sendGCPMarketPlaceAdminMails(eventData string, entitlement *cloudcommerceprocurement.Entitlement) error {
	// admin notification mail
	m1 := gomail.NewMessage()
	m1.SetAddressHeader("From", akuityNoReplyEmail, "Akuity GCP Marketplace")
	m1.SetHeader("To", s.getMarketPlaceNotifyEmail())
	// https://us-west-2.console.aws.amazon.com/ses/home?region=us-west-2#/configuration-sets/ses-history
	m1.SetHeader("X-SES-CONFIGURATION-SET", "ses-history")
	m1.SetHeader("Subject", "[GCP] Customer Entitlement Event")

	entitlementData, err := json.Marshal(entitlement)
	if err != nil {
		return err
	}

	notif := fmt.Sprintf("event: %v\n\nentitlement_data: %v", string(eventData), string(entitlementData))
	m1.SetBody("text/plain", string(notif))
	if err := s.mailer.DialAndSend(m1); err != nil {
		return err
	}
	return nil
}

func getAccountName(accountID string) string {
	return fmt.Sprintf(gcpAccountNameTemplate, gcpProviderID, accountID)
}

func getEntitlementName(entitlementID string) string {
	return fmt.Sprintf(gcpEntitlementNameTemplate, gcpProviderID, entitlementID)
}

func (s *AkuityPortalServer) approveAccount(ctx context.Context, token string) (string, error) {
	if token == "" {
		return "", fmt.Errorf("empty gcp token")
	}
	if s.cfg.GcpCredentialsPath == "" {
		return "", fmt.Errorf("server is unable to handle marketplace requests")
	}
	client, err := cloudcommerceprocurement.NewService(ctx, option.WithCredentialsFile(s.cfg.GcpCredentialsPath), option.WithEndpoint(gcpBasePath))
	if err != nil {
		return "", fmt.Errorf("could not create client: %w", err)
	}
	client.BasePath = gcpBasePath
	claims := jwt.MapClaims{}
	jwtToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		if kid, ok := token.Header["kid"].(string); ok {
			pk, err := getPublicKey(kid)
			if err != nil {
				return nil, err
			}
			return jwt.ParseRSAPublicKeyFromPEM([]byte(pk))
		} else {
			return nil, fmt.Errorf("gcp pub key not found for kid = %v", kid)
		}
	})
	if err != nil {
		return "", fmt.Errorf("invalid gcp token: %w", err)
	}

	accountId, ok := jwtToken.Claims.(jwt.MapClaims)["sub"].(string)
	if !ok || accountId == "" {
		return "", fmt.Errorf("invalid account id in token")
	}

	_, err = client.Providers.Accounts.Approve(getAccountName(accountId), &cloudcommerceprocurement.ApproveAccountRequest{
		ApprovalName: "signup",
	}).Do()

	return getAccountName(accountId), err
}

func (s *AkuityPortalServer) ListenToPubSub(ctx context.Context) error {
	client, err := pubsub.NewClient(ctx, gcpProjectID, option.WithCredentialsFile(s.cfg.GcpCredentialsPath))
	if err != nil {
		return err
	}

	procurement, err := cloudcommerceprocurement.NewService(ctx, option.WithCredentialsFile(s.cfg.GcpCredentialsPath), option.WithEndpoint(gcpBasePath))
	if err != nil {
		return err
	}
	procurement.BasePath = gcpBasePath

	if _, err := client.SubscriptionAdminClient.CreateSubscription(ctx, &pubsubpb.Subscription{
		Name:  fmt.Sprintf("projects/%s/subscriptions/%s", gcpProjectID, gcpProjectID),
		Topic: fmt.Sprintf("projects/%s/topics/%s", gcpTopicProjectID, gcpTopicID),
		RetryPolicy: &pubsubpb.RetryPolicy{
			// 60 second min retry interval for nack-ed messages so that we don't hit api limits
			// retrying to approve entitlements
			MinimumBackoff: durationpb.New(time.Minute),
		},
	}); err != nil {
		if rpcErr, ok := status.FromError(err); ok && rpcErr.Code() == codes.AlreadyExists {
			s.Log.Info("subscription already exists, continuing")
		} else {
			return fmt.Errorf("failed to create subscription: %w", err)
		}
	}

	// listen to subscription.
	subscription := client.Subscriber(gcpProjectID)
	s.Log.Info("listening to gcp marketplace subscription")
	return subscription.Receive(ctx, s.handlePubSubMsg(procurement))
}

func (s *AkuityPortalServer) handleErrorEvent(msg *pubsub.Message) {
	s.gcpMarketplaceMapMutex.Lock()
	defer s.gcpMarketplaceMapMutex.Unlock()
	if count, ok := s.gcpMarketPlaceErrorIDMap[msg.ID]; !ok {
		s.gcpMarketPlaceErrorIDMap[msg.ID] = 1
	} else {
		s.gcpMarketPlaceErrorIDMap[msg.ID] = count + 1
		if s.gcpMarketPlaceErrorIDMap[msg.ID] > eventRetryThreshold {
			// when retries hit max threshold ack the message to drop it from queue
			// and delete the event from the map
			delete(s.gcpMarketPlaceErrorIDMap, msg.ID)
			msg.Ack()
			return
		}
	}
	msg.Nack()
}

func (s *AkuityPortalServer) handlePubSubMsg(procurementSVC *cloudcommerceprocurement.Service) func(ctx context.Context, msg *pubsub.Message) {
	return func(ctx context.Context, msg *pubsub.Message) {
		// Do something with the message.
		event := gcpEvent{}
		if err := json.Unmarshal(msg.Data, &event); err != nil {
			s.Log.Error(err, "error parsing event", "event", string(msg.Data))
			return
		}

		switch event.EventType {
		case "ENTITLEMENT_CREATION_REQUESTED":
			if _, err := procurementSVC.Providers.Entitlements.Approve(getEntitlementName(event.Entitlement.Id), &cloudcommerceprocurement.ApproveEntitlementRequest{}).Do(); err != nil {
				s.Log.Error(err, "failed to approve entitlement", "event", string(msg.Data))
				s.handleErrorEvent(msg)
				return
			}

		case "ENTITLEMENT_PLAN_CHANGE_REQUESTED":
			if _, err := procurementSVC.Providers.Entitlements.ApprovePlanChange(getEntitlementName(event.Entitlement.Id), &cloudcommerceprocurement.ApproveEntitlementPlanChangeRequest{
				PendingPlanName: event.Entitlement.NewPlan,
			}).Do(); err != nil {
				s.Log.Error(err, "failed to approve entitlement change", "event", string(msg.Data))
				s.handleErrorEvent(msg)
				return
			}
		case "ENTITLEMENT_ACTIVE",
			"ENTITLEMENT_PLAN_CHANGED",
			"ENTITLEMENT_CANCELLED",
			"ENTITLEMENT_DELETED",
			"ACCOUNT_DELETED":
			var (
				data *cloudcommerceprocurement.Entitlement
				err  error
			)
			if !strings.Contains(event.EventType, "DELETED") {
				// if not a delete event get the entitlement data
				data, err = procurementSVC.Providers.Entitlements.Get(getEntitlementName(event.Entitlement.Id)).Do()
				if err != nil {
					s.Log.Error(err, "failed to get entitlement", "event", string(msg.Data))
					s.handleErrorEvent(msg)
					return
				}
			}
			if err := s.sendGCPMarketPlaceAdminMails(string(msg.Data), data); err != nil {
				s.Log.Error(err, "failed to send entitlement change email", "event", string(msg.Data))
				s.handleErrorEvent(msg)
				return
			}
		}

		msg.Ack()
	}
}
