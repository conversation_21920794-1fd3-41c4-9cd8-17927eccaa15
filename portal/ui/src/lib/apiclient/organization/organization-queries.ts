import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { defaultAuditFilters } from '@ui/config/filters';
import { useMainContext } from '@ui/lib/context/main-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { OrganizationRole, RawAuditFilters, Subset, UseQueryOptionsOverride } from '@ui/lib/types';
import { auditFilterTransformer } from '@ui/organizations/components/audit-log/filters/utils';
import { AuditLogArchiveFilter } from '@ui/organizations/components/audit-log-archive/filters';
import { CustomRole } from '@ui/organizations/components/custom-roles/schema';
import { RolePolicyTransformer } from '@ui/organizations/components/custom-roles/transformer';
import { MarketplaceName } from '@ui/views/marketplace-onboard/config';
import { MarketplaceData } from '@ui/views/marketplace-onboard/marketplace-onboard';

import {
  APIKey,
  DeleteAPIKeyRequest,
  RegenerateAPIKeySecretRequest,
  RegenerateAPIKeySecretResponse
} from '../apikey/v1/apikey_pb';
import { queryKeys } from '../query-keys';
import { DeleteTeamMemberRequest } from '../team/v1/team_pb';
import { FeatureStatus } from '../types/features/v1/features_pb';
import { ApiError, useApiFetch } from '../use-api-fetch';
import { getQueryParamsAsString } from '../utils';

import {
  AddTeamMemberRequest,
  AddTeamMemberResponse,
  AuditLogArchiveFilters,
  BillingCheckoutRequest,
  BillingCheckoutResponse,
  BillingDetails,
  CreateCustomRoleRequest,
  CreateCustomRoleResponse,
  CreateOrganizationAPIKeyRequest,
  CreateOrganizationAPIKeyResponse,
  CreateOrganizationRequest,
  DeleteCustomRoleRequest,
  CreateTeamRequest,
  CreateTeamResponse,
  DeleteOrganizationRequest,
  DeleteSSOConfigurationRequest,
  DeleteTeamRequest,
  EnsureSSOConfigurationRequest,
  EnsureSSOConfigurationResponse,
  GetAuditLogsResponse,
  GetAvailableAddonsRequest,
  GetAvailableAddonsResponse,
  GetCustomerDetailsRequest,
  GetCustomerDetailsResponse,
  GetOIDCMapRequest,
  GetOIDCMapResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  GetSSOConfigurationRequest,
  GetSSOConfigurationResponse,
  GetUserRoleInOrganizationRequest,
  GetUserRoleInOrganizationResponse,
  InviteMembersRequest,
  JoinOrganizationRequest,
  JoinOrganizationResponse,
  ListAuditLogsArchivesRequest,
  ListAuditLogsArchivesResponse,
  ListCustomRolesRequest,
  ListCustomRolesResponse,
  ListOrganizationAPIKeysRequest,
  ListOrganizationAPIKeysResponse,
  ListOrganizationInviteesRequest,
  ListOrganizationInviteesResponse,
  ListOrganizationMembersRequest,
  ListOrganizationMembersResponse,
  ListTeamMembersRequest,
  ListTeamMembersResponse,
  ListTeamsResponse,
  Organization,
  RejectOrganizationRequest,
  RemoveOrganizationMemberRequest,
  SubscriptionAddon,
  TeamMember,
  UninviteOrganizationMemberRequest,
  UpdateCustomRoleRequest,
  UpdateCustomRoleResponse,
  UpdateOIDCMapRequest,
  UpdateOrganizationMemberRoleRequest,
  UpdateOrganizationRequest,
  UpdateTeamRequest,
  UpdateTeamResponse,
  UserTeam,
  GetTeamOIDCMapRequest,
  GetTeamOIDCMapResponse,
  UpdateTeamOIDCMapRequest,
  GetKubeVisionUsageResponse,
  GetKubeVisionUsageRequest,
  ListUsersMFAStatusResponse,
  RequestMFAResetRequest,
  GetFeatureStatusesRequest,
  GetFeatureStatusesResponse,
  GetOrganizationPermissionsResponse,
  ListAvailablePlansResponse,
  CreateOrganizationResponse,
  ListArgocdInstancesQuotaResponse,
  ListKargoInstancesQuotaResponse,
  InstanceQuota,
  KargoInstanceQuota,
  UpdateArgocdInstancesQuotaResponse,
  UpdateArgocdInstancesQuotaRequest,
  UpdateKargoInstancesQuotaResponse,
  UpdateKargoInstancesQuotaRequest
} from './v1/organization_pb';

// Queries
export const useGetOrganization = ({
  id
}: Omit<PlainMessage<GetOrganizationRequest>, 'idType'>) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.item(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}`)
        .then(GetOrganizationResponse.fromJson)
        .then((res) => res.organization),
    staleTime: 500
  });
};

export const useListApiKeysQuery = (
  { id }: PlainMessage<ListOrganizationAPIKeysRequest>,
  opts: UseQueryOptionsOverride<APIKey[], readonly string[]>
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.apiKeys(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/apikeys`)
        .then(ListOrganizationAPIKeysResponse.fromJson)
        .then((res) => res.apiKeys),
    ...opts
  });
};

export const useGetMembers = (
  { id }: PlainMessage<ListOrganizationMembersRequest>,
  { enabled = true } = {}
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.members(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/members`)
        .then(ListOrganizationMembersResponse.fromJson)
        .then((res) => res.members),
    initialData: [],
    enabled
  });
};

export const useGetUsersMFAStatus = () => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const organizationId = currentOrg?.id;

  return useQuery({
    queryKey: queryKeys.organizations.mfaStatus(organizationId).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${organizationId}/users-mfa-status`)
        .then(ListUsersMFAStatusResponse.fromJson)
        .then((res) => res.userMfaStatus)
  });
};

export const useGetInvitees = (
  { id }: PlainMessage<ListOrganizationInviteesRequest>,
  { enabled = true } = {}
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.invitees(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/invitees`)
        .then(ListOrganizationInviteesResponse.fromJson)
        .then((res) => res.invitees),
    initialData: [],
    enabled
  });
};

export const useGetAuditLogs = ({
  id,
  filters = defaultAuditFilters
}: {
  id: string;
  filters: RawAuditFilters;
}) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: [...queryKeys.organizations.auditLogs(id).queryKey, filters],
    queryFn: () => {
      return apiFetch(
        `v1/organizations/${id}/audit-logs?${auditFilterTransformer.toSearchString(filters)}`
      ).then(GetAuditLogsResponse.fromJson);
    }
  });
};

export const useGetAuditLogArchives = (
  { id, filters }: PlainMessage<ListAuditLogsArchivesRequest>,
  opts?: UseQueryOptionsOverride<
    PlainMessage<ListAuditLogsArchivesResponse>,
    readonly (string | AuditLogArchiveFilters)[]
  >
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.auditLogArchives(id, new AuditLogArchiveFilters(filters))
      .queryKey,
    queryFn: () =>
      apiFetch(
        `v1/organizations/${id}/audit-logs-archives${AuditLogArchiveFilter.toSearchString(filters)}`
      ).then(ListAuditLogsArchivesResponse.fromJson),
    enabled: !!id,
    ...opts
  });
};

export const useGetUserRole = (
  { id }: PlainMessage<GetUserRoleInOrganizationRequest>,
  { enabled = true } = {}
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.userRole(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/role`)
        .then(GetUserRoleInOrganizationResponse.fromJson)
        .then((res) => res.role),
    enabled
  });
};

export const useGetCustomerDetails = (
  { id }: PlainMessage<GetCustomerDetailsRequest>,
  { enabled = true } = {}
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.customerDetails(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/billing/customer`, {})
        .then(GetCustomerDetailsResponse.fromJson)
        .then((res) => res.billingDetails || null),
    enabled
  });
};

export const useGetSSOConfiguration = ({ id }: PlainMessage<GetSSOConfigurationRequest>) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.ssoConfiguration(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/sso/configuration`).then(
        GetSSOConfigurationResponse.fromJson
      )
  });
};

// The new feature gate API
export const useGetFeatureStatus = (
  { id }: PlainMessage<GetFeatureStatusesRequest>,
  { enabled = true } = {}
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.featureStatuses(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/feature-statuses`)
        .then(GetFeatureStatusesResponse.fromJson)
        .then((res) => res.featureStatuses),
    enabled
  });
};

export const useGetOIDCMap = ({ id }: PlainMessage<GetOIDCMapRequest>) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.oidcMap(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/oidc-map`)
        .then(GetOIDCMapResponse.fromJson)
        .then((res) => res.entries)
  });
};

export const useGetOIDCTeamMap = ({ organizationId }: PlainMessage<GetTeamOIDCMapRequest>) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.oidcTeamMap(organizationId).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${organizationId}/teams-oidc-map`)
        .then(GetTeamOIDCMapResponse.fromJson)
        .then((res) => res.entries)
  });
};

export const useListAvailablePlans = () => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.plans().queryKey,
    queryFn: () =>
      apiFetch(`v1/billing/plans`)
        .then(ListAvailablePlansResponse.fromJson)
        .then((res) => res.plans)
  });
};

export const useGetAvailableAddons = ({ id, plan }: PlainMessage<GetAvailableAddonsRequest>) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.billingAddons(id, plan).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/billing/${plan}/addons`)
        .then(GetAvailableAddonsResponse.fromJson)
        .then((res) => res.addons as SubscriptionAddon[])
  });
};

// Mutations
export const useCreateApiKeyMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<APIKey, ApiError, PlainMessage<CreateOrganizationAPIKeyRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/organizations/${id}/apikeys`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
        .then(CreateOrganizationAPIKeyResponse.fromJson)
        .then((res) => res.apiKey)
  });
};

export const useCreateOrganizationMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<CreateOrganizationResponse, ApiError, PlainMessage<CreateOrganizationRequest>>(
    {
      mutationFn: (data) =>
        apiFetch('v1/organizations', {
          method: 'POST',
          body: JSON.stringify(data)
        }).then((res) => CreateOrganizationResponse.fromJson(res))
    }
  );
};

export const useDeleteApiKeyMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<DeleteAPIKeyRequest>>({
    mutationFn: ({ id }) =>
      apiFetch(`v1/apikeys/${id}`, {
        method: 'DELETE'
      })
  });
};

export const useRegenerateApiKeyMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<APIKey, ApiError, PlainMessage<RegenerateAPIKeySecretRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/apikeys/${id}/regenerate`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
        .then(RegenerateAPIKeySecretResponse.fromJson)
        .then((res) => res.apiKey)
  });
};

export const useDeleteOrganizationMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<DeleteOrganizationRequest>>({
    mutationFn: ({ id }) =>
      apiFetch(`v1/organizations/${id}`, {
        method: 'DELETE'
      })
  });
};

export const useUpdateOrganizationMutation = () => {
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<UpdateOrganizationRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/organizations/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      }),
    onSuccess: (_, { id, mfa, ai }) => {
      queryClient.setQueryData(
        queryKeys.organizations.item(id).queryKey,
        (oldData: GetOrganizationResponse) => ({
          ...oldData,
          mfaSettings: {
            enabled: mfa?.enabled
          },
          aiSettings: {
            provider: ai?.provider
          }
        })
      );
    }
  });
};

export const useSendResetMFARequestMutation = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<RequestMFAResetRequest>>({
    mutationFn: ({ organizationId, email }) =>
      apiFetch(`v1/organizations/${organizationId}/users/${email}/mfa/reset`, {
        method: 'POST'
      })
  });
};

export const useInviteMember = () => {
  const notification = useNotificationContext();
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<InviteMembersRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(
        `v1/organizations/${id}/members/invite`,
        {
          method: 'POST',
          body: JSON.stringify(data)
        },
        { disableErrorMessage: true }
      ),
    onError: (err) => {
      notification.error({ message: `Failed to send invitation email: ${err?.message}` });
    }
  });
};

export const useUninviteMember = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<UninviteOrganizationMemberRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/organizations/${id}/members/uninvite`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
  });
};

export const useRemoveMember = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<RemoveOrganizationMemberRequest>>({
    mutationFn: ({ id, memberId }) =>
      apiFetch(`v1/organizations/${id}/members/${memberId}`, {
        method: 'DELETE'
      })
  });
};

export const useChangeMemberRole = () => {
  const apiFetch = useApiFetch();

  return useMutation<
    unknown,
    ApiError,
    PlainMessage<UpdateOrganizationMemberRoleRequest> & { role: OrganizationRole }
  >({
    mutationFn: ({ id, memberId, ...data }) =>
      apiFetch(`v1/organizations/${id}/members/${memberId}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
  });
};

export const useJoinOrganization = () => {
  const apiFetch = useApiFetch();

  return useMutation<Organization, ApiError, PlainMessage<JoinOrganizationRequest>>({
    mutationFn: ({ id }) =>
      apiFetch(`v1/organizations/${id}/join`, {
        method: 'POST'
      })
        .then(JoinOrganizationResponse.fromJson)
        .then((res) => res.organization)
  });
};

export const useRejectOrganization = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<RejectOrganizationRequest>>({
    mutationFn: ({ id }) =>
      apiFetch(`v1/organizations/${id}/reject`, {
        method: 'POST'
      })
  });
};

export const useUpdateBillingDetails = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, { id: string; patch: Subset<BillingDetails> }>({
    mutationFn: ({ id, patch }) =>
      apiFetch(`v1/organizations/${id}/billing`, {
        method: 'PATCH',
        body: JSON.stringify({ billingDetails: patch })
      })
  });
};

export const useRegisterToMarketplace = (marketplace: MarketplaceName) => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, { payload: MarketplaceData; regToken: string }>({
    mutationFn: ({ payload, regToken }) => {
      if (!regToken) {
        throw new Error(
          `Registration Token Missing. Please go to ${marketplace} Marketplace and follow the instructions to set up your account!`
        );
      }

      return apiFetch(`${marketplace}/contact-us`, {
        method: 'POST',
        body: JSON.stringify({ ...payload, regToken })
      });
    }
  });
};

export const useUpdateSSOConfiguration = () => {
  const apiFetch = useApiFetch();

  return useMutation<EnsureSSOConfigurationResponse, ApiError, EnsureSSOConfigurationRequest>({
    mutationFn: (ensureSSOConfigurationRequest) => {
      return apiFetch(`v1/organizations/${ensureSSOConfigurationRequest.id}/sso/configuration`, {
        method: 'PUT',
        body: ensureSSOConfigurationRequest.toJsonString({ emitDefaultValues: true })
      }).then(EnsureSSOConfigurationResponse.fromJson);
    }
  });
};

export const useDeleteSSOConfiguration = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<DeleteSSOConfigurationRequest>>({
    mutationFn: ({ id }) =>
      apiFetch(`v1/organizations/${id}/sso/configuration`, {
        method: 'DELETE'
      })
  });
};

export const useUpdateOIDCMap = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<UpdateOIDCMapRequest>>({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/organizations/${id}/oidc-map`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
  });
};

export const useUpdateTeamOIDCMap = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, PlainMessage<UpdateTeamOIDCMapRequest>>({
    mutationFn: ({ organizationId, ...data }) =>
      apiFetch(`v1/organizations/${organizationId}/teams-oidc-map`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
  });
};

export const useUpdateSubscription = () => {
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, { id: string; plan: string; addons?: SubscriptionAddon[] }>(
    {
      mutationFn: ({ id, plan, addons }) =>
        apiFetch(`v1/organizations/${id}/billing/subscription`, {
          method: 'PATCH',
          body: JSON.stringify({ plan, addons })
        })
    }
  );
};

export const useGetCustomRoles = (
  filters: Omit<PlainMessage<ListCustomRolesRequest>, 'organizationId'>,
  opts?: { fetchAll?: boolean }
) => {
  filters.limit = filters?.limit || 10;
  filters.offset = filters?.offset || 0;

  const { currentOrg, currentOrgFeatureStatuses, permissionChecker } = useMainContext();
  const apiFetch = useApiFetch();

  const organizationId = currentOrg?.id;

  const getCustomRoles = (filters: Omit<PlainMessage<ListCustomRolesRequest>, 'organizationId'>) =>
    apiFetch(
      `v1/organizations/${organizationId}/custom-roles?limit=${filters?.limit}&offset=${filters?.offset}`
    )
      .then(ListCustomRolesResponse.fromJson)
      .then((res) => {
        const customRoles = res?.customRoles || [];
        const totalCount = res?.totalCount || 0;

        const customRolesWithTransformedPolicies: CustomRole[] = customRoles.map((customRole) => ({
          id: customRole.id,
          description: customRole.description,
          name: customRole.name,
          policy: RolePolicyTransformer.toProduct(customRole.policy)?.policy
        }));

        return {
          customRoles: customRolesWithTransformedPolicies,
          totalCount
        };
      });

  return useQuery({
    queryKey: queryKeys.organizations.customRoles(organizationId, filters).queryKey,
    queryFn: async () => {
      const initialData = await getCustomRoles(filters);

      if (!opts?.fetchAll) {
        return initialData;
      }

      let newCustomRoles = initialData.customRoles;

      while (newCustomRoles?.length < initialData?.totalCount) {
        const nextPageRoles = await getCustomRoles({
          limit: filters?.limit,
          offset: filters.offset + filters.limit
        });

        newCustomRoles = newCustomRoles.concat(nextPageRoles?.customRoles);
      }

      return {
        customRoles: newCustomRoles,
        totalCount: initialData?.totalCount
      };
    },
    placeholderData: (data) => {
      return { customRoles: data?.customRoles || [], totalCount: data?.totalCount || 0 };
    },
    enabled:
      currentOrgFeatureStatuses.customRoles === FeatureStatus.ENABLED &&
      permissionChecker.can({
        action: 'get',
        object: 'organization/custom-role',
        resource: '*'
      })
  });
};

export const useCreateCustomRoles = () => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const organizationId = currentOrg?.id;

  const queryClient = useQueryClient();

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<CreateCustomRoleRequest>, 'organizationId'>
  >({
    mutationFn: (data) =>
      apiFetch(`v1/organizations/${organizationId}/custom-roles`, {
        method: 'POST',
        body: JSON.stringify(data)
      }),
    onSuccess: (data: PlainMessage<CreateCustomRoleResponse>) => {
      if (!data.customRole) {
        return;
      }

      const newCustomRole: CustomRole = {
        ...data.customRole,
        policy: RolePolicyTransformer.toProduct(data?.customRole?.policy)?.policy
      };

      queryClient.setQueriesData(
        {
          queryKey: queryKeys.organizations.customRoles(organizationId).queryKey.filter(Boolean)
        },
        (oldData: { customRoles?: CustomRole[]; totalCount?: number }) => {
          return {
            ...oldData,
            customRoles: [newCustomRole, ...(oldData?.customRoles || [])]
          };
        }
      );
    }
  });
};

// Teams
export const useListTeams = () => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.teams(currentOrg.id).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams`)
        .then(ListTeamsResponse.fromJson)
        .then((res) => res.userTeams),
    staleTime: 500
  });
};

export const useCreateTeam = () => {
  const queryClient = useQueryClient();
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<
    CreateTeamResponse,
    ApiError,
    Omit<PlainMessage<CreateTeamRequest>, 'organizationId'>
  >({
    mutationFn: (data) =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams`, {
        method: 'POST',
        body: JSON.stringify(data)
      }).then(CreateTeamResponse.fromJson),
    onSuccess: (data) => {
      queryClient.setQueryData(
        queryKeys.organizations.teams(currentOrg.id).queryKey,
        (teams: UserTeam[]) => (Array.isArray(teams) ? [...teams, data.userTeam] : [data.userTeam])
      );
    }
  });
};

export const useEditCustomRole = () => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const organizationId = currentOrg?.id;

  const queryClient = useQueryClient();
  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<UpdateCustomRoleRequest>, 'organizationId'>
  >({
    mutationFn: ({ id, ...data }) =>
      apiFetch(`v1/organizations/${organizationId}/custom-roles/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(data)
      }),
    onSuccess: (data: PlainMessage<UpdateCustomRoleResponse>) => {
      if (!data.customRole) {
        return;
      }

      const existingCustomRole: CustomRole = {
        ...data.customRole,
        policy: RolePolicyTransformer.toProduct(data?.customRole?.policy)?.policy
      };

      queryClient.setQueriesData(
        {
          queryKey: queryKeys.organizations.customRoles(organizationId).queryKey.filter(Boolean)
        },
        (oldData: { customRoles?: CustomRole[]; totalCount?: number }) => {
          return {
            ...oldData,
            customRoles: oldData?.customRoles.map((customRole) => {
              if (customRole?.id === existingCustomRole?.id) {
                return existingCustomRole;
              }

              return customRole;
            })
          };
        }
      );
    }
  });
};

export const useUpdateTeam = () => {
  const queryClient = useQueryClient();
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<
    UpdateTeamResponse,
    ApiError,
    Omit<PlainMessage<UpdateTeamRequest>, 'organizationId'>
  >({
    mutationFn: (data) =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams/${data.name}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      }).then(UpdateTeamResponse.fromJson),
    onSuccess: (data) => {
      queryClient.setQueryData(
        queryKeys.organizations.teams(currentOrg.id).queryKey,
        (teams: UserTeam[]) =>
          teams?.map((item) => (item.team.name === data.userTeam.team.name ? data.userTeam : item))
      );
    }
  });
};

export const useDeleteCustomRole = () => {
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();
  const { currentOrg } = useMainContext();

  const organizationId = currentOrg?.id;

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<DeleteCustomRoleRequest>, 'organizationId'>
  >({
    mutationFn: ({ id }) =>
      apiFetch(`v1/organizations/${organizationId}/custom-roles/${id}`, {
        method: 'DELETE'
      }),
    onSuccess: (_, variables) => {
      queryClient.setQueriesData(
        {
          queryKey: queryKeys.organizations.customRoles(organizationId).queryKey.filter(Boolean)
        },
        (oldData: { customRoles?: CustomRole[]; totalCount?: number }) => {
          return {
            ...oldData,
            customRoles: oldData?.customRoles.filter(
              (customRole) => customRole?.id !== variables?.id
            )
          };
        }
      );
    }
  });
};

export const useDeleteTeam = () => {
  const queryClient = useQueryClient();
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError, Omit<PlainMessage<DeleteTeamRequest>, 'organizationId'>>({
    mutationFn: ({ name }) =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams/${name}`, {
        method: 'DELETE'
      }),
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        queryKeys.organizations.teams(currentOrg.id).queryKey,
        (data: UserTeam[]) => data?.filter((member) => member.team.name !== variables.name)
      );
    }
  });
};

export const useListTeamMembers = ({
  teamName
}: Omit<PlainMessage<ListTeamMembersRequest>, 'organizationId'>) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.teamMembers(currentOrg.id, teamName).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams/${teamName}/members`)
        .then(ListTeamMembersResponse.fromJson)
        .then((res) => res.teamMembers)
  });
};

export const useAddTeamMember = () => {
  const queryClient = useQueryClient();
  const { currentOrg, user } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<
    AddTeamMemberResponse,
    ApiError,
    Omit<PlainMessage<AddTeamMemberRequest>, 'organizationId'>
  >({
    mutationFn: ({ teamName, ...data }) =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams/${teamName}/members`, {
        method: 'POST',
        body: JSON.stringify(data)
      }).then(AddTeamMemberResponse.fromJson),
    onSuccess: (res, variables) => {
      // Update members list
      queryClient.setQueryData(
        queryKeys.organizations.teamMembers(currentOrg.id, variables.teamName).queryKey,
        (data: TeamMember[]) => (Array.isArray(data) ? [...data, res.teamMember] : [res.teamMember])
      );

      // Update the number of members in the teams list
      queryClient.setQueryData(
        queryKeys.organizations.teams(currentOrg.id).queryKey,
        (data: UserTeam[]) =>
          data?.map((item) =>
            item.team.name === variables.teamName
              ? {
                  ...item,
                  isMember: item.isMember || user.id === variables.userId,
                  team: {
                    ...item.team,
                    memberCount: BigInt(Number(item.team.memberCount) + 1)
                  }
                }
              : item
          )
      );
    }
  });
};

export const useDeleteTeamMember = () => {
  const queryClient = useQueryClient();
  const { currentOrg, user } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<DeleteTeamMemberRequest>, 'organizationId'>
  >({
    mutationFn: ({ teamName, id }) =>
      apiFetch(`v1/orgs/${currentOrg.id}/teams/${teamName}/members/${id}`, {
        method: 'DELETE'
      }),
    onSuccess: (_, variables) => {
      // Update members list
      queryClient.setQueryData(
        queryKeys.organizations.teamMembers(currentOrg.id, variables.teamName).queryKey,
        (data: TeamMember[]) => data?.filter((member) => member.id !== variables.id)
      );

      // Update the number of members in the teams list
      queryClient.setQueryData(
        queryKeys.organizations.teams(currentOrg.id).queryKey,
        (data: UserTeam[]) =>
          data?.map((item) =>
            item.team.name === variables.teamName
              ? {
                  ...item,
                  isMember: item.isMember && user.id !== variables.id,
                  team: {
                    ...item.team,
                    memberCount: BigInt(Number(item.team.memberCount) - 1)
                  }
                }
              : item
          )
      );
    }
  });
};

export const useGetCheckoutLink = (onSuccess?: (url: string) => void) => {
  const notification = useNotificationContext();
  const apiFetch = useApiFetch();

  return useMutation<BillingCheckoutResponse, ApiError, PlainMessage<BillingCheckoutRequest>>({
    mutationFn: (data) =>
      apiFetch(`v1/organizations/${data.id}/checkout`, {
        method: 'POST',
        body: JSON.stringify(data)
      }),
    onSuccess: (res) => {
      if (onSuccess) {
        onSuccess(res.url);
      }
      return res.url;
    },
    onError: () => {
      notification.error({ message: 'Could not open checkout page' });
    }
  });
};

export const useCancelSubscription = () => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useMutation<unknown, ApiError>({
    mutationFn: () =>
      apiFetch(`v1/organizations/${currentOrg?.id}/billing/subscription/cancel`, {
        method: 'POST'
      })
  });
};

export const useGetKubevisionUsageQuery = (
  data: Omit<PlainMessage<GetKubeVisionUsageRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubeVisionUsageResponse, readonly string[]>
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.kubevisionUsage(currentOrg.id, params).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/k8s/usage?${params}`).then(
        GetKubeVisionUsageResponse.fromJson
      ),
    ...opts
  });
};

// Might be used before main context initializes
export const useGetOrganizationPermissionsQuery = (
  { organizationId }: PlainMessage<GetKubeVisionUsageRequest>,
  opts?: UseQueryOptionsOverride<GetOrganizationPermissionsResponse, readonly string[]>
) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.permissions(organizationId).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${organizationId}/permissions`).then(
        GetOrganizationPermissionsResponse.fromJson
      ),
    ...opts
  });
};

export const useListArgoCDInstancesQuota = (
  opts?: UseQueryOptionsOverride<InstanceQuota[], readonly string[]>
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.listArgoQuota(currentOrg.id).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/instances/quota`)
        .then(ListArgocdInstancesQuotaResponse.fromJson)
        .then((data) => data.instances),
    ...opts
  });
};

export const useListKargoInstancesQuota = (
  opts?: UseQueryOptionsOverride<KargoInstanceQuota[], readonly string[]>
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.listKargoQuota(currentOrg.id).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/kargo-instances/quota`)
        .then(ListKargoInstancesQuotaResponse.fromJson)
        .then((data) => data.instances),
    ...opts
  });
};

export const useUpdateArgoCDInstancesQuota = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    UpdateArgocdInstancesQuotaResponse,
    ApiError,
    Omit<PlainMessage<UpdateArgocdInstancesQuotaRequest>, 'organizationId'>
  >({
    mutationFn: (body) =>
      apiFetch(`v1/orgs/${currentOrg.id}/instances/quota`, {
        method: 'PUT',
        body: JSON.stringify(body)
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations.listArgoQuota._def });
    }
  });
};

export const useUpdateKargoInstancesQuota = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    UpdateKargoInstancesQuotaResponse,
    ApiError,
    Omit<PlainMessage<UpdateKargoInstancesQuotaRequest>, 'organizationId'>
  >({
    mutationFn: (body) =>
      apiFetch(`v1/orgs/${currentOrg.id}/kargo-instances/quota`, {
        method: 'PUT',
        body: JSON.stringify(body)
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations.listKargoQuota._def });
    }
  });
};
