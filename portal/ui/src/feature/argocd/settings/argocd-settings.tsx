import { Space } from 'antd';
import React from 'react';
import { Navigate, Route, Routes, generatePath } from 'react-router-dom';
import semver from 'semver';

import { paths } from '@ui/config/paths';
import { ReconciliationStatus } from '@ui/feature/common/instance/reconciliation-status';
import { SettingsLink } from '@ui/feature/common/settings/settings-link';
import { SettingsNavHeader } from '@ui/feature/common/settings/settings-nav-header';
import { UnsupportedVersionBanner } from '@ui/feature/common/unsupported-version-banner';
import { Instance } from '@ui/lib/apiclient';
import {
  useGetArgoCDInstanceByName,
  useWatchArgoCDInstances
} from '@ui/lib/apiclient/argocd/argocd-queries';
import { Loading, PageContent } from '@ui/lib/components';
import { PageTitle } from '@ui/lib/components/page-title';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';

import { flatSettingsConfig, settingsConfig } from './views';

export const ArgoCDSettings = () => {
  const { name, org } = useRequiredParams<'name' | 'org'>(['name', 'org']);
  const { currentOrgFeatureStatuses } = useMainContext();
  const settingsViews = settingsConfig(currentOrgFeatureStatuses);

  const { data: instance, isLoading } = useGetArgoCDInstanceByName({ name });
  useWatchArgoCDInstances({ instanceId: instance?.id, workspaceId: org }, { enabled: !!instance });

  const instanceVersion = semver.parse(instance?.version);

  return (
    <PageContent
      breadcrumbs={[
        { label: 'Argo CD', path: paths.instances },
        {
          label: (
            <Space>
              {name}
              <ReconciliationStatus instance={instance} />
            </Space>
          ),
          path: generatePath(paths.instance, { org, name }),
          loading: isLoading
        },
        {
          label: 'Settings',
          path: generatePath(paths.argoCDInstanceSettings, { name, org })
        }
      ]}
    >
      <PageTitle>Argo CD Settings</PageTitle>

      <UnsupportedVersionBanner className='mb-10 -mt-2' instance={instance} orgName={org} />
      {isLoading && <Loading />}
      {instance && (
        <div className='flex gap-8 -mt-4' data-qe-id='settings-page'>
          <div className='sticky pt-2 flex flex-col gap-1' style={{ flex: '0 0 13.2rem' }}>
            {Object.values(settingsViews).map((item, key) => (
              <React.Fragment key={item.label + key}>
                {item.label && <SettingsNavHeader>{item.label}</SettingsNavHeader>}
                {Object.entries(item.views)
                  // Filter out hidden views
                  .filter(([, view]) => !view.isHidden)
                  .map(([key, view]) => (
                    <SettingsLink
                      key={key}
                      path={key}
                      icon={view.icon}
                      label={view.label}
                      isBeta={view.isBeta}
                      isOnlyAvailableInPro={view.isPaywalled}
                      paywallTooltip={view.paywallTooltip}
                      isUnsupported={
                        semver.valid(view.minVersion) &&
                        instanceVersion &&
                        semver.lt(instanceVersion, view.minVersion)
                      }
                    />
                  ))}
              </React.Fragment>
            ))}
          </div>
          <div className='flex-1' data-qe-id='settings-content'>
            <Routes>
              <Route
                index
                element={<Navigate to={Object.keys(settingsViews[0].views)[0]} replace={true} />}
              />
              {Object.entries(flatSettingsConfig(currentOrgFeatureStatuses))
                // Filter out hidden views and unsupported versions
                .filter(
                  ([, view]) =>
                    !view.isHidden &&
                    !(
                      semver.valid(view.minVersion) &&
                      instanceVersion &&
                      semver.lt(instanceVersion, view.minVersion)
                    )
                )
                .map(([key, value]) => (
                  <Route
                    key={key}
                    path={key}
                    element={
                      <value.component
                        instance={instance.toJson({ emitDefaultValues: true }) as Instance}
                      />
                    }
                  />
                ))}
              <Route path='*' element='Settings not found' />
            </Routes>
          </div>
        </div>
      )}
    </PageContent>
  );
};
