import { faH<PERSON><PERSON><PERSON><PERSON><PERSON>, faPuz<PERSON><PERSON><PERSON><PERSON>, faServer, faStore } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Divider, Flex, Space, Tooltip, Typography } from 'antd';
import { generatePath, Link } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { Instance } from '@ui/lib/apiclient';
import { Addon } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { StatusCode } from '@ui/lib/apiclient/types/status/reconciliation/v1/reconciliation_pb';
import { ConditionalWrapper } from '@ui/lib/components/conditional-wrapper';
import { useModal } from '@ui/lib/hooks';

import { AddonMarketplaceReinstallModal } from './addon-marketplace/components/reinstall/modal';
import { AddonDeleteModal } from './clusters-addon-details/actions/addon-delete-modal';
import { AddonStatus } from './components/addon-status';
import { ClusterAddonTileStatus } from './components/cluster-addon-tile-status';
import { getMarketplaceAddonStatus } from './utils';

type Props = {
  addon: Addon;
  instance: Instance;
};

export const ClustersAddonTile = ({ addon, instance }: Props) => {
  const {
    isMarketplaceAddon,
    isMarketplaceAddonInstalling,
    isMarketplaceAddonUninstalled,
    marketplaceAddonLastEvent,
    isMarketplaceAddonLastEventFailed
  } = getMarketplaceAddonStatus(addon);

  const addonMarketplaceDeleteModal = useModal((modalProps) => (
    <AddonDeleteModal {...modalProps} addon={addon} />
  ));
  const addonMarketplaceReinstallModal = useModal((modalProps) => (
    <AddonMarketplaceReinstallModal {...modalProps} instance={instance} addon={addon} />
  ));

  return (
    <ConditionalWrapper
      condition={isMarketplaceAddonInstalling || isMarketplaceAddonUninstalled}
      WrapperTrue={({ children }) => (
        <div
          className='flex hover:bg-gray-50 transition-colors'
          style={{ color: 'inherit !important' }}
        >
          {children}
        </div>
      )}
      WrapperFalse={({ children }) => (
        <Link
          to={generatePath(paths.clustersAddonsManifestSource, {
            organizationId: addon.organizationId,
            instanceName: instance.name,
            id: addon.id
          })}
          className='flex hover:bg-gray-50 transition-colors'
          style={{ color: 'inherit !important' }}
        >
          {children}
        </Link>
      )}
    >
      <div
        className='rounded-md border-gray-100 border p-5 pb-4'
        key={addon.id}
        style={{
          flex: '1',
          flexDirection: 'column',
          display: 'flex',
          boxShadow: '0 2px 6px 0 rgba(0,0,0,0.16),0 0 0 1px rgba(0,0,0,0.1)'
        }}
      >
        <Space size={12} className='items-start flex-1'>
          <FontAwesomeIcon icon={faPuzzlePiece} size='lg' className='mt-1.5' />
          <Space direction='vertical' size={0}>
            <Typography.Title style={{ margin: '6px 0 4px', fontSize: '18px', lineHeight: 1.2 }}>
              {addon.spec.name}
            </Typography.Title>
            <ClusterAddonTileStatus addon={addon} />
          </Space>
        </Space>
        <Divider className='mt-5 mb-3' />
        <Flex className='w-full h-8 text-gray-500' justify='space-between' align='center' gap={8}>
          <Space>
            {isMarketplaceAddon && (
              <Tooltip title='Marketplace Addon' placement='bottom'>
                <FontAwesomeIcon icon={faStore} />
              </Tooltip>
            )}
            <Tooltip title='Clusters' placement='bottom'>
              <Space size={8}>
                <FontAwesomeIcon icon={faServer} />
                <Typography.Text className='text-gray-500'>
                  {addon.status.clusterCount}
                </Typography.Text>
              </Space>
            </Tooltip>
            {addon.status?.reconciliationStatus?.code === StatusCode.FAILED && (
              <Tooltip
                title={
                  <div>
                    <div>Last failed event:</div>
                    <div>{addon.status?.reconciliationStatus?.message}</div>
                  </div>
                }
                placement='bottom'
              >
                <FontAwesomeIcon className='text-orange-500' icon={faHeartBroken} size='lg' />
              </Tooltip>
            )}
            {isMarketplaceAddon &&
              (isMarketplaceAddonInstalling || isMarketplaceAddonUninstalled) &&
              isMarketplaceAddonLastEventFailed && (
                <Tooltip
                  title={
                    <div>
                      <div>Last failed event:</div>
                      <div>{marketplaceAddonLastEvent.message}</div>
                    </div>
                  }
                  placement='bottom'
                >
                  <FontAwesomeIcon className='text-orange-500' icon={faHeartBroken} size='lg' />
                </Tooltip>
              )}
          </Space>
          {isMarketplaceAddonInstalling || isMarketplaceAddonUninstalled ? (
            <Space>
              <Button
                type='default'
                loading={!!addon.deleteTime}
                disabled={isMarketplaceAddonInstalling}
                onClick={() => addonMarketplaceDeleteModal.show()}
              >
                Delete
              </Button>
              <Button
                type='primary'
                loading={isMarketplaceAddonInstalling}
                disabled={!!addon.deleteTime}
                onClick={() => addonMarketplaceReinstallModal.show()}
              >
                Reinstall
              </Button>
            </Space>
          ) : (
            <AddonStatus addon={addon} />
          )}
        </Flex>
      </div>
    </ConditionalWrapper>
  );
};
