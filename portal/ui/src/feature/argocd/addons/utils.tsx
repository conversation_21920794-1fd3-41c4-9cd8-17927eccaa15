import { Addon } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { SECOND } from '@ui/lib/components/metrics/utils';

export const getMarketplaceAddonStatus = (addon: Addon) => {
  const isMarketplaceAddon = addon.addonMarketplaceInstallsId;
  const isMarketplaceAddonProcessing = addon.addonMarketplaceInstallStatusInfo.processing;
  const marketplaceAddonLastEvent = addon.addonMarketplaceInstallStatusInfo?.eventList?.[0];
  // Marketplace Addon event types: InstallSucceeded/UpdateSucceeded/InstallFailed/UpdateFailed
  const isMarketplaceAddonLastEventFailed = (marketplaceAddonLastEvent?.type || '').includes(
    'Failed'
  );

  const isMarketplaceAddonInstalling =
    isMarketplaceAddon &&
    // There is a delay between updating the git repo and addon repos refresh
    // In this time processing is false, but addon still doesn't have ID and isn't functional
    // Because of that we add some delay to prolong the installing status
    // It will be cleared when ID appears
    (isMarketplaceAddonProcessing ||
      (!addon.id &&
        marketplaceAddonLastEvent.type === 'InstallSucceeded' &&
        marketplaceAddonLastEvent.time.toDate().getTime() + 30 * SECOND > Date.now()));
  const isMarketplaceAddonUninstalled =
    isMarketplaceAddon &&
    !isMarketplaceAddonInstalling &&
    !addon.id &&
    !addon?.spec?.defaultManifest?.path;

  return {
    isMarketplaceAddon,
    isMarketplaceAddonInstalling,
    isMarketplaceAddonUninstalled,
    marketplaceAddonLastEvent,
    isMarketplaceAddonLastEventFailed
  };
};
