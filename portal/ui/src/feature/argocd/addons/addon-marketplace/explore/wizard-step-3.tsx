import { faCircleExclamation } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Flex, Input, Select, Tooltip, Typography } from 'antd';
import { useEffect, useMemo } from 'react';
import { DeepRequired, useForm, UseFormSetValue } from 'react-hook-form';
import { z } from 'zod';

import { Instance } from '@ui/lib/apiclient';
import { useArgoCDInstanceAddonRepos } from '@ui/lib/apiclient/argocd/argocd-addons-queries';
import { StatusCode } from '@ui/lib/apiclient/types/status/reconciliation/v1/reconciliation_pb';
import { FormSection } from '@ui/lib/components/forms';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { TagsInput } from '@ui/lib/components/tags-input/tags-input';
import { zodValidators } from '@ui/lib/utils';

export type WizardStep3Props = {
  formId: string;
  instance: Instance;
  onChangeValidity?: (isFormValid: boolean) => void;
  onSubmit?: (formData: FormSchemaType) => void;
};
export const WizardStep3 = ({ formId, instance, onChangeValidity, onSubmit }: WizardStep3Props) => {
  const { control, handleSubmit, formState, watch, setValue } = useForm<
    FormSchemaType,
    unknown,
    FormSchemaType
  >({
    mode: 'onChange',
    defaultValues: {
      repository: {
        url: '',
        branch: ''
      },
      helmChartMetadata: {
        name: '',
        version: '1.0.0',
        description: ''
      },
      overrides: {}
    },
    resolver: zodResolver(formSchema)
  });
  useEffect(() => {
    onChangeValidity?.(formState.isValid);
  }, [onChangeValidity, formState.isValid]);
  const repoUrl = watch('repository.url');

  const { repositoryUrlOptions, repositoryBranchesMap } = useRepositoryOptions({
    instance,
    repoUrl,
    setValue
  });

  return (
    <form
      id={formId}
      className='m-auto px-6 pt-4 max-w-screen-xl'
      onSubmit={handleSubmit(onSubmit)}
    >
      <FormSection title='Repository'>
        <Flex gap={8}>
          <FieldContainer
            label='Installation repository URL'
            name='repository.url'
            wrapperClassName='flex-[2_1]'
            control={control}
          >
            {({ field, ariaProps }) => (
              <Select
                {...field}
                {...ariaProps}
                className='w-full '
                placeholder='https://github.com/akuityio/addons'
                options={repositoryUrlOptions}
              />
            )}
          </FieldContainer>
          <FieldContainer
            label='Installation repository branch'
            name='repository.branch'
            wrapperClassName='flex-[1_1]'
            control={control}
          >
            {({ field, ariaProps }) => (
              <Select
                {...field}
                {...ariaProps}
                className='w-full'
                disabled={!repoUrl}
                placeholder='main'
                options={repositoryBranchesMap.get(repoUrl)}
                optionRender={({ data: { label, disabled, errorMessage } }) => (
                  <>
                    {disabled && (
                      <Tooltip title={errorMessage}>
                        <Typography.Text className='mr-2' type='danger'>
                          <FontAwesomeIcon icon={faCircleExclamation} />
                        </Typography.Text>
                      </Tooltip>
                    )}
                    {label}
                  </>
                )}
              />
            )}
          </FieldContainer>
        </Flex>
      </FormSection>
      <FormSection title='Helm chart metadata'>
        <Flex gap={8}>
          <FieldContainer
            control={control}
            name='helmChartMetadata.name'
            label='Name'
            wrapperClassName='flex-[2_1]'
          >
            {({ field, ariaProps }) => (
              <Input {...field} {...ariaProps} placeholder='custom-addon' />
            )}
          </FieldContainer>
          <FieldContainer
            control={control}
            name='helmChartMetadata.version'
            label='Version'
            wrapperClassName='flex-[1_1]'
          >
            {({ field, ariaProps }) => <Input {...field} {...ariaProps} placeholder='1.0.0' />}
          </FieldContainer>
        </Flex>
        <FieldContainer control={control} name='helmChartMetadata.description' label='Description'>
          {({ field, ariaProps }) => (
            <Input.TextArea
              {...field}
              {...ariaProps}
              placeholder='Custom addon from marketplace'
              // autoSize
            />
          )}
        </FieldContainer>
      </FormSection>
      <FormSection title='Additional overrides'>
        <Flex gap={8}>
          <FieldContainer control={control} name='overrides.envs' label='Env'>
            {({ field: { value, onChange } }) => (
              <TagsInput {...{ value, onChange }} placeholder='dev, staging' />
            )}
          </FieldContainer>
          <FieldContainer control={control} name='overrides.clusters' label='Cluster'>
            {({ field: { value, onChange } }) => (
              <TagsInput {...{ value, onChange }} placeholder='cluster-1, cluster-2' />
            )}
          </FieldContainer>
        </Flex>
      </FormSection>
    </form>
  );
};

type SelectOption = { value: string; label: string; disabled?: boolean; errorMessage?: string };

const formSchema = z.object({
  repository: z.object({
    url: zodValidators.requiredString,
    branch: zodValidators.requiredString
  }),
  helmChartMetadata: z.object({
    name: zodValidators.requiredString,
    version: zodValidators.requiredSemver,
    description: z.string()
  }),
  overrides: z
    .object({
      envs: z.array(z.string()).optional(),
      clusters: z.array(z.string()).optional()
    })
    .optional()
});
type FormSchemaType = DeepRequired<z.infer<typeof formSchema>>;

type UseRepositoryOptionsProps = {
  instance: Instance;
  repoUrl: string;
  setValue: UseFormSetValue<FormSchemaType>;
};
const useRepositoryOptions = ({ instance, repoUrl, setValue }: UseRepositoryOptionsProps) => {
  const { data = [] } = useArgoCDInstanceAddonRepos({
    instanceId: instance.id,
    workspaceId: instance.workspaceId
  });
  // UX looks bad when combining repository URL and branch, so it's split into separate selects
  // This requires some data manipulation to make sure that all URLs get their specific branches
  // Grab unique repository URL with healthy state
  const repositoryUrlOptions = useMemo(() => {
    const temp = new Set<string>();
    data.forEach((item) => {
      if (item.spec?.repoUrl) {
        temp.add(item.spec.repoUrl);
      }
    });
    return Array.from(temp.values()).map((item) => ({ value: item, label: item }));
  }, [data]);
  // Map repository branches to repository URLs
  const repositoryBranchesMap = useMemo(() => {
    const temp = new Map<string, SelectOption[]>();
    data.forEach((item) => {
      if (!item.spec?.repoUrl || !item.spec?.revision) {
        return;
      }

      const key = item.spec.repoUrl;
      const value = item.spec.revision;
      const disabled = item.status?.reconciliationStatus?.code !== StatusCode.SUCCESSFUL;
      const errorMessage = item.status?.reconciliationStatus?.message || '';
      const entry = { value, label: value, disabled, errorMessage };

      const prevVal = temp.get(key);
      if (prevVal) {
        prevVal.push(entry);
        prevVal.sort((a, b) => a.value.localeCompare(b.value));
        temp.set(key, prevVal);
      } else {
        temp.set(key, [entry]);
      }
    });
    return temp;
  }, [data]);
  useEffect(() => {
    if (repoUrl) {
      setValue(
        'repository.branch',
        repositoryBranchesMap.get(repoUrl).find((branch) => !branch.disabled)?.value,
        { shouldDirty: true, shouldTouch: true, shouldValidate: true }
      );
    }
  }, [repoUrl, repositoryBranchesMap]);

  return {
    repositoryUrlOptions,
    repositoryBranchesMap
  };
};
