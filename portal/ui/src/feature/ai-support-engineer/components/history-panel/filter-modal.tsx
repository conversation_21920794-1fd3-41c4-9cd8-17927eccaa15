import { Modal, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import { IncidentStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { useSpotlightSearch } from '@ui/lib/hooks/use-spotlight-search';

export type Filter = {
  incidentOnly: boolean;
  incidentStatus: IncidentStatus;
  application: string;
  namespace: string;
  cluster: string;
};

export type HistoryPanelFilter = Filter & {
  titleContains: string;
  offset: number;
  limit: number;
};

type FilterModalProps = {
  isFilterModalVisible: boolean;
  setIsFilterModalVisible: (isFilterModalVisible: boolean) => void;
  initialFilter: Filter;
  onFilterChange: (filter: Filter) => void;
};

export const FilterModal = ({
  isFilterModalVisible,
  setIsFilterModalVisible,
  initialFilter,
  onFilterChange
}: FilterModalProps) => {
  const { organizationId, instanceId, enabledClustersInfo } = useAISupportEngineerContext();
  const [filter, setFilter] = useState(initialFilter);
  const { resources } = useSpotlightSearch(organizationId, instanceId, true);

  const applications = useMemo(() => {
    const applications: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Application') {
        applications.push(resource.name);
      }
    });
    return applications;
  }, [resources]);

  const namespaces = useMemo(() => {
    const namespaces: { namespace: string; clusterId: string }[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Namespace') {
        namespaces.push({ namespace: resource.name, clusterId: resource.clusterId });
      }
    });
    return namespaces;
  }, [resources]);

  useEffect(() => {
    setFilter(initialFilter);
  }, [initialFilter]);

  return (
    <Modal
      title='Conversation Filters'
      open={isFilterModalVisible}
      onOk={() => {
        onFilterChange(filter);
        setIsFilterModalVisible(false);
      }}
      onCancel={() => setIsFilterModalVisible(false)}
    >
      <div className='flex flex-row flex-wrap items-center'>
        <div className='w-[35%] py-1'>
          <span className='text-sm'>Conversation Type:</span>
        </div>
        <div className='w-[65%] py-1'>
          <Select
            title='Status'
            size='middle'
            className='w-auto'
            style={{ minWidth: 200 }}
            options={[
              { label: 'All', value: false },
              { label: 'Incident Only', value: true }
            ]}
            value={filter.incidentOnly}
            onChange={(value) => {
              setFilter({ ...filter, incidentOnly: value });
            }}
            showSearch
          />
        </div>
        <div className='w-[35%] py-1'>
          <span className='text-sm'>Status:</span>
        </div>
        <div className='w-[65%] py-1'>
          <Select
            title='Status'
            size='middle'
            className='w-auto'
            style={{ minWidth: 200 }}
            options={[
              { label: 'All', value: IncidentStatus.UNSPECIFIED },
              { label: 'Resolved', value: IncidentStatus.RESOLVED },
              { label: 'Unresolved', value: IncidentStatus.UNRESOLVED }
            ]}
            value={filter.incidentStatus}
            onChange={(value) => {
              setFilter({ ...filter, incidentStatus: value });
            }}
            showSearch
          />
        </div>
        <div className='w-[35%] py-1'>
          <span className='text-sm'>Application:</span>
        </div>
        <div className='w-[65%] py-1'>
          <Select
            title='Application'
            size='middle'
            className='w-auto'
            style={{ minWidth: 200 }}
            options={[
              { label: 'All', value: '' },
              ...applications.map((application) => ({
                label: application,
                value: application
              }))
            ]}
            value={filter.application}
            onChange={(value) => {
              setFilter({ ...filter, application: value });
            }}
            showSearch
          />
        </div>
        <div className='w-[35%] py-1'>
          <span className='text-sm'>Namespace:</span>
        </div>
        <div className='w-[65%] py-1'>
          <Select
            title='Namespace'
            size='middle'
            className='w-auto'
            style={{ minWidth: 200 }}
            options={[
              { label: 'All', value: '' },
              ...namespaces.map((namespace) => ({
                label: (
                  <div className='flex flex-col'>
                    <div>{namespace.namespace}</div>
                    <div className='text-xs'>
                      <span className='text-xs text-gray-400'>Cluster:</span>{' '}
                      <span className='text-xs text-[#64748b]'>
                        {enabledClustersInfo.clusterName(namespace.clusterId)}
                      </span>
                    </div>
                  </div>
                ),
                value: namespace.clusterId + '/' + namespace.namespace
              }))
            ]}
            value={filter.namespace}
            onChange={(value) => {
              if (value) {
                const [clusterId, namespace] = value.split('/');
                const clusterName = enabledClustersInfo.clusterName(clusterId);
                setFilter({ ...filter, namespace: namespace, cluster: clusterName });
              } else {
                setFilter({ ...filter, namespace: '', cluster: '' });
              }
            }}
            showSearch
          />
        </div>
      </div>
    </Modal>
  );
};
