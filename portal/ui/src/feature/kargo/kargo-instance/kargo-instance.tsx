import {
  faCog,
  faServer,
  faShoe<PERSON><PERSON>ts,
  faChart<PERSON>ie,
  IconDefinition
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Space, Tabs } from 'antd';
import React from 'react';
import {
  Navigate,
  Route,
  Routes,
  generatePath,
  matchPath,
  useLocation,
  useNavigate,
  useSearchParams
} from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { DeletingInstanceInfo } from '@ui/feature/common/instance/deleting-instance-info';
import { ReconciliationStatus } from '@ui/feature/common/instance/reconciliation-status';
import { UnsupportedVersionBanner } from '@ui/feature/common/unsupported-version-banner';
import {
  useGetKargoInstance,
  useWatchKargoAgents,
  useWatchKargoInstances
} from '@ui/lib/apiclient/kargo/kargo-queries';
import { KargoInstance as KargoInstanceType } from '@ui/lib/apiclient/kargo/v1/kargo_pb';
import { Loading, PageContent } from '@ui/lib/components';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';

import { AgentsManagement } from './agents-management/agents-management';
import { KargoAuditLog } from './kargo-audit-log';
import { kargoInstancePaths } from './kargo-instance-paths';
import { KargoSummary } from './kargo-summary/kargo-summary';

const kargoInstanceViews: {
  [key: string]: {
    path: string;
    name: string;
    ico: IconDefinition;
    component: (props: { id: string; instance?: KargoInstanceType }) => JSX.Element;
  };
} = {
  summary: {
    path: kargoInstancePaths.summary,
    name: 'Summary',
    ico: faChartPie,
    component: (props: { id: string; instance?: KargoInstanceType }) => (
      <KargoSummary instance={props.instance!} />
    )
  },
  agents: {
    path: kargoInstancePaths.agents,
    name: 'Agents',
    ico: faServer,
    component: () => <AgentsManagement />
  },
  audit: {
    path: kargoInstancePaths.audit,
    name: 'Audit Log',
    ico: faShoePrints,
    component: () => <KargoAuditLog />
  }
} as const;

export const KargoInstance = () => {
  const { name } = useRequiredParams<'name'>(['name']);
  const { currentOrg, permissionChecker } = useMainContext();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const { data, isLoading, error } = useGetKargoInstance({
    name,
    organizationId: currentOrg.id,
    workspaceId: searchParams.get('workspaceId') || ''
  });

  const canGetAgents = permissionChecker.can({
    action: 'get',
    object: 'workspace/kargo-instance/agents',
    resource: `${data?.workspaceId}/${data?.id}`
  });

  useWatchKargoInstances(
    { instanceId: data?.id, workspaceId: searchParams.get('workspaceId') || '' },
    { instanceName: data?.name, enabled: !!data }
  );
  useWatchKargoAgents(
    { instanceId: data?.id || '', workspaceId: searchParams.get('workspaceId') || '' },
    { enabled: !!data && canGetAgents }
  );

  const location = useLocation();

  const currentTabPath = React.useMemo(
    () =>
      Object.values(kargoInstanceViews).find((item) =>
        matchPath(paths.kargoInstance.replace('*', item.path), location.pathname)
      )?.path,
    [location]
  );

  if (isLoading) {
    return <Loading />;
  }

  if (error || !data) {
    return <Navigate to={paths.kargoInstances} replace />;
  }

  // TODO: use React.memo
  if (!canGetAgents) {
    delete kargoInstanceViews.agents;
  }

  return (
    <PageContent
      breadcrumbs={[
        { label: 'Kargo', path: paths.kargoInstances },
        {
          label: (
            <Space>
              {name}
              <ReconciliationStatus instance={data} />
            </Space>
          ),
          path: generatePath(paths.kargoInstance, { name }),
          loading: isLoading
        }
      ]}
    >
      <UnsupportedVersionBanner instance={data} orgName={currentOrg.name} instanceType='kargo' />
      <DeletingInstanceInfo
        deleteTime={data?.deleteTime?.toDate().toDateString()}
        className='mb-8 -mt-2'
      />
      <Tabs
        destroyOnHidden
        className='-mt-6'
        activeKey={currentTabPath}
        onChange={(key) => navigate(generatePath(key, { name }))}
        tabBarExtraContent={
          <Button
            icon={<FontAwesomeIcon icon={faCog} />}
            type='text'
            onClick={() => navigate(generatePath(paths.kargoInstanceSettings, { name }))}
            data-qe-id='settings-btn'
            size='small'
          >
            Settings
          </Button>
        }
        items={Object.values(kargoInstanceViews).map((i) => ({
          key: i.path,
          label: (
            <>
              <FontAwesomeIcon icon={i.ico} /> {i.name}
            </>
          )
        }))}
      />
      <Routes>
        <Route
          index
          element={
            <Navigate
              to={kargoInstanceViews[Object.keys(kargoInstanceViews)[0]].path}
              replace={true}
            />
          }
        />
        {Object.values(kargoInstanceViews).map((t) => (
          <Route
            key={t.path}
            path={t.path}
            element={<t.component id={data?.id} instance={data} />}
          />
        ))}
      </Routes>
    </PageContent>
  );
};
