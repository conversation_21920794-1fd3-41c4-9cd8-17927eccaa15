import { faCog, faD<PERSON><PERSON><PERSON>, faIdBadge, faServer, faUsers } from '@fortawesome/free-solid-svg-icons';
import { faPuzzlePiece } from '@fortawesome/free-solid-svg-icons/faPuzzlePiece';
import { Space } from 'antd';
import { useMemo } from 'react';
import { Navigate, Route, Routes, generatePath } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { ReconciliationStatus } from '@ui/feature/common/instance/reconciliation-status';
import { SettingsLink } from '@ui/feature/common/settings/settings-link';
import { SettingsNavHeader } from '@ui/feature/common/settings/settings-nav-header';
import { UnsupportedVersionBanner } from '@ui/feature/common/unsupported-version-banner';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { PageContent } from '@ui/lib/components';
import { PageTitle } from '@ui/lib/components/page-title';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';

import { KargoSettingsContext } from './context/kargo-settings-context';
import { KargoSettingsContextProvider } from './context/kargo-settings-context-provider';
import { AccountSettings } from './views/access/accounts-settings';
import { ExternalAccessSettings } from './views/access/external-access';
import { OIDCConfigSettings } from './views/access/oidc/oidc-config-settings';
import { AgentDefaultSettings } from './views/advanced/agent-default-settings';
import { Credentials } from './views/advanced/credentials';
import { ExtensionsSettings } from './views/extensions/extensions';
import { GeneralSettings } from './views/general/general-settings';

export const KargoInstanceSettings = () => {
  const { currentOrgFeatureStatuses } = useMainContext();
  const { name } = useRequiredParams<'name'>(['name']);

  const kargoSettingsViews = useMemo(
    () => ({
      general: {
        path: 'general',
        label: 'General',
        icon: faCog,
        component: GeneralSettings,
        hidden: false
      },
      extensions: {
        path: 'extensions',
        label: 'Extensions',
        icon: faPuzzlePiece,
        component: ExtensionsSettings,
        // TODO(yiwei): once we release kargo enterprise, we should make it visible and behind paywall
        hidden: currentOrgFeatureStatuses?.kargoEnterprise !== FeatureStatus.ENABLED
      },
      access: {
        path: 'system-accounts',
        label: 'System Accounts',
        icon: faUsers,
        component: AccountSettings,
        hidden: false
      },
      oidc: {
        path: 'oidc-config',
        label: 'OIDC Config',
        icon: faIdBadge,
        component: OIDCConfigSettings,
        hidden: false
      },
      externalAccess: {
        path: 'external-access',
        label: 'External Access',
        icon: faDoorOpen,
        component: ExternalAccessSettings,
        hidden: false
      },
      agentDefaultSettings: {
        path: 'agent-default-settings',
        label: 'Agent Default Settings',
        icon: faServer,
        component: AgentDefaultSettings,
        hidden: false
      },
      credentialsSettings: {
        path: 'credentials',
        label: 'Credentials',
        icon: faIdBadge,
        component: Credentials,
        hidden: false
      }
    }),
    [currentOrgFeatureStatuses]
  );

  return (
    <KargoSettingsContextProvider>
      <KargoSettingsContext.Consumer>
        {({ instance }) => (
          <PageContent
            breadcrumbs={[
              { label: 'Kargo', path: paths.kargoInstances },
              {
                label: (
                  <Space>
                    {name}
                    <ReconciliationStatus instance={instance} />
                  </Space>
                ),
                path: generatePath(paths.kargoInstance, { name })
              },
              {
                label: 'Settings',
                path: generatePath(paths.kargoInstanceSettings, { name })
              }
            ]}
          >
            <UnsupportedVersionBanner
              className='mb-10 -mt-2'
              instance={instance}
              orgName={name}
              instanceType='kargo'
            />
            <PageTitle>Kargo Settings</PageTitle>
            <div className='flex gap-8 -mt-4' data-qe-id='settings-page'>
              <div className='sticky pt-2 flex flex-col gap-2' style={{ flex: '0 0 12rem' }}>
                <SettingsLink {...kargoSettingsViews.general} />
                {currentOrgFeatureStatuses?.kargoEnterprise === FeatureStatus.ENABLED && (
                  <SettingsLink {...kargoSettingsViews.extensions} />
                )}
                <SettingsNavHeader>Access</SettingsNavHeader>
                <SettingsLink {...kargoSettingsViews.access} />
                <SettingsLink {...kargoSettingsViews.oidc} />
                <SettingsLink {...kargoSettingsViews.externalAccess} />
                <SettingsNavHeader>Advanced</SettingsNavHeader>
                <SettingsLink {...kargoSettingsViews.agentDefaultSettings} />
                <SettingsLink {...kargoSettingsViews.credentialsSettings} />
              </div>

              <div className='flex-1'>
                <Routes>
                  <Route
                    index
                    element={<Navigate to={kargoSettingsViews.general.path} replace={true} />}
                  />
                  {Object.values(kargoSettingsViews)
                    .filter((t) => !t.hidden)
                    .map((t) => (
                      <Route key={t.path} path={t.path} element={<t.component />} />
                    ))}
                </Routes>
              </div>
            </div>
          </PageContent>
        )}
      </KargoSettingsContext.Consumer>
    </KargoSettingsContextProvider>
  );
};
