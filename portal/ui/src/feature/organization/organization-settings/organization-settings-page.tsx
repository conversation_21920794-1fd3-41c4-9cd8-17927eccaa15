import {
  faB<PERSON>,
  faCog,
  faId<PERSON><PERSON><PERSON>,
  fa<PERSON>ey,
  faRobot,
  faSlide<PERSON>
} from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import { Navigate, Route, Routes, generatePath } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { SettingsLink } from '@ui/feature/common/settings/settings-link';
import { useGetOrganization } from '@ui/lib/apiclient/organization/organization-queries';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { Loading, PageContent } from '@ui/lib/components';
import { PageTitle } from '@ui/lib/components/page-title';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';
import { OrganizationRole } from '@ui/lib/types';

import { OrganizationContext } from './context/organization-settings-context';
import { AiSettings } from './views/ai-settings';
import DomainSettings from './views/domains/domain-settings';
import { GeneralSettings } from './views/general/general-settings';
import MFASettings from './views/mfa/mfa-settings';
import { QuotasSettings } from './views/quotas';
import { WebhooksSettings } from './views/webhooks/webhooks-settings';

export const OrganizationSettingsPage = () => {
  const { id } = useRequiredParams<'id'>(['id']);
  const { currentOrgFeatureStatuses, systemSettings, currentRole, permissionChecker } =
    useMainContext();

  const { data: organization, isLoading: isLoading } = useGetOrganization({ id });

  const settingsViews = React.useMemo(
    () => ({
      general: {
        path: 'general',
        label: 'General',
        icon: faCog,
        component: GeneralSettings
      },

      ...(currentOrgFeatureStatuses.notification === FeatureStatus.ENABLED &&
      permissionChecker.can({
        action: 'get',
        object: 'organization/notification-configs',
        resource: '*'
      })
        ? {
            webhooks: {
              path: 'webhooks',
              label: 'Webhooks',
              icon: faBell,
              component: WebhooksSettings
            }
          }
        : {}),
      ...(currentOrgFeatureStatuses.sso === FeatureStatus.ENABLED && !systemSettings?.selfHosted
        ? {
            domain: {
              path: 'domain',
              label: 'Domain Verification',
              icon: faIdBadge,
              component: DomainSettings
            }
          }
        : {}),
      ...(!systemSettings?.selfHosted &&
      currentOrgFeatureStatuses.multiFactorAuth === FeatureStatus.ENABLED &&
      currentRole === OrganizationRole.Owner
        ? {
            mfa: {
              path: 'mfa',
              label: 'MFA',
              icon: faKey,
              component: MFASettings
            }
          }
        : {}),
      ...(currentOrgFeatureStatuses.aiSupportEngineer === FeatureStatus.ENABLED && {
        ai: {
          path: 'ai',
          label: 'AI',
          icon: faRobot,
          component: AiSettings
        }
      }),
      ...(currentRole === OrganizationRole.Owner
        ? {
            quotas: {
              path: 'quotas',
              label: 'Quotas',
              icon: faSliders,
              component: QuotasSettings
            }
          }
        : {})
    }),
    [currentOrgFeatureStatuses, systemSettings, currentRole]
  );

  return (
    <OrganizationContext.Provider value={{ organization, isLoading }}>
      <PageContent
        breadcrumbs={[
          { label: 'Organizations', path: '/organizations' },
          {
            label: organization?.name || 'Not found',
            path: generatePath(paths.organization, { id }),
            loading: isLoading
          },
          {
            label: 'Settings',
            path: generatePath(paths.organizationSettings, { id }),
            loading: isLoading
          }
        ]}
      >
        <PageTitle>Organization Settings</PageTitle>

        {isLoading && <Loading />}
        {organization && (
          <div className='flex gap-8 -mt-4'>
            <div className='sticky pt-2 flex flex-col gap-2' style={{ flex: '0 0 12rem' }}>
              <SettingsLink key={settingsViews.general.path} {...settingsViews.general} />
              {settingsViews.webhooks && (
                <SettingsLink key={settingsViews.webhooks.path} {...settingsViews.webhooks} />
              )}
              {settingsViews.domain && (
                <SettingsLink key={settingsViews.domain.path} {...settingsViews.domain} />
              )}
              {settingsViews.mfa && (
                <SettingsLink key={settingsViews.mfa.path} {...settingsViews.mfa} />
              )}
              {settingsViews.ai && (
                <SettingsLink key={settingsViews.ai.path} {...settingsViews.ai} />
              )}
              {settingsViews.quotas && (
                <SettingsLink key={settingsViews.quotas.path} {...settingsViews.quotas} />
              )}
            </div>
            <div className='flex-1'>
              <Routes>
                <Route
                  index
                  element={<Navigate to={settingsViews.general.path} replace={true} />}
                />
                {Object.values(settingsViews).map((t) => (
                  <Route key={t.path} path={t.path} element={<t.component />} />
                ))}
              </Routes>
            </div>
          </div>
        )}
      </PageContent>
    </OrganizationContext.Provider>
  );
};
