import { Select } from 'antd';
import { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { Instance } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { KargoInstance } from '@ui/lib/apiclient/kargo/v1/kargo_pb';
import { FieldContainer } from '@ui/lib/components/forms/field-container';

import { InstanceQuotaTableEntry } from '../types';

import { FormSchema } from './schema';

type InstanceSelectProps = {
  currentInstanceQuotas: InstanceQuotaTableEntry[];
  index: number;
  allInstances: Instance[] | KargoInstance[];
  loading?: boolean;
};
export const InstanceSelect = ({
  currentInstanceQuotas,
  index,
  allInstances,
  loading
}: InstanceSelectProps) => {
  const [search, setSearch] = useState('');

  const { control, watch } = useFormContext<FormSchema, unknown, FormSchema>();

  const selectedInstances = watch('quotas').map((quota) => quota.instanceId);
  const selectedInstancesSet = useMemo(
    () => new Set([...currentInstanceQuotas.map((quota) => quota.id), ...selectedInstances]),
    [currentInstanceQuotas, selectedInstances]
  );
  const filteredInstancesOptions = useMemo(
    () =>
      allInstances
        .filter(
          (instance) =>
            (selectedInstances[index] === instance.id || !selectedInstancesSet.has(instance.id)) &&
            instance.name.includes(search)
        )
        .map(({ id, name }) => ({ label: name, value: id })),
    [allInstances, selectedInstancesSet, index, search]
  );

  return (
    <FieldContainer control={control} name={`quotas.${index}.instanceId`}>
      {({ field }) => (
        <Select
          {...field}
          className='w-full'
          placeholder='Instance Name'
          options={filteredInstancesOptions}
          filterOption={false}
          loading={loading}
          // Preserves placeholder when value === ''
          value={field.value || undefined}
          showSearch
          searchValue={search}
          onSearch={setSearch}
        />
      )}
    </FieldContainer>
  );
};
