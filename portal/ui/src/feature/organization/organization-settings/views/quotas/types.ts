import {
  InstanceQuota,
  KargoInstanceQuota
} from '@ui/lib/apiclient/organization/v1/organization_pb';

export type QuotaType = 'argo' | 'kargo';
export const isArgoInstance = (
  type: QuotaType,
  instance: InstanceQuota | KargoInstanceQuota
): instance is InstanceQuota => type === 'argo';

export type InstanceQuotaTableEntry = {
  type: QuotaType;
  id: string;
  name: string;
  currentUsage: number;
  maxUsage: number;
};
