import { faPencil, faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Space, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo } from 'react';

import { useOrganizationSettingsContext } from '@ui/feature/organization/organization-settings/context/organization-settings-context';
import {
  useListArgoCDInstancesQuota,
  useListKargoInstancesQuota
} from '@ui/lib/apiclient/organization/organization-queries';
import {
  InstanceQuota,
  KargoInstanceQuota
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { FilteredIcon } from '@ui/lib/components/filter';
import { useModal } from '@ui/lib/hooks';

import { AddQuotaModalTypeDependantProps, AddQuotasModal } from './add-quotas-modal';
import { EditQuotaModal, EditQuotaModalTypeDependantProps } from './edit-quota-modal';
import { AppliedQuotasFilters, NameFilter, useQuotasFilters } from './quotas-filters';
import { InstanceQuotaTableEntry, isArgoInstance, QuotaType } from './types';
import {
  useDeleteQuotaModal,
  UseDeleteQuotaModalTypeDependantProps
} from './use-delete-quota-modal';

type QuotaTabTypeDependantProps = {
  currentUsageTitle: string;
  limitTitle: string;
  useQueryGetInstanceQuotasList:
    | typeof useListArgoCDInstancesQuota
    | typeof useListKargoInstancesQuota;
};
type QuotaTabProps = QuotaTabTypeDependantProps &
  AddQuotaModalTypeDependantProps &
  EditQuotaModalTypeDependantProps &
  UseDeleteQuotaModalTypeDependantProps & {
    type: QuotaType;
  };
export const QuotasTab = ({
  type,
  currentUsageTitle,
  limitTitle,
  useQueryGetInstanceQuotasList,
  useQueryGetInstancesList,
  useMutationUpdateQuotas
}: QuotaTabProps) => {
  const { organization } = useOrganizationSettingsContext();

  const modal = useModal();
  const showDeleteQuotaModal = useDeleteQuotaModal({ useMutationUpdateQuotas });

  const instanceQuotas = useQueryGetInstanceQuotasList();
  const { filters } = useQuotasFilters();
  const tableData: InstanceQuotaTableEntry[] = useMemo(() => {
    const mapTableEntries = (instance: InstanceQuota | KargoInstanceQuota) => ({
      type,
      id: instance.instance.id,
      name: instance.instance.name,
      currentUsage: isArgoInstance(type, instance)
        ? instance.currentAppsCount
        : instance.currentStageCount,
      maxUsage: isArgoInstance(type, instance) ? instance.maxAppsCount : instance.maxStageCount
    });

    if (!instanceQuotas.data) {
      return [];
    }

    if (!filters.name) {
      return instanceQuotas.data.map(mapTableEntries).sort((a, b) => a.name.localeCompare(b.name));
    }

    return instanceQuotas.data
      .filter((instanceQuota) => instanceQuota.instance.name.includes(filters.name))
      .map(mapTableEntries)
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [instanceQuotas.data, filters]);
  const columns: ColumnsType<InstanceQuotaTableEntry> = useMemo(
    () => [
      {
        title: 'Name',
        dataIndex: ['name'],
        filtered: !!filters.name?.length,
        filterIcon: FilteredIcon,
        filterDropdown: NameFilter,
        filterDropdownProps: { placement: 'bottomLeft' }
      },
      {
        width: 250,
        title: currentUsageTitle,
        dataIndex: ['currentUsage']
      },
      {
        width: 250,
        title: limitTitle,
        dataIndex: ['maxUsage']
      },
      {
        width: 90,
        render: (_value, record) => (
          <Space>
            <Button
              icon={<FontAwesomeIcon size='sm' icon={faPencil} />}
              onClick={() =>
                modal.show((modalProps) => (
                  <EditQuotaModal
                    {...modalProps}
                    limitTitle={limitTitle}
                    currentInstanceQuotas={tableData}
                    editedInstanceQuota={record}
                    useMutationUpdateQuotas={useMutationUpdateQuotas}
                  />
                ))
              }
              size='small'
              type='text'
            />
            <Button
              icon={<FontAwesomeIcon size='sm' icon={faTrash} />}
              onClick={() =>
                showDeleteQuotaModal({
                  currentInstanceQuotas: tableData,
                  deletedInstanceQuota: record
                })
              }
              size='small'
              type='text'
            />
          </Space>
        )
      }
    ],
    [filters, tableData]
  );

  return (
    <div>
      <AppliedQuotasFilters />
      <Table
        dataSource={tableData}
        columns={columns}
        rowKey={(record) => record.id}
        pagination={{
          hideOnSinglePage: true
        }}
        loading={instanceQuotas.isPending}
      />
      <Button
        className='mt-4'
        type='primary'
        icon={<FontAwesomeIcon icon={faPlus} />}
        onClick={() =>
          modal.show((modalProps) => (
            <AddQuotasModal
              {...modalProps}
              organizationId={organization.id}
              currentInstanceQuotas={tableData}
              limitTitle={limitTitle}
              useQueryGetInstancesList={useQueryGetInstancesList}
              useMutationUpdateQuotas={useMutationUpdateQuotas}
            />
          ))
        }
      >
        Add New Quotas
      </Button>
    </div>
  );
};
