import { faDiagramProject, faServer } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Space, Tabs } from 'antd';

import { OrganizationSettingsWrapper } from '@ui/feature/organization/organization-settings/components/organization-settings-wrapper';
import { useGetArgoCDInstances } from '@ui/lib/apiclient/argocd/argocd-queries';
import { useListKargoInstances } from '@ui/lib/apiclient/kargo/kargo-queries';
import {
  useListArgoCDInstancesQuota,
  useListKargoInstancesQuota,
  useUpdateArgoCDInstancesQuota,
  useUpdateKargoInstancesQuota
} from '@ui/lib/apiclient/organization/organization-queries';
import { HelperPopover } from '@ui/lib/components/forms';

import { QuotasTab } from './quotas-tab';

export const QuotasSettings = () => {
  return (
    <OrganizationSettingsWrapper
      header={
        <Space>
          <h1>Quotas</h1>
          <div className='-ml-1 inline-block'>
            <HelperPopover docsText='By using quotas you can limit the amount of resources used by your instances. Only instances specified there will have limits, rest of the instances will only be limited by your plan total limit.' />
          </div>
        </Space>
      }
      fullWidth
    >
      <Tabs
        className='mt-4'
        items={[
          {
            key: 'argo',
            label: 'Deploy',
            icon: <FontAwesomeIcon icon={faServer} />,
            children: (
              <QuotasTab
                type='argo'
                currentUsageTitle='Applications'
                limitTitle='Applications Limit'
                useQueryGetInstancesList={useGetArgoCDInstances}
                useQueryGetInstanceQuotasList={useListArgoCDInstancesQuota}
                useMutationUpdateQuotas={useUpdateArgoCDInstancesQuota}
              />
            )
          },
          {
            key: 'kargo',
            label: 'Promote',
            icon: <FontAwesomeIcon icon={faDiagramProject} />,
            children: (
              <QuotasTab
                type='kargo'
                currentUsageTitle='Stages'
                limitTitle='Stages Limit'
                useQueryGetInstancesList={useListKargoInstances}
                useQueryGetInstanceQuotasList={useListKargoInstancesQuota}
                useMutationUpdateQuotas={useUpdateKargoInstancesQuota}
              />
            )
          }
        ]}
      />
    </OrganizationSettingsWrapper>
  );
};
