import { faMagnifyingGlass, IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from 'antd';
import { useForm } from 'react-hook-form';
import z from 'zod';

import { AppliedFilters } from '@ui/lib/components/applied-filters';
import { Filter } from '@ui/lib/components/filter';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { useSearchParamsState } from '@ui/lib/hooks/use-search-params-state';

const filtersSchema = z.object({
  name: z.string().optional()
});
type FiltersSchema = z.infer<typeof filtersSchema>;

const FILTER_META = {
  name: { label: 'Name', icon: faMagnifyingGlass }
} as const satisfies Record<keyof FiltersSchema, { label: string; icon: IconDefinition }>;

export const useQuotasFilters = () => {
  const {
    state: filters,
    setSearchState: setFilters,
    removeKeysFromSearch: removeFilters,
    hasActiveState,
    buildURLSearchParams,
    appliedFilters: appliedFiltersFromSearchParams
  } = useSearchParamsState(filtersSchema);

  const appliedFilters = appliedFiltersFromSearchParams.map((config) => ({
    ...config,
    label: FILTER_META[config.key].label,
    icon: FILTER_META[config.key].icon
  }));

  return {
    filters,
    setFilters,
    removeFilters,
    hasActiveState,
    appliedFilters,
    buildURLSearchParams
  };
};

type NameFilterProps = {
  close?: () => void;
};
export const NameFilter = ({ close }: NameFilterProps) => {
  const { filters, setFilters, removeFilters } = useQuotasFilters();
  const { control, handleSubmit } = useForm({
    values: {
      name: filters.name || ''
    },
    resolver: zodResolver(filtersSchema)
  });
  const onClear = () => {
    removeFilters(['name']);
    close?.();
  };
  const onApply = handleSubmit((filters) => {
    setFilters(filters);
    close?.();
  });

  return (
    <Filter onApplyFilter={onApply} onClearFilter={onClear} close={close}>
      <FieldContainer control={control} name='name' label='Name'>
        {({ field }) => <Input {...field} />}
      </FieldContainer>
    </Filter>
  );
};

export const AppliedQuotasFilters = () => {
  const { appliedFilters, removeFilters } = useQuotasFilters();

  const LIST_OF_KEYS = Object.keys(filtersSchema.shape) as Array<keyof FiltersSchema>;

  return (
    <AppliedFilters
      appliedFilters={appliedFilters}
      clearAllButton={{
        text: 'Clear all',
        onClick: () => removeFilters(LIST_OF_KEYS)
      }}
      className='mb-4 mt-2'
    />
  );
};
