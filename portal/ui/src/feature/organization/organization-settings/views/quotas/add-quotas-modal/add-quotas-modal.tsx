import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Modal, Input, Button, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo } from 'react';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';

import { useGetArgoCDInstances } from '@ui/lib/apiclient/argocd/argocd-queries';
import { useListKargoInstances } from '@ui/lib/apiclient/kargo/kargo-queries';
import {
  useUpdateArgoCDInstancesQuota,
  useUpdateKargoInstancesQuota
} from '@ui/lib/apiclient/organization/organization-queries';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { withForm } from '@ui/lib/components/with-form';
import { ModalProps } from '@ui/lib/hooks';

import { InstanceQuotaTableEntry } from '../types';

import { InstanceSelect } from './instance-select';
import { FormSchema, schema } from './schema';

export type AddQuotaModalTypeDependantProps = {
  limitTitle: string;
  useMutationUpdateQuotas:
    | typeof useUpdateArgoCDInstancesQuota
    | typeof useUpdateKargoInstancesQuota;
  useQueryGetInstancesList: typeof useGetArgoCDInstances | typeof useListKargoInstances;
};
type AddQuotasModalProps = ModalProps &
  AddQuotaModalTypeDependantProps & {
    organizationId: string;
    currentInstanceQuotas: InstanceQuotaTableEntry[];
  };
export const AddQuotasModal = ({
  visible,
  hide,
  organizationId,
  currentInstanceQuotas,
  limitTitle,
  useMutationUpdateQuotas,
  useQueryGetInstancesList
}: AddQuotasModalProps) => {
  const form = useForm<FormSchema, unknown, FormSchema>({
    defaultValues: {
      quotas: [{ instanceId: '' }]
    },
    resolver: zodResolver(schema)
  });

  const updateQuotas = useMutationUpdateQuotas();
  const submitForm = form.handleSubmit(async (formData) => {
    const formDataRecord = Object.fromEntries([
      ...currentInstanceQuotas.map(({ id, maxUsage }) => [id, maxUsage]),
      ...formData.quotas.map(({ instanceId, limit }) => [instanceId, limit])
    ]);
    updateQuotas.mutate(
      {
        instanceQuota: formDataRecord
      },
      {
        onSuccess: hide
      }
    );
  });

  const instancesQuery = useQueryGetInstancesList(
    {
      organizationId,
      workspaceId: ''
    },
    {
      placeholderData: []
    }
  );

  const fieldArrayQuotas = useFieldArray({
    control: form.control,
    name: 'quotas'
  });
  const columns: ColumnsType<(typeof fieldArrayQuotas)['fields'][number]> = useMemo(
    () => [
      {
        title: 'Name',
        dataIndex: 'name',
        render: (_value, _record, index) => (
          <InstanceSelect
            currentInstanceQuotas={currentInstanceQuotas}
            index={index}
            allInstances={instancesQuery.data}
            loading={instancesQuery.isPending}
          />
        )
      },
      {
        width: 150,
        title: limitTitle,
        dataIndex: ['limit'],
        render: (_value, _record, index) => (
          <FieldContainer control={form.control} name={`quotas.${index}.limit`}>
            {({ field }) => <Input {...field} type='number' min={0} placeholder='0' />}
          </FieldContainer>
        )
      },
      {
        width: 50,
        render: (_value, _record, index) => (
          <Button
            icon={<FontAwesomeIcon size='sm' icon={faTrash} />}
            onClick={() => fieldArrayQuotas.remove(index)}
            size='small'
            type='text'
          />
        )
      }
    ],
    [
      currentInstanceQuotas,
      instancesQuery.data,
      instancesQuery.isPending,
      form.control,
      fieldArrayQuotas.remove
    ]
  );

  return (
    <Modal
      width='700px'
      open={visible}
      modalRender={withForm({ onSubmit: submitForm })}
      okButtonProps={{ htmlType: 'submit', loading: updateQuotas.isPending }}
      okText='Add'
      title='Add New Quotas'
      onCancel={hide}
    >
      <FormProvider {...form}>
        <Table
          dataSource={fieldArrayQuotas.fields}
          columns={columns}
          pagination={false}
          rowClassName='align-top'
          size='small'
          rowKey='id'
        />
        <Button
          className='mt-4'
          type='dashed'
          block
          onClick={() => fieldArrayQuotas.append({ instanceId: '' })}
        >
          Add Next
        </Button>
      </FormProvider>
    </Modal>
  );
};
