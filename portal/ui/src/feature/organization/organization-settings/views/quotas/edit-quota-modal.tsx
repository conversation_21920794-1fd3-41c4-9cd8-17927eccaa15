import { zodResolver } from '@hookform/resolvers/zod';
import { Modal, Input } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  useUpdateArgoCDInstancesQuota,
  useUpdateKargoInstancesQuota
} from '@ui/lib/apiclient/organization/organization-queries';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { withForm } from '@ui/lib/components/with-form';
import { ModalProps } from '@ui/lib/hooks';

import { InstanceQuotaTableEntry } from './types';

const schema = z.object({
  maxUsage: z.coerce.number().nonnegative()
});
type FormSchema = z.infer<typeof schema>;

export type EditQuotaModalTypeDependantProps = {
  limitTitle: string;
  useMutationUpdateQuotas:
    | typeof useUpdateArgoCDInstancesQuota
    | typeof useUpdateKargoInstancesQuota;
};
type EditQuotaModalProps = ModalProps &
  EditQuotaModalTypeDependantProps & {
    currentInstanceQuotas: InstanceQuotaTableEntry[];
    editedInstanceQuota: InstanceQuotaTableEntry;
  };
export const EditQuotaModal = ({
  visible,
  hide,
  limitTitle,
  currentInstanceQuotas,
  editedInstanceQuota,
  useMutationUpdateQuotas
}: EditQuotaModalProps) => {
  const form = useForm<FormSchema, unknown, FormSchema>({
    defaultValues: {
      maxUsage: editedInstanceQuota.maxUsage
    },
    resolver: zodResolver(schema)
  });

  const updateQuotas = useMutationUpdateQuotas();
  const submitForm = form.handleSubmit(async ({ maxUsage }) => {
    const formDataRecord = Object.fromEntries([
      ...currentInstanceQuotas.map(({ id, maxUsage }) => [id, maxUsage]),
      [editedInstanceQuota.id, maxUsage]
    ]);
    updateQuotas.mutate(
      {
        instanceQuota: formDataRecord
      },
      {
        onSuccess: hide
      }
    );
  });

  return (
    <Modal
      open={visible}
      modalRender={withForm({ onSubmit: submitForm })}
      okButtonProps={{ htmlType: 'submit', loading: updateQuotas.isPending }}
      okText='Save'
      title={`Edit "${editedInstanceQuota.name}" Quota`}
      onCancel={hide}
    >
      <FieldContainer control={form.control} name='maxUsage' label={limitTitle}>
        {({ field }) => <Input {...field} type='number' min={0} placeholder='0' />}
      </FieldContainer>
    </Modal>
  );
};
