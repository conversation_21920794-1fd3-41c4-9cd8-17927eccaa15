import { useCallback } from 'react';

import {
  useUpdateArgoCDInstancesQuota,
  useUpdateKargoInstancesQuota
} from '@ui/lib/apiclient/organization/organization-queries';
import { useConfirmModal } from '@ui/lib/hooks/use-confirm-modal';

import { InstanceQuotaTableEntry } from './types';

export type UseDeleteQuotaModalTypeDependantProps = {
  useMutationUpdateQuotas:
    | typeof useUpdateArgoCDInstancesQuota
    | typeof useUpdateKargoInstancesQuota;
};
type UseDeleteQuotaModalCallbackProps = {
  currentInstanceQuotas: InstanceQuotaTableEntry[];
  deletedInstanceQuota: InstanceQuotaTableEntry;
};
export const useDeleteQuotaModal = ({
  useMutationUpdateQuotas
}: UseDeleteQuotaModalTypeDependantProps) => {
  const updateQuotas = useMutationUpdateQuotas();
  const deleteQuota = useCallback(
    async ({ currentInstanceQuotas, deletedInstanceQuota }: UseDeleteQuotaModalCallbackProps) => {
      const updatedInstanceQuotas = Object.fromEntries(
        currentInstanceQuotas.map(({ id, maxUsage }) => [id, maxUsage])
      );
      delete updatedInstanceQuotas[deletedInstanceQuota.id];

      return updateQuotas.mutateAsync({
        instanceQuota: updatedInstanceQuotas
      });
    },
    [updateQuotas.mutateAsync]
  );

  const confirm = useConfirmModal();
  const showDeleteQuotaModal = useCallback(
    ({ currentInstanceQuotas, deletedInstanceQuota }: UseDeleteQuotaModalCallbackProps) =>
      confirm({
        title: `Are you sure you want to delete instance quota for "${deletedInstanceQuota.name}"?`,
        onOk: () => deleteQuota({ currentInstanceQuotas, deletedInstanceQuota })
      }),
    [confirm, deleteQuota]
  );

  return showDeleteQuotaModal;
};
