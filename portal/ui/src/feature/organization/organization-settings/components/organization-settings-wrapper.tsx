import classNames from 'classnames';
import React from 'react';

type Props = React.PropsWithChildren<{
  header: string | React.ReactNode;
  fullWidth?: boolean;
}>;

export const OrganizationSettingsWrapper = ({ children, header, fullWidth }: Props) => {
  return (
    <div>
      <div className='pt-2 flex overflow-hidden border-b border-gray-100 mb-6 pb-4'>
        <div className='flex-1'>
          {typeof header === 'string' ? (
            <h1 className='flex-1 flex items-center'>{header}</h1>
          ) : (
            header
          )}
        </div>
      </div>
      <div className={classNames('pb-6', { 'max-w-5xl': !fullWidth })}>{children}</div>
    </div>
  );
};
