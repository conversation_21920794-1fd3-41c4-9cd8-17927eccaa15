// billing source of truth

import {
  BillingDetails,
  Organization,
  Plan
} from '@ui/lib/apiclient/organization/v1/organization_pb';

export type PlanTier =
  | 'starter_v2'
  | 'starter'
  | 'professional'
  | 'professional_plus'
  | 'professional_plus_trial'
  | 'enterprise'
  | 'unknown'
  | 'custom';

export const getPlanTier = (org: Organization, billingDetails: BillingDetails): PlanTier => {
  if (billingDetails?.manual) {
    return 'custom';
  }

  return (org?.plan as PlanTier) || 'unknown';
};

export const customerPlanStatus = (
  org: Organization,
  billingDetails: BillingDetails,
  expired: boolean
) => {
  if (org?.status?.trial && !billingDetails?.manual) {
    return `Free Trial expire${expired ? 'd' : 's'}`;
  }

  if (expired) {
    return 'Expired';
  }

  return 'Current plan renews';
};

export const shouldContactSales = (
  org: Organization,
  billingDetails: BillingDetails,
  planExpired: boolean
) => planExpired && (org?.status?.trial || billingDetails?.manual);

/**
 * new pricing must not show deprecated plans
 * however, the orgs are on deprecated plans should still be able to see them to upgrade/downgrade
 */
export const shouldShowPlan = (payload: {
  plan: PlanTier;
  availablePlans: Plan[];
  currentOrgPlan: PlanTier;
}) => {
  const planFound = payload.availablePlans.find((p) => p.name === payload.plan);

  if (!planFound) {
    return false;
  }

  const isDeprecatedPlan = planFound?.deprecated;

  return !isDeprecatedPlan || payload.plan === payload.currentOrgPlan;
};

export const canResubscribe = (payload: {
  plan: PlanTier;
  availablePlans: Plan[];
  currentOrgPlan: PlanTier;
  currentPlanSubscriptionActive?: boolean;
}) => {
  const isDeprecatedPlan = payload.availablePlans.find((p) => p.name === payload.plan)?.deprecated;

  return (
    !isDeprecatedPlan ||
    (payload.plan === payload.currentOrgPlan && payload.currentPlanSubscriptionActive)
  );
};
