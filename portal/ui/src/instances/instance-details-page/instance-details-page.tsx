import {
  faCircleExclamation,
  faCircleNotch,
  faEye,
  faListUl,
  faPuzzle<PERSON>iece,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';
import { faChartPie } from '@fortawesome/free-solid-svg-icons/faChartPie';
import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
import { faCog } from '@fortawesome/free-solid-svg-icons/faCog';
import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons/faExternalLinkAlt';
import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
import { faServer } from '@fortawesome/free-solid-svg-icons/faServer';
import { faSync } from '@fortawesome/free-solid-svg-icons/faSync';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Tabs, Button, Popover, Space, Descriptions, Alert, Typography, theme, Flex } from 'antd';
import { Col, Row } from 'antd/lib/grid';
import Progress from 'antd/lib/progress';
import Tooltip from 'antd/lib/tooltip';
import React, { useEffect, useMemo } from 'react';
import { generatePath, Navigate, useNavigate, useSearchParams } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { ClustersAddons } from '@ui/feature/argocd/addons/clusters-addons';
import { DeletingInstanceInfo } from '@ui/feature/common/instance/deleting-instance-info';
import { UnsupportedVersionBanner } from '@ui/feature/common/unsupported-version-banner';
import { KubeVision } from '@ui/feature/kubevision/kubevision';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { useGettingStartedTrigger } from '@ui/feature/user/getting-started/use-getting-started-trigger';
import {
  Cluster,
  EventType,
  HealthV1StatusCode,
  ReconciliationV1StatusCode,
  SyncOperationGroupField
} from '@ui/lib/apiclient';
import {
  useDeleteArgoCDCluster,
  useGetOutdatedAgentClusters
} from '@ui/lib/apiclient/argocd/argocd-clusters-queries';
import {
  useGetAIAssistantUsageStats,
  useUpdateArgoCDInstanceMutation
} from '@ui/lib/apiclient/argocd/argocd-queries';
import { useGetMembers } from '@ui/lib/apiclient/organization/organization-queries';
import {
  useGetLatestAgentVersion,
  useGetArgoCDVersions,
  useGetAgentVersions
} from '@ui/lib/apiclient/system/system-queries';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { OrganizationSummary } from '@ui/lib/apiclient/user/v1/user_pb';
import { refreshToken } from '@ui/lib/apiclient/utils';
import { Loading, PageContent, Error } from '@ui/lib/components';
import { FormSection } from '@ui/lib/components/forms';
import { HelperPopover } from '@ui/lib/components/forms/helper-popover';
import { PageTitle } from '@ui/lib/components/page-title';
import { PaidFeatureTag } from '@ui/lib/components/paid-feature-tag';
import { KubeVisionContextProvider } from '@ui/lib/context/kubevision-context';
import { useMainContext } from '@ui/lib/context/main-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { useDocumentEvent, useModal } from '@ui/lib/hooks';
import { useConfirmModal } from '@ui/lib/hooks/use-confirm-modal';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';
import { services } from '@ui/lib/services';
import { errorToString, pick, plural } from '@ui/lib/utils';
import { AIAssistantValue } from '@ui/organizations/components/ai-assistant-value';
import { AuditLog } from '@ui/organizations/components/audit-log';

import { IconPropsForReconciliation, HealthState } from '../components';
import { useClusterFilters } from '../components/cluster-table-filters/use-cluster-filters';
import { ClustersTable } from '../components/clusters-table';
import { CVEPatchedVersionsTable } from '../components/security/cve-patched-versions-table';
import { CVEVulnerabilityBox } from '../components/security/cve-vulnerability-box';
import { predicateGreaterPatchVersions } from '../components/security/utils';
import { SyncHistory } from '../components/sync-history/sync-history';

import { ClustersSecurityModal } from './clusters-security-modal';
import { ConnectClusterModal } from './connect-cluster-modal';
import { AuditFilterInstanceScope } from './filter/utils';
import { InstallAgentModal } from './install-agent-modal/install-agent-modal';
import { InstanceSummaryGraphs } from './instance-summary-graphs';
import { InstanceSummaryMetrics } from './instance-summary-metrics';
import { ConnectIntegratedClusterModal } from './integrated-cluster/connect-integrated-cluster';
import { EditIntegratedClusterModal } from './integrated-cluster/edit-integrated-cluster';
import { UpdateAgentModal } from './update-agent-modal';
import { UpdateClusterModal } from './update-cluster-modal';

import './instance-details-page.less';

export const InstanceDetailsPage = () => {
  const navigate = useNavigate();
  const [search, setSearch] = useSearchParams();
  const queryClient = useQueryClient();
  const { name, org } = useRequiredParams<'name' | 'org'>(['name', 'org']);
  const [installClusterId, setIntallClusterId] = React.useState<string>('');
  const gettingStartedTrigger = useGettingStartedTrigger();
  const isVisible = useDocumentEvent(
    'visibilitychange',
    () => document.visibilityState === 'visible'
  );
  const confirm = useConfirmModal();
  const isConnectModalShown = React.useRef(false);

  const ctx = useMainContext();
  const notification = useNotificationContext();
  const { useToken } = theme;
  const { token } = useToken();

  const instanceQueryKey = ['organizations', ctx.currentOrg, 'instances-by-name', name];
  const {
    data: inst,
    isLoading: isInstanceLoading,
    error: instanceError
  } = useQuery({
    queryKey: instanceQueryKey,
    queryFn: () => services.apiClient.instances.getByName(ctx.currentOrg?.id, name)
  });
  const canGetClusters = ctx.permissionChecker.can({
    action: 'get',
    object: 'workspace/instance/clusters',
    resource: `${inst?.workspaceId}/${inst?.id}`
  });

  const { mutate: updateVersion } = useUpdateArgoCDInstanceMutation();
  const { mutate: deleteCluster } = useDeleteArgoCDCluster();

  const {
    data: latestAgentVersion,
    isLoading: isVersionLoading,
    error: getLatestAgentVersionError
  } = useGetLatestAgentVersion();

  const {
    data: agentVersions,
    isLoading: isAgentVersionsLoading,
    error: getAgentVersionError
  } = useGetAgentVersions();

  const { data: members } = useGetMembers(
    { id: ctx?.currentOrg?.id },
    { enabled: !!ctx?.currentOrg?.id }
  );

  const clusterFilterProps = useClusterFilters(latestAgentVersion?.format?.());

  const clustersQueryKey = [
    'organizations',
    ctx.currentOrg,
    'instances',
    inst?.id,
    'clusters',
    clusterFilterProps.filters,
    isVisible
  ];

  const enableGetClusters =
    !!inst?.id && search.get('tab') === 'clusters' && isVisible && canGetClusters;
  const {
    data: clustersData,
    isInitialLoading: isClusterLoading,
    isSuccess,
    refetch: refetchClusters
  } = useQuery({
    queryKey: clustersQueryKey,
    queryFn: async () => {
      if (!inst) {
        return;
      }

      const { clusters = [], totalCount = 0 } = await services.apiClient.instances.getClusters(
        ctx.currentOrg?.id,
        inst?.id,
        clusterFilterProps.filters
      );

      return { clusters, totalClusters: +totalCount };
    },
    enabled: enableGetClusters
  });

  React.useEffect(() => {
    if ((clustersData?.totalClusters ?? 0) > 0 && clustersData?.clusters?.length === 0) {
      clusterFilterProps.setFilters({ ...clusterFilterProps.filters, offset: '0' });
    }
  }, [isSuccess, clustersData]);

  React.useEffect(() => {
    const changeContext = async (o: OrganizationSummary) => {
      ctx.changeCurrentOrg(o);
      queryClient.refetchQueries({
        queryKey: ['instances', 'organizations', o]
      });
    };

    if (org === 'u' && ctx.currentOrg) {
      changeContext(ctx.user.organizations?.find((value) => value.id === ''));
    } else if (org !== ctx.currentOrg?.name) {
      for (const o of ctx.user.organizations || []) {
        if (o.name === org) {
          changeContext(o);
          break;
        }
      }
    }
  }, [org]);

  const { show: showConnectClusterModal } = useModal((p) => (
    <ConnectClusterModal
      {...p}
      inst={inst}
      setIntallClusterId={setIntallClusterId}
      availableAgentVersions={agentVersions}
    />
  ));

  const { show: showConnectExistingClusterModal } = useModal((p) => (
    <ConnectIntegratedClusterModal {...p} instanceId={inst.id} />
  ));

  const { show: showModal } = useModal();

  const showUpdateClusterModal = (cluster: Cluster) =>
    showModal((p) =>
      cluster.data.directClusterSpec?.clusterType ? (
        <EditIntegratedClusterModal {...p} cluster={cluster} instanceId={inst.id} />
      ) : (
        <UpdateClusterModal
          {...p}
          instance={inst}
          orgId={ctx.currentOrg?.id}
          cluster={cluster}
          availableAgentVersions={agentVersions}
          setIntallClusterId={setIntallClusterId}
          multiClusterK8sDashboardEnabled={inst?.spec?.multiClusterK8sDashboardEnabled}
        />
      )
    );

  const showClustersSecurityModal = () =>
    showModal((props) => (
      <ClustersSecurityModal {...props} orgId={ctx?.currentOrg?.id} instance={inst} />
    ));

  React.useEffect(() => {
    if (
      search.get('connectCluster') === 'true' &&
      agentVersions &&
      inst &&
      !isConnectModalShown.current
    ) {
      showConnectClusterModal();
      isConnectModalShown.current = true;
    }
  }, [search, inst, agentVersions]);

  React.useEffect(() => {
    if (!inst || !ctx.currentOrg?.id || !isVisible) {
      return;
    }

    return services.apiClient.instances.watch(
      ctx.currentOrg?.id,
      (e) => {
        if (e.type === EventType.EVENT_TYPE_DELETED) {
          navigate(paths.instances);
        } else {
          queryClient.setQueryData(instanceQueryKey, { ...e.item });
        }
      },
      { id: inst.id }
    );
  }, [isInstanceLoading, ctx.currentOrg?.id, isVisible]);

  let minClusterName: string | null = null;
  let maxClusterName: string | null = null;
  if (clustersData && clustersData.clusters.length > 0) {
    if (+clusterFilterProps.filters.offset > 0) {
      minClusterName = clustersData.clusters[0].name;
    }
    if (
      clustersData.totalClusters >
      +clusterFilterProps.filters.limit + +clusterFilterProps.filters.offset
    ) {
      maxClusterName = clustersData.clusters[clustersData.clusters.length - 1].name;
    }
  }

  // Temporary fix for pagiation update on add/delete new cluster
  // This watchClusters watch all cluster
  useEffect(() => {
    if (!inst || !clustersData || !ctx.currentOrg?.id || !isVisible) {
      return;
    }

    return services.apiClient.instances.watchClusters(
      ctx.currentOrg?.id,
      inst.id,
      {
        minClusterName,
        maxClusterName,
        filters: { ...clusterFilterProps.filters, limit: '9999', offset: '0' }
      },
      (event) => {
        const clusterEvent = event.result;

        if (
          (clusterEvent.type === EventType.EVENT_TYPE_ADDED ||
            clusterEvent.type === EventType.EVENT_TYPE_DELETED) &&
          clustersData.totalClusters >= Number(clusterFilterProps.filters.limit) - 1
        ) {
          refetchClusters();
        }
      }
    );
  }, [
    isInstanceLoading,
    isClusterLoading,
    ctx.currentOrg?.id,
    clusterFilterProps.filters,
    latestAgentVersion,
    minClusterName,
    maxClusterName,
    isVisible
  ]);

  useEffect(() => {
    if (!inst || !clustersData || !ctx.currentOrg?.id || !isVisible) {
      return;
    }

    return services.apiClient.instances.watchClusters(
      ctx.currentOrg?.id,
      inst.id,
      { minClusterName, maxClusterName, filters: clusterFilterProps.filters },
      (event) => {
        const clusterEvent = event.result;
        const existing = queryClient.getQueryData<typeof clustersData>(clustersQueryKey);
        const existingClusters = existing?.clusters || [];

        const setClusters = (nextClusters: Cluster[]) => {
          queryClient.setQueryData(clustersQueryKey, {
            ...existing,
            clusters: nextClusters
          });
        };
        const clusterIndexFromEvent = existingClusters.findIndex(
          (cluster) => cluster.id === clusterEvent?.item?.id
        );

        const clusterAlreadyInExistingList = clusterIndexFromEvent > -1;

        if (clusterEvent.type === EventType.EVENT_TYPE_DELETED && clusterAlreadyInExistingList) {
          setClusters(existingClusters.filter((_, idx) => idx !== clusterIndexFromEvent));
          return;
        }

        if (
          (clusterEvent.type === EventType.EVENT_TYPE_ADDED ||
            clusterEvent.type === EventType.EVENT_TYPE_MODIFIED) &&
          !clusterAlreadyInExistingList
        ) {
          setClusters(
            [...existingClusters, clusterEvent.item].sort((first, second) =>
              first.name.localeCompare(second.name)
            )
          );
          return;
        }

        if (clusterEvent.type === EventType.EVENT_TYPE_MODIFIED && clusterAlreadyInExistingList) {
          setClusters([
            ...existingClusters.slice(0, clusterIndexFromEvent),
            clusterEvent.item,
            ...existingClusters.slice(clusterIndexFromEvent + 1)
          ]);
        }
      }
    );
  }, [
    isInstanceLoading,
    isClusterLoading,
    ctx.currentOrg?.id,
    clusterFilterProps.filters,
    latestAgentVersion,
    minClusterName,
    maxClusterName,
    isVisible
  ]);

  const clusters = clustersData?.clusters || [];
  const installCluster = installClusterId && clusters?.find((c) => c.id === installClusterId);

  React.useEffect(() => {
    if (clusters?.[0]?.healthStatus?.code === HealthV1StatusCode.STATUS_CODE_HEALTHY) {
      gettingStartedTrigger();
    }
  }, [clusters?.[0]?.healthStatus?.code]);

  const error = instanceError;

  const { data: outdatedAgentClusters, isLoading: isOutdatedAgentClustersLoading } =
    useGetOutdatedAgentClusters(
      {
        instanceId: inst?.id,
        filter: {
          excludeAgentVersion: latestAgentVersion?.format(),
          limit: BigInt(
            inst?.clusterCount || services.apiClient.instances.defaultClusterFilters.limit
          )
        }
      },
      {
        enabled: !!latestAgentVersion && !!inst?.id && canGetClusters
      }
    );

  const { data: rawVersions, isLoading: isRawVersionsLoading } = useGetArgoCDVersions();

  const hasNextVersionSecurityPatches = useMemo(() => {
    if (!rawVersions?.length || !inst?.version) {
      return false;
    }

    return !!rawVersions
      ?.filter(predicateGreaterPatchVersions(inst?.version))
      ?.find((v) => v?.securityAdvisories?.length > 0);
  }, [rawVersions, inst?.version]);

  const { show: showUpdateAgentModal } = useModal();
  let UpdateAllAgents = null;
  if (canGetClusters && inst?.clusterCount && inst.clusterCount > 0) {
    if (isOutdatedAgentClustersLoading) {
      UpdateAllAgents = <Loading />;
    } else {
      const ButtonIfAllAgentsHaveUpToDateVersion = (
        <Popover content='All Agents are running latest available version'>
          <Button
            icon={<FontAwesomeIcon icon={faCheck} />}
            className='text-green-500 text-xs'
            size='small'
            type='text'
          >
            Up To Date Agents
          </Button>
        </Popover>
      );

      const ButtonIfMoreThanZeroAgentsWithNoLatestVersion = (
        <Popover
          content={
            <>
              {outdatedAgentClusters?.length} Agent{plural(outdatedAgentClusters) ? 's' : ''} are
              detected running older version. Please click here to update all to latest available
              version <b>{latestAgentVersion?.format?.()}</b>
            </>
          }
        >
          <Button
            icon={<FontAwesomeIcon icon={faSync} />}
            danger
            className='text-xs'
            size='small'
            type='dashed'
            style={{ fontSize: '12px' }}
            onClick={() =>
              showUpdateAgentModal((p) => (
                <UpdateAgentModal
                  {...p}
                  instanceId={inst?.id}
                  latestVersion={latestAgentVersion.format()}
                  outdatedAgentClusters={outdatedAgentClusters}
                />
              ))
            }
          >
            {inst?.clusterCount - outdatedAgentClusters?.length}/{inst?.clusterCount} Up To Date
            Agent(s)
          </Button>
        </Popover>
      );

      const upToDateAgentsPercentage =
        ((inst?.clusterCount - outdatedAgentClusters?.length) * 100) / inst?.clusterCount;

      UpdateAllAgents = (
        <div className='w-[170px] scale-90'>
          {outdatedAgentClusters?.length == 0
            ? ButtonIfAllAgentsHaveUpToDateVersion
            : ButtonIfMoreThanZeroAgentsWithNoLatestVersion}
          <Progress
            percent={Math.round(upToDateAgentsPercentage * 10) / 10}
            success={{ percent: upToDateAgentsPercentage }}
          />
        </div>
      );
    }
  }

  const { data: aiAssistantStats } = useGetAIAssistantUsageStats(
    {
      organizationId: ctx?.currentOrg?.id,
      instanceId: [inst?.id]
    },
    {
      enabled: Boolean(ctx?.currentOrg?.id && inst?.id)
    }
  );

  const showSummaryIconWarning =
    inst?.reconciliationStatus.code == ReconciliationV1StatusCode.STATUS_CODE_FAILED ||
    inst?.clusterCount < 1;
  const basepath = inst?.spec?.basepath ? `/${inst.spec.basepath}` : '';
  const argocdUrl = 'https://' + inst?.hostname + basepath;

  if (isInstanceLoading) {
    return (
      <PageContent>
        <Loading />
      </PageContent>
    );
  }
  if (error) {
    notification.error({ message: `API Error: ${error.message}` });
    return <Navigate to={paths.instances} replace />;
  }
  if (!inst) {
    return <Navigate to={paths.instances} replace />;
  }

  // Support legacy URL for settings tab
  if (search.get('tab') === 'settings') {
    return <Navigate to={generatePath(paths.argoCDInstanceSettings, { org, name })} />;
  }

  return (
    <PageContent
      breadcrumbs={[
        { label: 'Argo CD', path: paths.instances },
        {
          label: (
            <Space>
              {name}
              {!inst?.deleteTime &&
                inst?.reconciliationStatus?.code !==
                  ReconciliationV1StatusCode.STATUS_CODE_SUCCESSFUL && (
                  <Tooltip title={inst?.reconciliationStatus.message}>
                    <FontAwesomeIcon
                      {...IconPropsForReconciliation(inst?.reconciliationStatus.code)}
                    />
                  </Tooltip>
                )}
            </Space>
          ),
          path: generatePath(paths.instance, { org, name }),
          loading: isInstanceLoading
        }
      ]}
    >
      {isInstanceLoading ? (
        <Loading />
      ) : error ? (
        <Error err={error} />
      ) : (
        inst && (
          <div className='instance-details-page'>
            <DeletingInstanceInfo deleteTime={inst.deleteTime} className='mb-8 -mt-2' />
            <UnsupportedVersionBanner
              className='mb-10 -mt-2'
              instance={inst}
              orgName={ctx.currentOrg?.name}
            />
            <Tabs
              destroyOnHidden
              className='-mt-6'
              onChange={(tab) => setSearch({ tab })}
              activeKey={search.get('tab') || 'summary'}
              tabBarExtraContent={
                <Button
                  icon={<FontAwesomeIcon icon={faCog} />}
                  type='text'
                  onClick={() =>
                    navigate(generatePath(paths.argoCDInstanceSettings, { org, name }))
                  }
                  data-qe-id='settings-btn'
                  size='small'
                >
                  Settings
                </Button>
              }
              items={[
                {
                  key: 'summary',
                  label: showSummaryIconWarning ? (
                    <>
                      <Typography.Text type='warning'>
                        <FontAwesomeIcon icon={faCircleExclamation} className='mr-1' />
                      </Typography.Text>{' '}
                      <span>Summary</span>
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faChartPie} className='mr-1' /> <span>Summary</span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Argo CD Summary</PageTitle>
                      {inst.reconciliationStatus.code ==
                      ReconciliationV1StatusCode.STATUS_CODE_FAILED ? (
                        <Alert
                          message={inst.reconciliationStatus.message}
                          type='error'
                          banner
                          className='mb-4'
                        />
                      ) : (
                        inst?.clusterCount < 1 && (
                          <Alert
                            message='You will be unable to deploy any applications to this instance until you
                          configure a cluster.'
                            type='warning'
                            banner
                            className='mb-4'
                          />
                        )
                      )}

                      <Descriptions bordered size='small' column={2} className='max-w-3xl'>
                        <Descriptions.Item label='Status'>
                          <span data-qe-id='argocd-status'>
                            <HealthState
                              health={inst.healthStatus}
                              deletionTimestamp={inst.deleteTime}
                              className='mr-6'
                            />
                          </span>
                        </Descriptions.Item>
                        <Descriptions.Item label='ID'>
                          <span data-qe-id='argocd-id'>{inst?.id}</span>
                        </Descriptions.Item>
                        <Descriptions.Item label='URL' span={2}>
                          {inst?.hostname &&
                            !(
                              inst?.healthStatus.code === HealthV1StatusCode.STATUS_CODE_UNKNOWN &&
                              inst?.generation <= 1
                            ) && (
                              <div className='flex items-center'>
                                <FontAwesomeIcon icon={faExternalLinkAlt} className='mr-2' />
                                <Typography.Link
                                  href={argocdUrl}
                                  target='_blank'
                                  className='underline'
                                >
                                  {inst?.hostname + basepath}
                                </Typography.Link>
                              </div>
                            )}
                        </Descriptions.Item>
                        {!!inst.description && (
                          <Descriptions.Item label='Description' span={2}>
                            <div style={{ wordBreak: 'break-all' }}>
                              {inst.description?.split('\n').map((line, i) => (
                                <p key={i}>{line}</p>
                              ))}
                            </div>
                          </Descriptions.Item>
                        )}
                      </Descriptions>
                      <Flex className='mb-4'>
                        <div className='flex flex-none text-2xl font-bold mr-6'>
                          {inst.deleteTime &&
                            inst.reconciliationStatus.code !=
                              ReconciliationV1StatusCode.STATUS_CODE_SUCCESSFUL && (
                              <span className='text-sm' title={inst.reconciliationStatus.message}>
                                <FontAwesomeIcon
                                  {...IconPropsForReconciliation(inst.reconciliationStatus.code)}
                                />
                              </span>
                            )}
                        </div>
                      </Flex>
                      <div className='flex gap-4 items-start'>
                        <FormSection title='Applications Metrics' className='pb-5' isLast>
                          <div className='flex flex-wrap items-stretch w-full gap-4'>
                            <InstanceSummaryGraphs instance={inst} />
                            <InstanceSummaryMetrics instance={inst} />
                          </div>
                        </FormSection>
                        <AIAssistantValue {...aiAssistantStats} className='mb-2' />
                      </div>
                      <SyncHistory
                        fixed={{ instance: [inst?.id] }}
                        allowedGroupBy={Object.values(SyncOperationGroupField).filter(
                          (gf) =>
                            gf !== SyncOperationGroupField.SYNC_OPERATION_GROUP_FIELD_INSTANCE_NAMES
                        )}
                        excludeFilters={['instance']}
                        instances={[pick(inst, ['name', 'id', 'hostname'])]}
                      />
                    </>
                  )
                },
                // TODO: The feature has been moved to ArgoCD and Kargo, and is hidden on AKP using a boolean flag in case we reverse the decision. Related to issue #8105.
                (() => {
                  const showIntelligenceTab = false; // TODO: Set to true to re-enable Intelligence tab
                  return (
                    showIntelligenceTab &&
                    ctx.currentOrgFeatureStatuses.multiClusterK8sDashboard ===
                      FeatureStatus.ENABLED &&
                    ctx.permissionChecker.can({
                      action: 'get',
                      object: 'organization/kubernetes-dashboard',
                      resource: '*'
                    }) &&
                    inst?.spec?.multiClusterK8sDashboardEnabled
                  );
                })() && {
                  key: 'kubevision',
                  label: (
                    <>
                      <FontAwesomeIcon icon={faEye} className='mr-1' />
                      <span>
                        Intelligence
                        <sup className='ml-0.5'>Beta</sup>
                      </span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Akuity Intelligence Explorer</PageTitle>
                      <PlatformContextProvider
                        platform='akuity-platform'
                        refreshToken={() => refreshToken()}
                      >
                        <KubeVisionContextProvider
                          organizationMode={false}
                          kubeVisionConfig={inst.spec.kubeVisionConfig}
                          instanceId={inst.id}
                          organizationId={ctx.currentOrg.id}
                          currentRole={ctx.currentRole}
                        >
                          <KubeVision
                            isk8sFeatureOn={inst.spec?.multiClusterK8sDashboardEnabled}
                            isAISREFeatureOn={
                              ctx.currentOrgFeatureStatuses.aiSupportEngineer ===
                              FeatureStatus.ENABLED
                            }
                            disabled={search.get('tab') !== 'kubevision'}
                          />
                        </KubeVisionContextProvider>
                      </PlatformContextProvider>
                    </>
                  )
                },
                canGetClusters && {
                  key: 'clusters',
                  label: (
                    <>
                      <FontAwesomeIcon icon={faServer} className='mr-1' /> <span>Clusters</span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Argo CD Clusters</PageTitle>
                      {isClusterLoading || isVersionLoading || isAgentVersionsLoading ? (
                        <Loading />
                      ) : (
                        <>
                          <Flex className='mb-2'>
                            <Flex align='center' className='gap-5'>
                              <h2 className='font-bold text-2xl mb-3'>
                                Clusters{' '}
                                <HelperPopover
                                  docsText={
                                    'Clusters are targets to which your applications will be deployed.'
                                  }
                                />
                              </h2>

                              {UpdateAllAgents}

                              <Tooltip title='Clusters Security'>
                                <FontAwesomeIcon
                                  icon={faShieldAlt}
                                  className='cursor-pointer text-lg'
                                  onClick={showClustersSecurityModal}
                                />
                              </Tooltip>
                            </Flex>

                            <Space className='ml-auto'>
                              <Button
                                disabled={
                                  ctx?.currentOrgFeatureStatuses.argocdClusterIntegration ===
                                  FeatureStatus.DISABLED
                                }
                                icon={<FontAwesomeIcon icon={faPlus} />}
                                className='ml-auto flex items-center gap-2'
                                onClick={() => showConnectExistingClusterModal()}
                              >
                                Integration
                                {ctx?.currentOrgFeatureStatuses.argocdClusterIntegration ===
                                  FeatureStatus.DISABLED && <PaidFeatureTag />}
                              </Button>
                              <Button
                                type='primary'
                                icon={<FontAwesomeIcon icon={faPlus} />}
                                className='ml-auto'
                                onClick={() => showConnectClusterModal()}
                              >
                                Connect a cluster
                              </Button>
                            </Space>
                          </Flex>

                          {Boolean(getLatestAgentVersionError || getAgentVersionError) && (
                            <Alert
                              type='error'
                              description={`Error getting agent version: ${errorToString(
                                getLatestAgentVersionError || getAgentVersionError
                              )}`}
                            />
                          )}

                          <ClustersTable
                            clusters={clusters}
                            onClusterRemove={(id) =>
                              deleteCluster({
                                id,
                                instanceId: inst?.id
                              })
                            }
                            onInstallAgent={(id) => setIntallClusterId(id)}
                            onSettingsClick={showUpdateClusterModal}
                            clusterFilters={clusterFilterProps}
                            totalCluster={clustersData?.totalClusters}
                            instanceId={inst.id}
                            agentVersions={agentVersions}
                          />
                        </>
                      )}
                    </>
                  )
                },
                ctx.currentOrgFeatureStatuses.fleetManagement === FeatureStatus.ENABLED && {
                  key: 'addons',
                  label: (
                    <>
                      <FontAwesomeIcon icon={faPuzzlePiece} className='mr-1' />{' '}
                      <span>
                        Addons
                        <sup className='ml-0.5'>Beta</sup>
                      </span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Argo CD Addons</PageTitle>
                      <ClustersAddons instance={inst} workspaceId={inst.workspaceId} />
                    </>
                  )
                },
                {
                  key: 'audit',
                  label: (
                    <>
                      <FontAwesomeIcon icon={faListUl} className='mr-1' /> <span>Audit</span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Argo CD Audit</PageTitle>
                      <AuditLog
                        members={members}
                        organizationId={ctx?.currentOrg?.id}
                        filterScopes='instance'
                        filterOverwrite={(filters) => {
                          return new AuditFilterInstanceScope(inst?.name).overwrite(filters);
                        }}
                        hideArchive
                      />
                    </>
                  )
                },
                {
                  key: 'security',
                  label: (
                    <>
                      <FontAwesomeIcon
                        spin={isRawVersionsLoading}
                        icon={isRawVersionsLoading ? faCircleNotch : faShieldAlt}
                        className='mr-1'
                        style={{
                          color: hasNextVersionSecurityPatches ? token.colorWarning : 'inherit'
                        }}
                      />{' '}
                      <span>Security</span>
                    </>
                  ),
                  children: (
                    <>
                      <PageTitle>Argo CD Security</PageTitle>
                      <h2 className='font-bold text-2xl'>
                        Common Vulnerabilities and Exposures (CVE)
                      </h2>
                      <Row gutter={24}>
                        <Col md={7} sm={24}>
                          <CVEVulnerabilityBox
                            currentVersion={inst?.version}
                            showCurrentVersionCVEPatches
                            showCVESuccess
                          />
                        </Col>

                        <Col className='mt-4' md={17} sm={24}>
                          <CVEPatchedVersionsTable
                            currentVersion={inst?.version}
                            versions={rawVersions}
                            onUpdateVersion={(version) => {
                              confirm({
                                title: `Are you sure you want to upgrade version to ${version}`,
                                footnote: {
                                  message: `This change will automatically update the agent installation in your managed clusters using the latest Akuity version.`,
                                  banner: true
                                },
                                onOk: async () =>
                                  updateVersion(
                                    { id: inst?.id, patch: { version } },
                                    { onSuccess: () => setSearch({ tab: 'summary' }) }
                                  )
                              });
                            }}
                          />
                        </Col>
                      </Row>
                    </>
                  )
                }
              ].filter(Boolean)}
            />
            <InstallAgentModal
              hide={() => setIntallClusterId('')}
              installCluster={installCluster}
              instanceId={inst?.id}
              visible={!!installCluster}
            />
          </div>
        )
      )}
    </PageContent>
  );
};
