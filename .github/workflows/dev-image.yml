name: DevImage

on:
  push:
    branches:
      - '*/dev-image'
env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  dev-image:
    name: Dev Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      # Add "<EMAIL>" Principal and assign an "Artifact Registry Writer" Role
      DISTROLESS: "true"
      PUSH_IMAGE: "true"
      PUSH_LATEST: "false"
      IMAGE_REPO: us-docker.pkg.dev/akuity-test/${{ github.actor }}/akp/akuity-platform
      GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        name: Checkout
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0
      # The following is necessary because `go list` (used in our makefile) does not seem to
      # use git credentials the same way as `git ls-remote`, and secrets.GITHUB_TOKEN is not
      # accepted by GitHub for username/password auth
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:
          driver-opts: network=host
      - name: Login to Google Artifact Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: us-docker.pkg.dev
          username: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          password: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.5
          check-latest: true
          cache: false
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - run: make image
        id: push-image
      - name: Echo image built
        run: echo "Image built - [${{ steps.push-image.outputs.akuity-platform-image }}]"
