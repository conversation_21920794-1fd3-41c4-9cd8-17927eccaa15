name: Update Deprecated Kubernetes APIs for Akuity Intelligence

on:
  schedule:
    # Run every Sunday at midnight
    - cron: '0 0 * * 0'
  workflow_dispatch: # Allow manual triggering

jobs:
  update-deprecated-apis:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Platform Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.5
          check-latest: true
          cache: false
      - name: <PERSON><PERSON> Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Update Deprecated Kubernetes APIs
        run: make generate-deprecated-gvk-map
      - name: Check for changes
        id: changes
        run: |
          if ! git diff --exit-code -- internal/services/k8sresource/deprecated_gvk_map.gen.go; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
      - name: Create Pull Request
        if: steps.changes.outputs.changed == 'true'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ secrets.AKUITYBOT_PAT }}
          author: Akuity Bot <<EMAIL>>
          commit-message: "chore(akuity-intelligence): update deprecated Kubernetes APIs"
          title: "chore(akuity-intelligence): update deprecated Kubernetes APIs"
          body: "Update deprecated Kubernetes APIs for Akuity Intelligence"
          branch: akuity-bot/update-deprecated-apis
          delete-branch: true 
