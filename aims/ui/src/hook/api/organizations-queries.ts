import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery, UseQueryOptions } from '@tanstack/react-query';

import { auditFilterTransformer } from '@/lib/components/audit-log/filters/utils';
import {
  OrganizationFilters,
  useOrganizationFilters
} from '@/organizations/filters/use-organization-filter';
import { PaginationPaylod, RawAuditFilters } from '@/types';

import { queryKeys } from '../../constants/query-keys';
import {
  Audit,
  BasicOrganization,
  GetFeatureGatesResponse,
  GetInternalConfigResponse,
  GetOrganizationResponse,
  InternalConfig,
  ListAllOrganizationsResponse,
  SendNotificationRequest,
  ListAuditLogsResponse,
  ListOrganizationMembersResponse,
  GetKubeVisionUsageResponse,
  ListAvailablePlansResponse,
  UpdateOrganizationBillingPlanRequest,
  ListOrganizationDomainsResponse,
  UpdateOrganizationDomainsResponse,
  ListTeamsResponse,
  ListWorkspacesResponse,
  ResetMFARequest,
  ListOrganizationCustomRolesResponse,
  ListOrganizationUsersResponse,
  UpdateQuotasResponse
} from '../../lib/apiclient/aims/v1/aims_pb';
import {
  DomainVerification,
  GetAIConversationResponse,
  GetWorkspaceResponse,
  ListAIConversationsResponse,
  ListTeamMembersResponse,
  ListWorkspaceCustomRolesResponse,
  ListWorkspaceMembersResponse
} from '../../lib/apiclient/organization/v1/organization_pb';
import {
  OrganizationFeatureGates,
  OrganizationQuota
} from '../../lib/apiclient/types/features/v1/features_pb';
import { apiFetch } from '../../utils/api-fetch';

export function useListAllOrganizations(
  filters?: Partial<OrganizationFilters>,
  options?: Omit<UseQueryOptions<ListAllOrganizationsResponse>, 'queryKey' | 'queryFn'>
) {
  const { buildURLSearchParams } = useOrganizationFilters();

  const queryParams = buildURLSearchParams(filters);

  return useQuery<ListAllOrganizationsResponse>({
    queryKey: queryKeys.organizations.list(filters).queryKey,
    queryFn: () => apiFetch(`v1/aims/organizations?${queryParams.toString()}`),
    placeholderData: (prev: ListAllOrganizationsResponse) => prev,
    ...options
  });
}

export function useListOrganizationMembers(
  orgIds: string[] = [],
  options?: Omit<UseQueryOptions<ListOrganizationMembersResponse>, 'queryKey' | 'queryFn'>
) {
  const search = new URLSearchParams();

  for (const orgId of orgIds) {
    search.append('organization_id', orgId);
  }

  return useQuery<ListOrganizationMembersResponse>({
    queryKey: queryKeys.organizations.members(orgIds).queryKey,
    queryFn: () => apiFetch(`v1/aims/organizations/members?${search.toString()}`),
    ...options
  });
}

export function useGetOrganization(
  id: string,
  options?: Omit<UseQueryOptions<BasicOrganization>, 'queryKey' | 'queryFn'>
) {
  return useQuery<BasicOrganization>({
    queryKey: queryKeys.organizations.item(id).queryKey,
    queryFn: () =>
      apiFetch<GetOrganizationResponse>(`v1/aims/organizations/${id}`).then(
        (response) => response.organization
      ),
    enabled: !!id,
    ...options
  });
}

export function useGetFeatureGates(
  id: string,
  options?: Omit<UseQueryOptions<GetFeatureGatesResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<GetFeatureGatesResponse>({
    queryKey: queryKeys.organizations.featureGates(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/aims/organizations/${id}/feature-gates`).then(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (res) => new GetFeatureGatesResponse(res as any)
      ),
    enabled: !!id,
    ...options
  });
}

export function useGetOrganizationDomains(
  id: string,
  options?: Omit<UseQueryOptions<ListOrganizationDomainsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListOrganizationDomainsResponse>({
    queryKey: queryKeys.organizations.domains(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/aims/organizations/${id}/domains`).then(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (res) => new ListOrganizationDomainsResponse(res as any)
      ),
    enabled: !!id,
    ...options
  });
}

export function useUpdateOrganizationDomains() {
  return useMutation({
    mutationFn: ({
      id,
      domains,
      audit
    }: {
      id: string;
      domains: DomainVerification[];
      audit: PlainMessage<Audit>;
    }) =>
      apiFetch(`v1/aims/organizations/${id}/domains`, {
        method: 'POST',
        body: JSON.stringify({ domains, audit })
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      }).then((res) => new UpdateOrganizationDomainsResponse(res as any))
  });
}

export function usePatchFeatureGates() {
  return useMutation({
    mutationFn: ({
      id,
      featureGates,
      audit
    }: {
      id: string;
      featureGates: Partial<PlainMessage<OrganizationFeatureGates>>;
      audit: PlainMessage<Audit>;
    }) =>
      apiFetch(`v1/aims/organizations/${id}/feature-gates`, {
        method: 'PATCH',
        body: JSON.stringify({ featureGates, audit })
      })
  });
}

export function useUpdateQuotas() {
  return useMutation<
    UpdateQuotasResponse,
    Error,
    {
      id: string;
      quota: Partial<PlainMessage<OrganizationQuota>>;
      audit: PlainMessage<Audit>;
    }
  >({
    mutationFn: async ({ id, quota, audit }) => {
      const res = await apiFetch<UpdateQuotasResponse>(`v1/aims/organizations/${id}/quotas`, {
        method: 'PUT',
        body: JSON.stringify({ quota, audit })
      });
      return res;
    }
  });
}

export function useToggleInstanceCreation() {
  return useMutation({
    mutationFn: ({ disabled, audit }: { disabled: boolean; audit: PlainMessage<Audit> }) =>
      apiFetch(`v1/aims/organizations/instance_creation`, {
        method: 'POST',
        body: JSON.stringify({ disabled, audit })
      })
  });
}

export function useDeleteUnpaidOrganization() {
  return useMutation({
    mutationFn: ({ id, audit }: { id: string; audit: PlainMessage<Audit> }) =>
      apiFetch(
        `v1/aims/organizations/${id}?audit.actor=${audit.actor}&audit.reason=${audit.reason}`,
        { method: 'DELETE', ignoreJSONReadFromResponse: true }
      )
  });
}

export function useGetInternalConfig(
  options?: Omit<UseQueryOptions<InternalConfig>, 'queryKey' | 'queryFn'>
) {
  return useQuery<InternalConfig>({
    queryKey: queryKeys.organizations.internalConfig().queryKey,
    queryFn: () =>
      apiFetch<GetInternalConfigResponse>('v1/aims/internal/config').then(
        (response) => response.config
      ),
    ...options
  });
}

export function useSetManuallyVerified() {
  return useMutation({
    mutationFn: ({
      id,
      verified,
      audit
    }: {
      id: string;
      verified: boolean;
      audit: PlainMessage<Audit>;
    }) =>
      apiFetch(`v1/aims/organizations/${id}/verified`, {
        method: 'POST',
        body: JSON.stringify({ verified, audit })
      })
  });
}

export function useSendNotification() {
  return useMutation({
    mutationFn: (body: SendNotificationRequest) =>
      apiFetch(`v1/aims/notification`, {
        method: 'POST',
        body: body.toJsonString(),
        ignoreJSONReadFromResponse: true
      })
  });
}

export function useListAuditLogs(
  orgId: string,
  filters: RawAuditFilters,
  options?: Omit<UseQueryOptions<ListAuditLogsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListAuditLogsResponse>({
    queryKey: queryKeys.organizations.auditLogs(orgId, filters).queryKey,
    queryFn: () =>
      apiFetch<ListAuditLogsResponse>(
        `v1/aims/organizations/${orgId}/audit-logs?${auditFilterTransformer.toSearchString(filters)}`
      ),
    enabled: !!orgId,
    ...options
  });
}

export function useGetKubeVisionUsage(
  orgId: string,
  startTime: string,
  endTime: string,
  options?: Omit<UseQueryOptions<GetKubeVisionUsageResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<GetKubeVisionUsageResponse>({
    queryKey: queryKeys.organizations.kubeVisionUsage(orgId, startTime, endTime).queryKey,
    queryFn: () =>
      apiFetch<GetKubeVisionUsageResponse>(
        `v1/aims/organizations/${orgId}/k8s/usage?startTime=${startTime}&endTime=${endTime}`
      ),
    enabled: !!orgId && !!startTime && !!endTime,
    ...options
  });
}

export function useListAIConversations(
  orgId: string,
  options?: Omit<UseQueryOptions<ListAIConversationsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListAIConversationsResponse>({
    queryKey: queryKeys.organizations.aiConversations(orgId).queryKey,
    queryFn: () => {
      return apiFetch(`v1/aims/organizations/${orgId}/ai/conversations`).then(
        ListAIConversationsResponse.fromJson
      );
    },
    enabled: !!orgId,
    ...options
  });
}

export function useGetAIConversation(
  orgId: string,
  id: string,
  options?: Omit<UseQueryOptions<GetAIConversationResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<GetAIConversationResponse>({
    queryKey: queryKeys.organizations.aiConversationDetail(orgId, id).queryKey,
    queryFn: () => {
      return apiFetch(`v1/aims/organizations/${orgId}/ai/conversations/${id}`).then(
        GetAIConversationResponse.fromJson
      );
    },
    enabled: !!orgId && !!id,
    ...options
  });
}

export function useListAvailablePlans(
  options?: Omit<UseQueryOptions<ListAvailablePlansResponse['plans']>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListAvailablePlansResponse['plans']>({
    queryKey: queryKeys.organizations.availablePlans().queryKey,
    queryFn: () => apiFetch<ListAvailablePlansResponse>(`v1/aims/plans`).then((res) => res.plans),
    ...options
  });
}

export function useUpdateOrganizationPlan() {
  return useMutation({
    mutationFn: (body: PlainMessage<UpdateOrganizationBillingPlanRequest>) =>
      apiFetch(`v1/aims/organizations/${body.organizationId}/plan`, {
        method: 'POST',
        body: JSON.stringify(body)
      })
  });
}

export function useListTeams(
  orgId: string,
  payload?: PaginationPaylod,
  options?: Omit<UseQueryOptions<ListTeamsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListTeamsResponse>({
    queryKey: queryKeys.organizations.teams(orgId, payload || { limit: 10, offset: 0 }).queryKey,
    queryFn: () =>
      apiFetch<ListTeamsResponse>(
        `v1/aims/organizations/${orgId}/teams?limit=${payload?.limit || 10}&offset=${payload?.offset || 0}`
      ),
    enabled: !!orgId,
    ...options
  });
}

export function useListTeamMembers(
  orgId: string,
  teamName: string,
  payload?: PaginationPaylod,
  options?: Omit<UseQueryOptions<ListTeamMembersResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListTeamMembersResponse>({
    queryKey: queryKeys.organizations.teamMembers(
      orgId,
      teamName,
      payload || { limit: 10, offset: 0 }
    ).queryKey,
    queryFn: () =>
      apiFetch<ListTeamMembersResponse>(
        `v1/aims/organizations/${orgId}/teams/${teamName}/members?limit=${payload?.limit || 10}&offset=${payload?.offset || 0}`
      ),
    enabled: !!orgId && !!teamName,
    ...options
  });
}

export function useListWorkspaces(
  orgId: string,
  payload: PaginationPaylod,
  options?: Omit<UseQueryOptions<ListWorkspacesResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListWorkspacesResponse>({
    queryKey: queryKeys.workspace.list(orgId, payload).queryKey,
    queryFn: () =>
      apiFetch<ListWorkspacesResponse>(
        `v1/aims/organizations/${orgId}/workspaces?limit=${payload?.limit}&offset=${payload?.offset}`
      ),
    enabled: !!orgId,
    ...options
  });
}

export function useGetWorkspace(
  orgId: string,
  workspaceId: string,
  options?: Omit<UseQueryOptions<GetWorkspaceResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<GetWorkspaceResponse>({
    queryKey: queryKeys.workspace.item(orgId, workspaceId).queryKey,
    queryFn: () =>
      apiFetch<GetWorkspaceResponse>(`v1/aims/organizations/${orgId}/workspaces/${workspaceId}`),
    enabled: !!orgId && !!workspaceId,
    ...options
  });
}

export function useGetWorkspaceMembers(
  orgId: string,
  workspaceId: string,
  options?: Omit<UseQueryOptions<ListWorkspaceMembersResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListWorkspaceMembersResponse>({
    queryKey: queryKeys.workspace.members(orgId, workspaceId).queryKey,
    queryFn: () =>
      apiFetch<ListWorkspaceMembersResponse>(
        `v1/aims/organizations/${orgId}/workspaces/${workspaceId}/members`
      ),
    enabled: !!orgId && !!workspaceId,
    ...options
  });
}

export function useGetWorkspaceCustomRoles(
  orgId: string,
  workspaceId: string,
  payload: PaginationPaylod,
  options?: Omit<UseQueryOptions<ListWorkspaceCustomRolesResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListWorkspaceCustomRolesResponse>({
    queryKey: queryKeys.workspace.customRoles(orgId, workspaceId, payload).queryKey,
    queryFn: () =>
      apiFetch<ListWorkspaceCustomRolesResponse>(
        `v1/aims/organizations/${orgId}/workspaces/${workspaceId}/custom-roles?limit=${payload?.limit}&offset=${payload?.offset}`
      ),
    enabled: !!orgId && !!workspaceId,
    ...options
  });
}

export function useGetOrganizationCustomRoles(
  orgId: string,
  payload: PaginationPaylod,
  options?: Omit<UseQueryOptions<ListOrganizationCustomRolesResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListOrganizationCustomRolesResponse>({
    queryKey: queryKeys.organizations.customRoles(orgId, payload).queryKey,
    queryFn: () =>
      apiFetch<ListOrganizationCustomRolesResponse>(
        `v1/aims/organizations/${orgId}/custom-roles?limit=${payload?.limit}&offset=${payload?.offset}`
      ),
    enabled: !!orgId,
    ...options
  });
}

export function useGetOrganizationUsers(
  orgId: string,
  options?: Omit<UseQueryOptions<ListOrganizationUsersResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ListOrganizationUsersResponse>({
    queryKey: queryKeys.organizations.users(orgId).queryKey,
    queryFn: () => apiFetch<ListOrganizationUsersResponse>(`v1/aims/organizations/${orgId}/users`),
    enabled: !!orgId,
    ...options
  });
}

export function useResetMFA() {
  return useMutation({
    mutationFn: (body: PlainMessage<ResetMFARequest>) =>
      apiFetch(`v1/aims/mfa/reset`, {
        method: 'POST',
        body: JSON.stringify(body)
      })
  });
}

export function useUpdateTrialExpiration() {
  return useMutation({
    mutationFn: ({
      organizationId,
      trialExpiration,
      audit
    }: {
      organizationId: string;
      trialExpiration: number;
      audit: Audit;
    }) =>
      apiFetch(`v1/aims/organizations/${organizationId}/trial`, {
        method: 'POST',
        body: JSON.stringify({ trialExpiration, audit }),
        ignoreJSONReadFromResponse: true
      })
  });
}
