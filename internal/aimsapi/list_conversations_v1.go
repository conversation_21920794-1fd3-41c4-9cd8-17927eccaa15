package aimsapi

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *AimsV1Server) ListAIConversations(ctx context.Context, req *organizationv1.ListAIConversationsRequest) (*organizationv1.ListAIConversationsResponse, error) {
	repoSet := client.NewRepoSet(s.db)
	orgID := req.GetOrganizationId()
	featureStatuses := s.featureSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.db, repoSet, featureStatuses, orgID, config.AIConfig{}, nil, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	// AIMS API doesn't support pagination yet, so we return all conversations
	conversations, count, err := aiSvc.ListConversations(ctx, nil, "", false,
		req.GetIncidentStatus(), req.GetApplication(), req.GetNamespace(), req.GetClusterId(),
		"", 0, 0)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListAIConversationsResponse{Conversations: conversations, Count: uint32(count)}, nil
}
