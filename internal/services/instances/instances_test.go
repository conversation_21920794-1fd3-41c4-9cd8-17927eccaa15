package instances

import (
	"context"
	"database/sql"
	"encoding/base64"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/version"
	akuitytesting "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/test/utils"
)

const (
	domainSuffix = "akuity.cloud"
	sampleCA     = `-----BEGIN CERTIFICATE-----
MIIDaDCCAlACCQCqFpjAE6YfLzANBgkqhkiG9w0BAQsFADB2MQswCQYDVQQGEwJV
UzENMAsGA1UECAwEVGVzdDENMAsGA1UEBwwEdGVzdDENMAsGA1UECgwEdGVzdDEN
MAsGA1UECwwEdGVzdDENMAsGA1UEAwwEdGVzdDEcMBoGCSqGSIb3DQEJARYNdGVz
dEB0ZXN0LmNvbTAeFw0yMzA1MDkyMDUzMTFaFw0yNDA1MDgyMDUzMTFaMHYxCzAJ
BgNVBAYTAlVTMQ0wCwYDVQQIDARUZXN0MQ0wCwYDVQQHDAR0ZXN0MQ0wCwYDVQQK
DAR0ZXN0MQ0wCwYDVQQLDAR0ZXN0MQ0wCwYDVQQDDAR0ZXN0MRwwGgYJKoZIhvcN
AQkBFg10ZXN0QHRlc3QuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEA4+sXBD1e4/+QWRgEN1SVcmW4ngK80HXJfTi0lpu25H4ccCRA0U2jaUZbpkx0
MpNe/BQ/C0iH1dUAvc58QuzIKE5Q8S4C6d8GP87vjt6Fbj7AZqdx2lfAiJZpWoAO
NReo0t9kPIcOyb2DAyA0y/pRxb+DwO9p/LKvezcnxp0HjFyXvGZfCDfRfuC0vYzj
WuqXa9y3Mi/pSEIxwy5WB+3VbHe5iXv1s5LUgHl5boNuOjOpO7f17oPDF4XlpmU8
6LLCWxsmXWplRDXRtWxHhw6V3LF13C+hX4N9Pia+5eG3aVsB+T4kfftK+vst+JSW
B1DKPeAYKq64zNMOokjOtKEziQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAOU3xt
M8l+6bovn7uPoYBJkCg04uillFBiiJTb8sb0FAnmuuQCxS6u49CveYmopVAP5lJ1
a4LxdC2d2DrsNoN5m7zzI5C71o/seg28VOW+0uZG4vVNesVdEWeVXA6rzclJE1vM
XoswdqY3uTjnlpm++nQdlQZU95ohzvm5AXH8dsCHMQ3O0JBiOPHulS9iRtCwyzSx
MF4lr6FbYaDRAPjynpU7Sa3RJgR3YM6JAk3l044DAvJy5WIKLnnof3WJVv43MVsS
H28pwlq2C73K7OS5ilJe6wm3up/El3+WNJ+C87IqP/Y23IyyQtDEmx6LRXsGwczc
20LBnc/0eoLzT2pW
-----END CERTIFICATE-----`
)

var (
	compatibility       models.ClusterCompatibility
	argocdNotifications models.ClusterArgoCDNotificationsControllerSpec
)

func init() {
	utils.InitializeTestDataKey()
}

type fakeUserInstances struct {
	instances []*ArgoCDInstance
}

func (f fakeUserInstances) GetSyncOperationStats(ctx context.Context, mods []qm.QueryMod, interval GroupByInterval, groupByField string) ([]SyncOperationStats, error) {
	return nil, nil
}

func (f fakeUserInstances) GetSyncOperationEvents(ctx context.Context, mods []qm.QueryMod) ([]*models.ArgoCDSyncOperation, error) {
	return nil, nil
}

func (f fakeUserInstances) GetSyncOperationEventsField(ctx context.Context, mods []qm.QueryMod, field string) ([]string, error) {
	return nil, nil
}

func (f fakeUserInstances) GetSyncOperationEventsCount(ctx context.Context, mods []qm.QueryMod) (int64, error) {
	return 0, nil
}

func (f fakeUserInstances) GetConfigurationSummary(ctx context.Context) (*InstancesConfigurationSummary, error) {
	return nil, nil
}

func (f fakeUserInstances) ListAll(context.Context) ([]*ArgoCDInstance, error) {
	return f.instances, nil
}

func (f fakeUserInstances) GetAIAssistantUsageStats(ctx context.Context, ids []string) (*AIAssistantUsageStats, error) {
	return nil, nil
}

func (f fakeUserInstances) GetByID(_ context.Context, id string) (*ArgoCDInstance, error) {
	if len(f.instances) == 0 {
		return nil, sql.ErrNoRows
	}
	return f.instances[0], nil
}

func (f fakeUserInstances) GetByName(_ context.Context, id string) (*ArgoCDInstance, error) {
	return nil, sql.ErrNoRows
}

type fakeTxBeginner struct{}

func (f *fakeTxBeginner) Commit() error {
	return nil
}

func (f *fakeTxBeginner) Rollback() error {
	return nil
}

func (f *fakeTxBeginner) Begin(ctx context.Context, opts ...database.TxOpt) (database.Commiter, error) {
	return f, nil
}

func (f *fakeTxBeginner) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	return nil, nil
}

func (f fakeUserInstances) GetSummary(ctx context.Context) (*InstancesSummary, error) {
	clusterCount := 0
	for _, inst := range f.instances {
		clusterCount += inst.ClusterCount
	}
	counts := &InstancesSummary{
		InstancesCount: len(f.instances),
		ClustersCount:  clusterCount,
	}
	return counts, nil
}

func (f fakeUserInstances) GetGlobalSummary(ctx context.Context) (*InstancesSummary, error) {
	clusterCount := 0
	for _, inst := range f.instances {
		clusterCount += inst.ClusterCount
	}
	counts := &InstancesSummary{
		InstancesCount: len(f.instances),
		ClustersCount:  clusterCount,
	}
	return counts, nil
}

func (f fakeUserInstances) GetInstanceApplicationCount(ctx context.Context, instanceID string) (int, error) {
	return 0, nil
}

func newService(repoSet *akuitytesting.InMemoryRepoSet, instances ...*ArgoCDInstance) (*Service, error) {
	log, err := logging.NewLogger()
	if err != nil {
		return nil, err
	}
	nameConfig := config.NameConfig{
		MinOrganizationNameLength: 4,
		MinInstanceNameLength:     3,
		MinClusterNameLength:      3,
	}
	org := &models.Organization{ID: "1", Name: "test-org"}
	err = org.SetOrgFeatureGates(&features.OrgFeatureGatesEnabled)
	if err != nil {
		return nil, err
	}
	repoSet.OrganizationsRepo.Items["1"] = org

	featSvc := features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceEnv, license.License{})
	svc := NewService(repoSet, &models.OrganizationUser{OrganizationID: "1"}, nil, &fakeTxBeginner{}, nil, nil, domainSuffix, &log, 1*time.Hour, nameConfig, featSvc, false)

	svc.instancesSource = &fakeUserInstances{instances: instances}
	return svc, nil
}

var (
	modelInstance = models.ArgoCDInstance{ID: "1", Name: "test1", OrganizationOwner: "1"}
	testInstance  = &ArgoCDInstance{ArgoCDInstance: modelInstance, ArgoCDInstanceConfig: models.ArgoCDInstanceConfig{InstanceID: "1"}}
)

func TestGetInstances(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	instances, err := svc.GetAllInstances(context.Background())
	require.NoError(t, err)

	require.Len(t, instances, 1)
	require.Equal(t, "1", instances[0].ID)
}

func TestGetInstanceClusters(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	repoSet.ArgoCDClustersRepo.SetItems(
		&models.ArgoCDCluster{ID: "1", InstanceID: "1", Name: "cluster1"},
		&models.ArgoCDCluster{ID: "2", InstanceID: "2", Name: "cluster2"},
	)

	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		assert.ElementsMatch(t, []qm.QueryMod{
			models.ArgoCDClusterWhere.InstanceID.EQ("1"),
			qm.OrderBy(fmt.Sprintf("%s asc", models.ArgoCDClusterColumns.Name)),
		}, mods)
		return []string{"1"}
	}

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	t.Run("InstanceClusters", func(t *testing.T) {
		clusters, err := svc.GetInstanceClusters(context.Background(), "1")
		require.NoError(t, err)

		require.Len(t, clusters, 1)
		require.Equal(t, "1", clusters[0].ID)
	})
}

func TestCreateAndUpdateInstanceUserOwner(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer io.Close(db)

	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	instance, err := svc.CreateInstance(context.Background(), "test1", "latest", "test1-description", 0, "", "")
	require.NoError(t, err)
	err = svc.Workspaces().Create(context.Background(), &models.Workspace{ID: "workspace-id", Name: "test1"})
	require.NoError(t, err)

	assert.Equal(t, "1", instance.OrganizationOwner)
	var instances []models.ArgoCDInstance
	for _, item := range repoSet.ArgoCDInstancesRepo.Items {
		instances = append(instances, *item)
	}
	assert.Equal(t, "test1", instances[0].Name)
	assert.Equal(t, "test1-description", instances[0].Description.String)

	// checking instance hardlimit
	_, err = svc.CreateInstance(context.Background(), "test1", "latest", "", 1, "", "")
	require.EqualError(t, err, InstanceHardLimitError)

	instance, err = svc.UpdateInstanceMetadata(context.Background(), instance.ID, "test2", "test2-description", "workspace-id")
	require.NoError(t, err)

	assert.Equal(t, "test2", instance.Name)
	assert.Equal(t, "test2-description", instance.Description.String)
	assert.Equal(t, "workspace-id", instance.WorkspaceID.String)
}

func TestCreateInstanceOrganizationOwner_HasMembership(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.OrganizationsRepo.Items["1"] = &models.Organization{ID: "1", Name: "test1", MaxClusters: 10, MaxInstances: 10}
	orgUser := &models.OrganizationUser{UserID: "1", OrganizationID: "1", OrganizationRole: "owner"}
	repoSet.OrganizationUsersRepo.Items["1"] = orgUser
	_, err = svc.CreateInstance(context.Background(), "test1", "latest", "", 0, "", "")
	require.NoError(t, err)
	svc.organizationID = orgUser.OrganizationID

	instances, err := repoSet.ArgoCDInstancesRepo.ListAll(context.Background())
	require.NoError(t, err)
	require.Len(t, instances, 1)
}

func TestOrgLimitEnforcement(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.OrganizationsRepo.Items["1"] = &models.Organization{ID: "1", Name: "test1", MaxClusters: 2, MaxInstances: 1}
	orgUser := &models.OrganizationUser{UserID: "1", OrganizationID: "1", OrganizationRole: "owner"}
	repoSet.OrganizationUsersRepo.Items["1"] = orgUser
	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return []string{}
	}
	svc.organizationID = "1"

	instance, err := svc.CreateInstance(context.Background(), "test1", "latest", "", 0, "", "")
	require.NoError(t, err)

	// should not allow 2nd instance creation
	_, err = svc.CreateInstance(context.Background(), "test1", "latest", "", 0, "", "")
	require.EqualError(t, err, OrgInstanceLimitError)

	// should allow 2 cluster creations
	_, err = svc.CreateCluster(context.Background(), instance.InstanceID, "cluster1", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	// set expiration time, grace period is set to 1 hour so (now - 61mins)
	// should enforce expired plan logic
	require.NoError(t, repoSet.OrganizationsRepo.Items["1"].SetOrgStatus(models.OrgStatus{
		Trial:      false,
		ExpiryTime: time.Now().Add(-60 * time.Minute).Unix(),
	}))

	// 2nd cluster should be allowed by limit but plan expired
	_, err = svc.CreateCluster(context.Background(), instance.InstanceID, "cluster2", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.EqualError(t, err, OrgPlanExpiredError)
}

func TestUpdateInstanceConfig(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.ArgoCDInstanceConfigsRepo.Items["1"] = &models.ArgoCDInstanceConfig{InstanceID: "1"}
	repoSet.ArgoCDInstanceConfigsRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return nil
	}
	repoSet.ArgoCDInstancesRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return []string{"1"}
	}

	t.Run("NoInstancePermissions", func(t *testing.T) {
		_, err := svc.UpdateInstanceConfig(context.Background(), "2", func(cfg *models.ArgoCDInstanceConfig) error {
			return nil
		})
		require.Error(t, err)
	})

	t.Run("SuccessfulUpdate", func(t *testing.T) {
		_, err := svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			return cfg.SetArgoCDConfigMap(&models.ArgoCDConfigMap{BannerContent: "updated"})
		})
		require.NoError(t, err)
		cfg := repoSet.ArgoCDInstanceConfigsRepo.Items["1"]

		assert.Equal(t, `{"admin.enabled":"false","helm.enabled":"true","kustomize.enabled":"true","ui.bannercontent":"updated"}`, string(cfg.ArgocdCM.JSON))
	})

	t.Run("InvalidConfig", func(t *testing.T) {
		_, err := svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			return cfg.SetArgoCDConfigMap(&models.ArgoCDConfigMap{BannerContent: "updated", Accounts: []models.Account{{Name: "s<3>@34"}}})
		})
		require.EqualError(t, err, "json: error calling MarshalJSON for type *models.ArgoCDConfigMap: invalid key accounts.s<3>@34, key should be valid for regex [-._a-zA-Z0-9]+ and should be < 254 characters")
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			return cfg.SetArgocdSecret(map[string]string{"sfsdf<df?sdf": "test"})
		})
		require.EqualError(t, err, "invalid key sfsdf<df?sdf, key should be valid for regex [-._a-zA-Z0-9]+ and should be < 254 characters")
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			cfg.Subdomain = "a"
			return nil
		})
		require.EqualError(t, err, "subdomain length must be >= 3 and <= 63")
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			cfg.Subdomain = "abc-"
			return nil
		})
		require.EqualError(t, err, "invalid subdomain")
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			cfg.Subdomain = "abcd"
			return nil
		})
		require.NoError(t, err)
		// valid dex config schema check
		caData := base64.StdEncoding.EncodeToString([]byte(sampleCA))
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			return cfg.SetArgoCDConfigMap(&models.ArgoCDConfigMap{DexConfig: fmt.Sprintf("connectors:\n  - type: github\n    name: GitHub\n    id: github\n    config:\n      clientID: $GITHUB_CLIENT_ID\n      clientSecret: $GITHUB_CLIENT_SECRET\n      loadAllGroups: false\n      useLoginAsID: false\n      redirectURI: https://1im7nan01mq4s11x.cd.akuity.cloud/api/dex/callback\n  - type: saml\n    name: SAML\n    id: saml\n    config:\n      ssoURL: ert\n      caData: %s\n      redirectURI: https://1im7nan01mq4s11x.cd.akuity.cloud/api/dex/callback\n      usernameAttr: ert\n      emailAttr: ert\n", caData)})
		})
		require.NoError(t, err)
		// invalid dex config schema check
		_, err = svc.UpdateInstanceConfig(context.Background(), "1", func(cfg *models.ArgoCDInstanceConfig) error {
			return cfg.SetArgoCDConfigMap(&models.ArgoCDConfigMap{DexConfig: "connectors:\n  - type: github\n    name: GitHub\n    id: github\n    config:\n      clientID: $GITHUB_CLIENT_ID\n      clientSecret: $GITHUB_CLIENT_SECRET\n      loadAllGroups: false\n      useLoginAsID: false\n      redirectURI: https://1im7nan01mq4s11x.cd.akuity.cloud/api/dex/callback\n  - type: saml\n    name: SAML\n    id: saml\n    config:\n      ca: str\n      redirectURI: https://1im7nan01mq4s11x.cd.akuity.cloud/api/dex/callback\n      usernameAttr: ert\n      emailAttr: ert\n"})
		})
		require.EqualError(t, err, "invalid dex config, error=(root): Must validate at least one schema (anyOf), connectors.1: Must validate at least one schema (anyOf), connectors.1.config: ssoURL is required")
	})
}

func TestDeleteCluster(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.ArgoCDInstancesRepo.SetItems(&models.ArgoCDInstance{ID: "1", OrganizationOwner: "org1", Name: "cluster1"})
	repoSet.ArgoCDClustersRepo.SetItems(&models.ArgoCDCluster{ID: "1", InstanceID: "1", Name: "cluster1"})

	require.NoError(t, svc.DeleteCluster(context.Background(), "1"))

	assert.NotNil(t, repoSet.ArgoCDClustersRepo.Items["1"].DeletionTimestamp)
}

func TestCreateCluster_FirstCluster(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	_, err = svc.CreateCluster(context.Background(), "1", "cluster1", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	clusters, err := repoSet.ArgoCDClustersRepo.ListAll(context.Background())
	require.NoError(t, err)
	assert.Len(t, clusters, 1)
	assert.Equal(t, "cluster1", clusters[0].Name)
	assert.Equal(t, 0, clusters[0].SequenceID)
}

func TestCreateCluster_ExistingClusterWithSequenceGaps(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()
	repoSet.ArgoCDClustersRepo.SetItems(
		&models.ArgoCDCluster{ID: "1", Name: "cluster1", SequenceID: 0, InstanceID: "1"},
		&models.ArgoCDCluster{ID: "3", Name: "cluster3", SequenceID: 2, InstanceID: "1"})
	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		if reflect.DeepEqual(mods, []qm.QueryMod{models.ArgoCDClusterWhere.Name.EQ("cluster4"), models.ArgoCDClusterWhere.InstanceID.EQ("1")}) {
			return nil
		}
		return []string{"1", "3"}
	}

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	_, err = svc.CreateCluster(context.Background(), "1", "cluster4", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, client.LatestAgentVersion, nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	nameToSequence := map[string]int{}
	for _, cluster := range repoSet.ArgoCDClustersRepo.Items {
		nameToSequence[cluster.Name] = cluster.SequenceID
	}
	assert.Equal(t, map[string]int{"cluster1": 0, "cluster4": 1, "cluster3": 2}, nameToSequence,
		"missing sequence id should be re-used")
}

func TestClusterCredentialRotation(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	cluster, err := svc.CreateCluster(context.Background(), "1", "cluster1", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)
	originalSpec, err := cluster.GetSpec()
	require.NoError(t, err)
	originalPrivateSpec, err := cluster.GetPrivateSpec()
	require.NoError(t, err)
	assert.Equal(t, 1, originalSpec.AgentRotationCount)
	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return []string{cluster.ID}
	}
	var mods []qm.QueryMod
	mods = append(mods, models.ArgoCDClusterWhere.Name.IN([]string{"cluster1"}))
	mods = append(mods, models.ArgoCDClusterWhere.InstanceID.EQ("1"))
	skipped, err := svc.BatchRotateClusterCredentials(context.Background(), mods...)
	require.NoError(t, err)
	require.Equal(t, skipped, []string{"cluster1"})

	status, err := cluster.GetStatus()
	require.NoError(t, err)
	status.ObservedRotationCount = null.IntFrom(1)
	require.NoError(t, cluster.SetStatus(status))
	require.NoError(t, repoSet.ArgoCDClustersRepo.Update(context.Background(), cluster))

	skipped, err = svc.BatchRotateClusterCredentials(context.Background(), mods...)
	require.NoError(t, err)
	require.Equal(t, skipped, []string{})

	clusters, err := repoSet.ArgoCDClustersRepo.ListAll(context.Background())
	require.NoError(t, err)
	assert.Len(t, clusters, 1)
	assert.Equal(t, "cluster1", clusters[0].Name)
	privateSpec, err := clusters[0].GetPrivateSpec()
	require.NoError(t, err)
	status, err = clusters[0].GetStatus()
	require.NoError(t, err)
	spec, err := clusters[0].GetSpec()
	require.NoError(t, err)
	assert.Equal(t, 2, spec.AgentRotationCount)
	assert.NotEqual(t, originalPrivateSpec.AgentPassword, privateSpec.AgentPassword)
}

func TestCreateCluster_ExistingClusterWithSequenceGapsFromBeginning(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	repoSet.ArgoCDClustersRepo.SetItems(&models.ArgoCDCluster{ID: "abc", Name: "cluster1", SequenceID: 2, InstanceID: "1"})
	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		if reflect.DeepEqual(mods, []qm.QueryMod{models.ArgoCDClusterWhere.Name.EQ("cluster2"), models.ArgoCDClusterWhere.InstanceID.EQ("1")}) {
			return nil
		}
		if reflect.DeepEqual(mods, []qm.QueryMod{models.ArgoCDClusterWhere.Name.EQ("cluster3"), models.ArgoCDClusterWhere.InstanceID.EQ("1")}) {
			return nil
		}
		var ids []string
		for id := range repoSet.ArgoCDClustersRepo.Items {
			ids = append(ids, id)
		}
		return ids
	}

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	_, err = svc.CreateCluster(context.Background(), "1", "cluster2", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)
	_, err = svc.CreateCluster(context.Background(), "1", "cluster3", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	nameToSequence := map[string]int{}
	for _, cluster := range repoSet.ArgoCDClustersRepo.Items {
		nameToSequence[cluster.Name] = cluster.SequenceID
	}
	assert.Equal(t, map[string]int{"cluster1": 2, "cluster2": 1, "cluster3": 0}, nameToSequence,
		"missing sequence id should be re-used")
}

func TestCreateCluster_WithReservedNameForIncluster(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	_, err = svc.CreateCluster(context.Background(), "1", inclusterName, "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.Error(t, err)
}

func TestCreateCluster_InstanceDefaults(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	instaceConfig := models.ArgoCDInstanceConfig{InstanceID: "1"}
	customization := models.ClusterCustomization{
		CustomImageRegistryArgoproj: "akuity-reg",
		AutoUpgradeDisabled:         false,
	}
	err := instaceConfig.SetSpec(models.InstanceConfigSpec{ClusterCustomizationDefaults: customization})
	require.NoError(t, err)

	instance := ArgoCDInstance{ArgoCDInstance: modelInstance, ArgoCDInstanceConfig: instaceConfig}
	svc, err := newService(repoSet, &instance)
	require.NoError(t, err)

	repoSet.ArgoCDClustersRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		if reflect.DeepEqual(mods, []qm.QueryMod{models.ArgoCDClusterWhere.Name.EQ("cluster1"), models.ArgoCDClusterWhere.InstanceID.EQ("1")}) {
			return nil
		}
		if reflect.DeepEqual(mods, []qm.QueryMod{models.ArgoCDClusterWhere.Name.EQ("cluster2"), models.ArgoCDClusterWhere.InstanceID.EQ("1")}) {
			return nil
		}
		var ids []string
		for id := range repoSet.ArgoCDClustersRepo.Items {
			ids = append(ids, id)
		}
		return ids
	}

	// cluster with all defaults
	cluster1, err := svc.CreateCluster(context.Background(), "1", "cluster1", "akuity", models.ClusterSizeSmall, nil, nil, "", false, false, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	assert.Equal(t, cluster1.AutoUpgradeDisabled, customization.AutoUpgradeDisabled)

	// cluster with partial defaults
	cluster2, err := svc.CreateCluster(context.Background(), "1", "cluster2", "akuity", models.ClusterSizeSmall, map[string]string{}, map[string]string{}, "", false, true, version.GetLatestAgentVersion(), nil, false, false, nil, false, false, false, nil, false, nil, "", compatibility, argocdNotifications)
	require.NoError(t, err)

	assert.Equal(t, cluster2.AutoUpgradeDisabled, true)
}

func TestPatchInstanceImageUpdaterSecret(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.ArgoCDInstanceConfigsRepo.Items["1"] = &models.ArgoCDInstanceConfig{InstanceID: "1"}
	repoSet.ArgoCDInstanceConfigsRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return nil
	}
	repoSet.ArgoCDInstancesRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return []string{"1"}
	}

	t.Run("Invalid secret key", func(t *testing.T) {
		testValue := "foo"
		err := svc.PatchInstanceImageUpdaterSecret(context.Background(), "1", map[string]*string{"https://test": &testValue})
		require.EqualError(t, err, "invalid key https://test, key should be valid for regex [-._a-zA-Z0-9]+ and should be < 254 characters")
	})
}

func TestPatchInstanceNotificationsSecret(t *testing.T) {
	repoSet := akuitytesting.NewInMemoryRepoSet()

	svc, err := newService(repoSet, testInstance)
	require.NoError(t, err)

	repoSet.ArgoCDInstanceConfigsRepo.Items["1"] = &models.ArgoCDInstanceConfig{InstanceID: "1"}
	repoSet.ArgoCDInstanceConfigsRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return nil
	}
	repoSet.ArgoCDInstancesRepo.FilterStub = func(mods ...qm.QueryMod) []string {
		return []string{"1"}
	}

	t.Run("Invalid secret key", func(t *testing.T) {
		testValue := "foo"
		err := svc.PatchInstanceNotificationsSecret(context.Background(), "1", map[string]*string{"https://test": &testValue})
		require.EqualError(t, err, "invalid key https://test, key should be valid for regex [-._a-zA-Z0-9]+ and should be < 254 characters")
	})
}

func TestVerifyBcryptPassword(t *testing.T) {
	require.NoError(t, verifyBcryptPassword("$2a$10$2b2cU8CPhOTaGrs1HRQuAueS7JTT5ZHsHSzYiFPm1leZck7Mc8T4W"))
	require.Error(t, verifyBcryptPassword("invalid bcrypt password"))
}

func TestIsAccountPassword(t *testing.T) {
	require.True(t, isAccountPasswordKey("admin.password"))
	require.True(t, isAccountPasswordKey("accounts.test.password"))
	require.False(t, isAccountPasswordKey("accounts.password"))
	require.False(t, isAccountPasswordKey("accounts.account1.passwordTime"))
}
