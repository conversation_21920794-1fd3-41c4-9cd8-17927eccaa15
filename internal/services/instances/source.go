package instances

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/models/models"

	_ "embed"
)

// instancesQuery returns argocd instances with configs and number of clusters filtered by user
//
//go:embed instances.sql
var instancesQuery string

var fromInstancesMod = qm.From(fmt.Sprintf("(%s) as instances", instancesQuery))

// ArgoCDInstance contains information about an ArgoCD instance, related config and stats data
type ArgoCDInstance struct {
	models.ArgoCDInstance       `boil:",bind"`
	models.ArgoCDInstanceConfig `boil:",bind"`

	// ClusterCount is a number of clusters in the instance
	ClusterCount int `boil:"clusters_count"`
	// NotIntegrationClusterCount is a number of clusters without directClusterSpec (not integration)
	NotIntegrationClusterCount int `boil:"not_integration_clusters_count"`
	// OwnerOrganizationName is an organization name that own instance
	OwnerOrganizationName string `boil:"owner_organization_name"`
}

// Source provides method to list Argo CD instances
type Source interface {
	ListAll(ctx context.Context) ([]*ArgoCDInstance, error)
	GetByID(ctx context.Context, id string) (*ArgoCDInstance, error)
	GetSummary(ctx context.Context) (*InstancesSummary, error)
	GetGlobalSummary(ctx context.Context) (*InstancesSummary, error)
	GetInstanceApplicationCount(ctx context.Context, instanceID string) (int, error)
	GetConfigurationSummary(ctx context.Context) (*InstancesConfigurationSummary, error)
	GetByName(ctx context.Context, name string) (*ArgoCDInstance, error)
	GetSyncOperationStats(ctx context.Context, mods []qm.QueryMod, interval GroupByInterval, groupByField string) ([]SyncOperationStats, error)
	GetSyncOperationEventsCount(ctx context.Context, mods []qm.QueryMod) (int64, error)
	GetSyncOperationEvents(ctx context.Context, mods []qm.QueryMod) ([]*models.ArgoCDSyncOperation, error)
	GetSyncOperationEventsField(ctx context.Context, mods []qm.QueryMod, field string) ([]string, error)
	GetAIAssistantUsageStats(ctx context.Context, ids []string) (*AIAssistantUsageStats, error)
}

type instancesSource struct {
	db          boil.ContextExecutor
	ownerFilter qm.QueryMod
}

func NewInstancesSource(db boil.ContextExecutor, orgID string) *instancesSource {
	return &instancesSource{db: db, ownerFilter: qm.And("organization_owner = ?", orgID)}
}

func (s *instancesSource) ListAll(ctx context.Context) ([]*ArgoCDInstance, error) {
	var instances []*ArgoCDInstance
	if err := models.NewQuery(fromInstancesMod, s.ownerFilter).Bind(ctx, s.db, &instances); err != nil {
		return nil, err
	}
	return instances, nil
}

func (s *instancesSource) GetByID(ctx context.Context, id string) (*ArgoCDInstance, error) {
	var instance ArgoCDInstance
	if err := models.NewQuery(fromInstancesMod, s.ownerFilter, qm.And("id=?", id)).Bind(ctx, s.db, &instance); err != nil {
		return nil, fmt.Errorf("failed to get instance with id '%s': %w", id, err)
	}
	return &instance, nil
}

func (s *instancesSource) GetByName(ctx context.Context, name string) (*ArgoCDInstance, error) {
	var instance ArgoCDInstance
	if err := models.NewQuery(fromInstancesMod, s.ownerFilter, qm.And("name=?", name)).Bind(ctx, s.db, &instance); err != nil {
		return nil, fmt.Errorf("failed to get instance with name '%s': %w", name, err)
	}
	return &instance, nil
}

type AIAssistantUsageStats struct {
	TotalConversations    int `boil:"total_conversations"`
	ResolvedConversations int `boil:"total_resolved_conversations"`
}

func (s *instancesSource) GetAIAssistantUsageStats(ctx context.Context, ids []string) (*AIAssistantUsageStats, error) {
	stats := &AIAssistantUsageStats{}
	convertedInstanceIds := make([]interface{}, len(ids))
	for idx, id := range ids {
		convertedInstanceIds[idx] = id
	}
	mod := []qm.QueryMod{
		fromInstancesMod,
		qm.Select(`
		coalesce(sum(cast(status_info->'openAI'->'conversations'->>'total' as integer)), 0) as total_conversations,
		coalesce(sum(cast(status_info->'openAI'->'conversations'->>'resolved' as integer)), 0) as total_resolved_conversations
	`),
	}
	if len(convertedInstanceIds) > 0 {
		mod = append(mod, qm.WhereIn(`id in ?`, convertedInstanceIds...))
	}
	err := models.NewQuery(mod...).Bind(ctx, s.db, stats)
	return stats, err
}

type InstancesSummary struct {
	InstancesCount    int         `boil:"instances_count" json:"instancesCount"`
	FirstInstanceID   null.String `boil:"first_instance_id" json:"firstInstanceID"`
	ClustersCount     int         `boil:"clusters_count" json:"clustersCount"`
	ApplicationsCount int         `boil:"applications_count" json:"applicationsCount"`
}

func (s *instancesSource) GetSummary(ctx context.Context) (*InstancesSummary, error) {
	counts := &InstancesSummary{}
	err := models.NewQuery(fromInstancesMod, s.ownerFilter,
		qm.Select(`
min(instances.id) as first_instance_id,
coalesce(sum(instances.clusters_count),0) as clusters_count,
count(*) as instances_count, 
coalesce(sum((status_info->'applicationsStatus'->>'count')::int),0) as applications_count`)).Bind(ctx, s.db, counts)
	return counts, err
}

func (s *instancesSource) GetGlobalSummary(ctx context.Context) (*InstancesSummary, error) {
	counts := &InstancesSummary{}
	err := models.NewQuery(fromInstancesMod,
		qm.Select(`
min(instances.id) as first_instance_id,
coalesce(sum(instances.clusters_count),0) as clusters_count,
count(*) as instances_count, 
coalesce(sum((status_info->'applicationsStatus'->>'count')::int),0) as applications_count`)).Bind(ctx, s.db, counts)
	return counts, err
}

func (s *instancesSource) GetInstanceApplicationCount(ctx context.Context, instanceID string) (int, error) {
	var count int
	err := models.NewQuery(
		fromInstancesMod,
		s.ownerFilter,
		qm.And("id = ?", instanceID),
		qm.Select("coalesce((status_info->'applicationsStatus'->>'count')::int, 0) as applications_count"),
	).QueryRowContext(ctx, s.db).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get application count for instance '%s': %w", instanceID, err)
	}

	return count, nil
}

type InstancesConfigurationSummary struct {
	Any                null.String `boil:"any" json:"any"`
	AccountConfigured  null.String `boil:"account_configured" json:"accountConfigured"`
	ClusterConnected   null.String `boil:"cluster_connected" json:"clusterConnected"`
	ApplicationCreated null.String `boil:"application_created" json:"applicationCreated"`
}

func (s *instancesSource) GetConfigurationSummary(ctx context.Context) (*InstancesConfigurationSummary, error) {
	summary := &InstancesConfigurationSummary{}
	summaryQuery := fmt.Sprintf(`
select
	i.name,
	i.organization_owner,
	case count(argocd_cm.key) when 0 then false else true end as account_configured,
	case count(c.id) when 0 then false else true end as cluster_connected,
	case coalesce(sum((i.status_info->'applicationsStatus'->> 'count')::int), 0) when 0 then false else true end as applications_created
from
	(%s) i
inner join
	argo_cd_instance_config cfg on i.id = cfg.instance_id
left join
		lateral jsonb_each_text(cfg.argocd_cm) as argocd_cm on ((key like 'accounts.%%' and value != 'false') or (key like 'users.anonymous.enabled' and value != 'false'))
left outer join
	argo_cd_cluster c on i.id = c.instance_id and c.status_agent_state is not null
group by
	i.name, i.organization_owner
`, instancesQuery)
	from := qm.From(fmt.Sprintf("(%s) as summary", summaryQuery))
	err := models.NewQuery(from, s.ownerFilter, qm.Select(`
min(name) as any,
min(case account_configured when true then name end) as account_configured,
min(case cluster_connected when true then name end) as cluster_connected,
min(case applications_created when true then name end) as application_created`)).Bind(ctx, s.db, summary)
	return summary, err
}

type GroupByInterval string

const (
	GroupByIntervalMinute GroupByInterval = "minute"
	GroupByIntervalHour   GroupByInterval = "hour"
	GroupByIntervalDay    GroupByInterval = "day"
	GroupByIntervalWeek   GroupByInterval = "week"
	GroupByIntervalMonth  GroupByInterval = "month"
	GroupByIntervalYear   GroupByInterval = "year"
)

func ParseGroupByInterval(interval string) (GroupByInterval, error) {
	switch GroupByInterval(interval) {
	case GroupByIntervalMinute, GroupByIntervalHour, GroupByIntervalDay, GroupByIntervalWeek, GroupByIntervalMonth, GroupByIntervalYear:
		return GroupByInterval(interval), nil
	default:
		return "", fmt.Errorf("invalid group by interval: %s", interval)
	}
}

type SyncOperationStats struct {
	IntervalStart time.Time          `json:"intervalStart"`
	CountMap      map[string]uint32  `json:"countMap"`
	AverageMap    map[string]float32 `json:"averageMap"`
}

type syncResultRow struct {
	Timestamp time.Time   `boil:"timestamp"`
	Count     uint32      `boil:"count"`
	Item      null.String `boil:"result_phase"`
	Average   float32     `boil:"avg"`
	Rank      uint32      `boil:"rank"`
}

func (s *instancesSource) GetSyncOperationStats(ctx context.Context, mods []qm.QueryMod, interval GroupByInterval, groupByField string) ([]SyncOperationStats, error) {
	mods = append(mods,
		qm.Select("sync.*", "CASE WHEN sync.count > 1 THEN sync.duration*sync.count ELSE extract(epoch from sync.end_time - sync.start_time) END as final_duration"),
		qm.From("argo_cd_sync_operation as sync"),
	)

	query, args := queries.BuildQuery(models.NewQuery(mods...))
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as sync", query)),
		qm.Select(fmt.Sprintf("date_trunc('%s', sync.end_time) as timestamp", interval),
			"sum(sync.final_duration)/sum(sync.count) as avg",
			fmt.Sprintf("%v as field", groupByField),
			"sum(sync.count) as count"),
		qm.GroupBy(fmt.Sprintf("timestamp, %v", groupByField)),
		qm.OrderBy("timestamp")),
	)
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as res1", query)), qm.Select("res1.*", "rank() over ( partition by timestamp order by count desc)")))
	query = strings.ReplaceAll(query, ";", "")
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as res", query)), qm.Select("*"), qm.Where("res.rank<=5")))

	rows, err := queries.Raw(query, args...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func(rows *sql.Rows) { _ = rows.Close() }(rows)

	var stats []SyncOperationStats
	for rows.Next() {
		row := syncResultRow{}
		if err := rows.Scan(&row.Timestamp, &row.Average, &row.Item, &row.Count, &row.Rank); err != nil {
			return nil, err
		}
		if len(stats) == 0 || stats[len(stats)-1].IntervalStart != row.Timestamp {
			stats = append(stats, SyncOperationStats{IntervalStart: row.Timestamp, CountMap: map[string]uint32{}, AverageMap: map[string]float32{}})
		}
		stats[len(stats)-1].CountMap[row.Item.String] = row.Count
		stats[len(stats)-1].AverageMap[row.Item.String] = row.Average
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return stats, nil
}

func (s *instancesSource) GetSyncOperationEvents(ctx context.Context, mods []qm.QueryMod) ([]*models.ArgoCDSyncOperation, error) {
	events := []*models.ArgoCDSyncOperation{}
	mods = append(mods, qm.Select("sync.*"),
		qm.From("argo_cd_sync_operation as sync"),
		qm.OrderBy("sync.start_time desc"))
	if err := models.NewQuery(mods...).Bind(ctx, s.db, &events); err != nil {
		return nil, err
	}
	return events, nil
}

func (s *instancesSource) GetSyncOperationEventsCount(ctx context.Context, mods []qm.QueryMod) (int64, error) {
	count := 0
	mods = append(mods, qm.Select("count(*)"),
		qm.From("argo_cd_sync_operation as sync"))
	if err := models.NewQuery(mods...).QueryRowContext(ctx, s.db).Scan(&count); err != nil {
		return 0, err
	}
	return int64(count), nil
}

func (s *instancesSource) GetSyncOperationEventsField(ctx context.Context, mods []qm.QueryMod, field string) ([]string, error) {
	mods = append(mods, qm.Distinct(fmt.Sprintf("%v as field", field)),
		qm.From("argo_cd_sync_operation as sync"), qm.Where(fmt.Sprintf("%v is not null", field)))
	sqlRows, err := models.NewQuery(mods...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func() { _ = sqlRows.Close() }()

	result := []string{}
	for sqlRows.Next() {
		res := ""
		if err := sqlRows.Scan(&res); err != nil {
			return nil, err
		}
		result = append(result, res)
	}

	if err := sqlRows.Err(); err != nil {
		return nil, err
	}

	return result, nil
}
