package instances

import (
	"context"
	"database/sql"
	"fmt"
	"net"
	"net/http"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/yaml"

	agentClient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

var (
	configManagementPluginRegex = regexp.MustCompile(`^[-._a-zA-Z0-9]+$`)
	subdomainRegex              = regexp.MustCompile(`^[a-z0-9]([a-z0-9-]*[a-z0-9])?$`)
	maxSubdomainLength          = 63
	minSubdomainLength          = 3
	// inclusterName is reserved for incluster server address feature.
	inclusterName          = "in-cluster"
	ProxyPortRangeStart    = 1030
	ProxyPortRangeEnd      = 49151
	PortsPerCluster        = 10
	maxClusterNameLength   = 50
	maxInstanceNameLength  = 50
	maxNamespaceNameLength = 63
	// MaxClusterSequenceID is used to calculate the repo server port. The total available user range is 1024-49151.
	// We are using 1024, 1025 for chisel proxy so cluster ports range is 1030-49151 ( starting from round number )
	// We are reserving 10 ports per cluster so max cluster sequence id is 4811 ((49151-1030) / 10 - 1).
	MaxClusterSequenceID = (ProxyPortRangeEnd-ProxyPortRangeStart)/PortsPerCluster - 1
)

const (
	MaxPasswordLength = 32
	MinPasswordLength = 8

	DefaultClusterNamespace = "akuity"
	DefaultArgoCDNamespace  = "argocd"
)

const (
	InstanceHardLimitError    = "Sorry, due to the high demand, we have run out of capacity for our free trial tier. <NAME_EMAIL> if you'd like to be notified as soon as we have more room or have an urgent ask."
	OrgInstanceLimitError     = "organization has reached the maximum number of allowed instances"
	LicenseInstanceLimitError = "product has reached the maximum number of allowed instances as per license"
	OrgPlanExpiredError       = "organization plan has expired"
	OrgInstanceShardError     = "organization doesn't support that shard"
)

func InstancesWhereIdInMod(ids ...string) qm.QueryMod {
	if len(ids) == 0 || ids[0] == accesscontrol.ResourceAny {
		return nil
	}
	convertedIds := make([]interface{}, len(ids))
	for idx, id := range ids {
		convertedIds[idx] = id
	}
	return qm.WhereIn(fmt.Sprintf(`%s in ?`, models.ArgoCDInstanceColumns.ID), convertedIds...)
}

func (s *Service) validateClusterSize(clusterSize models.ClusterSize) error {
	switch clusterSize {
	case models.ClusterSizeSmall, models.ClusterSizeMedium, models.ClusterSizeLarge, models.ClusterSizeAuto:
		return nil
	}
	return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("invalid cluster size: %s", clusterSize))
}

func (s *Service) validateClusterName(name string) error {
	if name == inclusterName {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("%q is a reserved name. please choose a different cluster name.", inclusterName))
	}
	return nil
}

func (s *Service) validateResourceSettings(cm *models.ArgoCDConfigMap) error {
	type FilteredResource struct {
		APIGroups []string `json:"apiGroups,omitempty"`
		Kinds     []string `json:"kinds,omitempty"`
		Clusters  []string `json:"clusters,omitempty"`
	}

	fr := make([]FilteredResource, 0)
	if err := yaml.Unmarshal([]byte(cm.ResourceInclusions), &fr); err != nil {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("failed to parse resource inclusions: %v", err))
	}
	if err := yaml.Unmarshal([]byte(cm.ResourceExclusions), &fr); err != nil {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("failed to parse resource exclusions: %v", err))
	}
	return nil
}

func (s *Service) validateRbacConfig(config *models.ArgoCDRbacConfigMap) error {
	if config.PolicyCSV == "" {
		return nil
	}

	for _, policy := range config.OverlayPolicies {
		if err := argocd.ValidatePolicy(policy.Policy, argocd.CasbinModel); err != nil {
			return err
		}
	}

	return argocd.ValidatePolicy(config.PolicyCSV, argocd.CasbinModel)
}

type Service struct {
	client.RepoSet
	txBeginner       database.TxBeginner
	db               boil.ContextExecutor
	instancesSource  Source
	instancesWatcher database.Watcher[events.InstanceEvent]
	clustersWatcher  database.Watcher[events.ClusterEvent]
	domainSuffix     string
	featSvc          features.Service

	log                   *logr.Logger
	organizationID        string
	nameConfig            config.NameConfig
	gracePeriodDuration   time.Duration
	redisTunnelingDefault bool
}

type Option func(*Service)

func WithLogger(logger logr.Logger) Option {
	return func(s *Service) {
		s.log = &logger
	}
}

func WithClusterWatcher(clustersWatcher database.Watcher[events.ClusterEvent]) Option {
	return func(s *Service) {
		s.clustersWatcher = clustersWatcher
	}
}

func WithInstanceWatcher(instancesWatcher database.Watcher[events.InstanceEvent]) Option {
	return func(s *Service) {
		s.instancesWatcher = instancesWatcher
	}
}

func WithOrganizationScope(orgID string) Option {
	return func(s *Service) {
		s.organizationID = orgID
		s.instancesSource = NewInstancesSource(s.db, orgID)
	}
}

// WithRedisTunneling enables or disables redis tunneling by default for anything created with this service.
func WithRedisTunneling(enable bool) Option {
	return func(s *Service) {
		s.redisTunnelingDefault = enable
	}
}

func NewServiceWithOptions(db *sql.DB, domainSuffix string, featSvc features.Service, opts ...Option) *Service {
	txDB, txBeginner := database.WithTxBeginner(db)
	rs := client.NewRepoSet(txDB)
	svc := &Service{
		RepoSet:      rs,
		db:           txDB,
		txBeginner:   txBeginner,
		domainSuffix: domainSuffix,
		featSvc:      featSvc,
	}
	for _, opt := range opts {
		opt(svc)
	}
	if svc.log == nil {
		nopLogger := logr.Discard()
		svc.log = &nopLogger
	}
	return svc
}

func NewService(
	r client.RepoSet,
	orgContext *models.OrganizationUser,
	db boil.ContextExecutor,
	txBeginner database.TxBeginner,
	instancesWatcher database.Watcher[events.InstanceEvent],
	clustersWatcher database.Watcher[events.ClusterEvent],
	domainSuffix string,
	log *logr.Logger,
	gracePeriodDuration time.Duration,
	nameConfig config.NameConfig,
	featSvc features.Service,
	redisTunnelingDefault bool,
) *Service {
	var orgID string
	if orgContext != nil {
		orgID = orgContext.OrganizationID
	}
	return &Service{
		RepoSet:               r,
		instancesSource:       NewInstancesSource(db, orgID),
		instancesWatcher:      instancesWatcher,
		clustersWatcher:       clustersWatcher,
		organizationID:        orgID,
		domainSuffix:          domainSuffix,
		txBeginner:            txBeginner,
		featSvc:               featSvc,
		log:                   log,
		nameConfig:            nameConfig,
		gracePeriodDuration:   gracePeriodDuration,
		redisTunnelingDefault: redisTunnelingDefault,
	}
}

func (s *Service) GetInstanceByID(ctx context.Context, id string) (*ArgoCDInstance, error) {
	return s.instancesSource.GetByID(ctx, id)
}

func (s *Service) GetInstanceByName(ctx context.Context, name string) (*ArgoCDInstance, error) {
	return s.instancesSource.GetByName(ctx, name)
}

func (s *Service) GetAllInstances(ctx context.Context) ([]*ArgoCDInstance, error) {
	return s.instancesSource.ListAll(ctx)
}

func (s *Service) WatchInstances(ctx context.Context, existing []*ArgoCDInstance, filter func(e events.InstanceEvent) bool) (<-chan database.Event[*ArgoCDInstance], error) {
	items := s.instancesWatcher.Subscribe(ctx, filter)
	res := make(chan database.Event[*ArgoCDInstance])
	go func() {
		for _, instance := range existing {
			res <- database.Event[*ArgoCDInstance]{Type: events.TypeAdded, Item: instance}
		}
		for e := range items {
			next := database.Event[*ArgoCDInstance]{Type: e.Type}
			if e.Type == events.TypeDeleted {
				next.Item = &ArgoCDInstance{ArgoCDInstance: models.ArgoCDInstance{ID: e.ID}}
			} else {
				instance, err := s.instancesSource.GetByID(ctx, e.ID)
				if err != nil {
					continue
				}
				next.Item = instance
			}
			res <- next
		}
		close(res)
	}()
	return res, nil
}

func (s *Service) UpdateInstance(ctx context.Context, instanceID, name, description, workspaceID string, configMapUpdater func(cfg *models.ArgoCDInstanceConfig) error) (*ArgoCDInstance, error) {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	if _, err := s.UpdateInstanceMetadata(txCtx, instanceID, name, description, workspaceID); err != nil {
		return nil, err
	}

	instance, err := s.UpdateInstanceConfig(txCtx, instanceID, configMapUpdater)
	if err != nil {
		return nil, err
	}

	return instance, tx.Commit()
}

func (s *Service) UpdateInstanceMetadata(ctx context.Context, instanceID, name, description, workspaceID string) (*ArgoCDInstance, error) {
	instance, err := s.GetInstanceByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	columnsToUpdate := []string{}
	if name != "" && instance.Name != name {
		instance.Name = name

		if err := validator.ValidateResourceName(instance.Name, s.nameConfig.MinInstanceNameLength, maxInstanceNameLength); err != nil {
			return nil, err
		}

		if _, err := s.instancesSource.GetByName(ctx, instance.Name); err == nil {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("instance with name '%s' already exists", instance.Name))
		}

		columnsToUpdate = append(columnsToUpdate, models.ArgoCDInstanceColumns.Name)
	}

	if description != instance.Description.String {
		if err := validator.ValidateResourceDescription(description, 255); err != nil {
			return nil, err
		}

		instance.Description = null.StringFrom(description)

		columnsToUpdate = append(columnsToUpdate, models.ArgoCDInstanceColumns.Description)
	}

	if instance.WorkspaceID.String != workspaceID && workspaceID != "" {
		instance.WorkspaceID = null.StringFrom(workspaceID)
		columnsToUpdate = append(columnsToUpdate, models.ArgoCDInstanceColumns.WorkspaceID)
	}

	if len(columnsToUpdate) > 0 {
		if err := s.ArgoCDInstances().Update(ctx, &models.ArgoCDInstance{
			ID:          instance.ID,
			Name:        instance.Name,
			Description: instance.Description,
			WorkspaceID: instance.WorkspaceID,
		}, columnsToUpdate...); err != nil {
			return nil, err
		}
	}

	return instance, nil
}

func (s *Service) updateInstanceConfig(ctx context.Context, instanceID string, updater func(cfg *models.ArgoCDInstanceConfig) error) (*ArgoCDInstance, error) {
	instanceConfig, err := s.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	unchangedConfig := instanceConfig.DeepCopy()
	if err := updater(instanceConfig); err != nil {
		return nil, err
	}

	newCm, err := instanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}

	oldCm, err := unchangedConfig.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}

	newDeepLinks := len(newCm.ApplicationLinks) + len(newCm.ProjectLinks) + len(newCm.ResourceLinks)

	deepLinkChanged := models.IsDeepLinksChanged(oldCm.ApplicationLinks, newCm.ApplicationLinks) ||
		models.IsDeepLinksChanged(oldCm.ProjectLinks, newCm.ProjectLinks) ||
		models.IsDeepLinksChanged(oldCm.ResourceLinks, newCm.ResourceLinks)

	if newDeepLinks > 0 && deepLinkChanged {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdDeepLinks().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "argo cd deep links feature is not enabled")
		}
	}

	if (newCm.OidcConfig != "" ||
		newCm.DexConfig != "") &&
		(oldCm.DexConfig != newCm.DexConfig || oldCm.OidcConfig != newCm.OidcConfig) {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdSso().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "argo cd sso feature is not enabled")
		}
	}

	oldSpec, err := unchangedConfig.GetSpec()
	if err != nil {
		return nil, err
	}
	newSpec, err := instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}
	if oldSpec.MultiClusterK8sDashboardEnabled && !newSpec.MultiClusterK8sDashboardEnabled {
		if err = s.ArgoCDClusterK8sObjects(
			models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		).DeleteAll(ctx); err != nil {
			return nil, err
		}
		clusters, err := s.GetInstanceClusters(ctx, instanceID)
		if err != nil {
			return nil, err
		}
		for _, cluster := range clusters {
			clusterSpec, err := cluster.GetSpec()
			if err != nil {
				return nil, err
			}
			clusterSpec.MultiClusterK8SDashboardEnabled = false
			if err = cluster.SetSpec(*clusterSpec); err != nil {
				return nil, err
			}
			cluster.StatusK8SInfo = null.JSONFrom(nil)
			if err = s.ArgoCDClusters().Update(ctx, cluster, models.ArgoCDClusterColumns.Spec, models.ArgoCDClusterColumns.StatusK8SInfo); err != nil {
				return nil, err
			}
		}
	}

	if !reflect.DeepEqual(newSpec.AppsetPolicy, oldSpec.AppsetPolicy) {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetApplicationSetController().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "application set controller feature is not enabled")
		}
	}

	if len(oldSpec.AppsetPlugins) > 0 &&
		len(newSpec.AppsetPlugins) > 0 &&
		!reflect.DeepEqual(newSpec.AppsetPlugins, oldSpec.AppsetPlugins) {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetApplicationSetController().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "application set controller feature is not enabled")
		}
	}

	if equals, err := instanceConfig.Equals(unchangedConfig); err != nil {
		return nil, err
	} else if !equals {
		columns := sets.New[string](models.ArgoCDInstanceConfigAllColumns...).
			Delete(models.ArgoCDInstanceConfigColumns.PrivateSpec, models.ArgoCDInstanceConfigColumns.InternalSpec)
		if err := s.ArgoCDInstanceConfigs().Update(ctx, instanceConfig, columns.UnsortedList()...); err != nil {
			return nil, err
		}
	}
	return s.instancesSource.GetByID(ctx, instanceID)
}

func normalizeBasePath(basePath string) (string, error) {
	basePath = strings.Trim(basePath, "/")
	if len(strings.Split(basePath, "/")) > 1 {
		return "", errorsutil.NewAPIStatus(http.StatusBadRequest, "basepath must be a single path segment")
	}
	return basePath, nil
}

func (s *Service) UpdateInstanceConfig(ctx context.Context, instanceID string, updater func(cfg *models.ArgoCDInstanceConfig) error) (*ArgoCDInstance, error) {
	return s.updateInstanceConfig(ctx, instanceID, func(cfg *models.ArgoCDInstanceConfig) error {
		existingCM, err := cfg.GetArgoCDConfigMap()
		if err != nil {
			return err
		}

		existingSpec, err := cfg.GetSpec()
		if err != nil {
			return err
		}

		if err := updater(cfg); err != nil {
			return err
		}

		if cfg.Basepath, err = normalizeBasePath(cfg.Basepath); err != nil {
			return err
		}
		if err := s.validateHostname(ctx, instanceID, cfg.Subdomain, cfg.Basepath); err != nil {
			return err
		}

		spec, err := cfg.GetSpec()
		if err != nil {
			return err
		}

		if spec.IsGitDelegateInControlPlane() || spec.IsGitDelegateInManagedCluster() {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdFlexibleArchitecture().Enabled() {
				return status.Error(codes.PermissionDenied, "argo cd flexible architecture feature is not enabled")
			}
		}

		if spec.DeclarativeManagementEnabled &&
			existingSpec.DeclarativeManagementEnabled != spec.DeclarativeManagementEnabled {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetAppOfApps().Enabled() {
				return status.Error(codes.PermissionDenied, "app of apps feature is not enabled")
			}
		}

		// only managed or controlplane should be set
		if spec.IsGitDelegateInControlPlane() && spec.IsGitDelegateInManagedCluster() {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "cannot have both managed cluster and controlplane set at the same time for repo server")
		}
		if spec.IsImageUpdaterInManagedCluster() && spec.IsImageUpdaterInControlPlane() {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "cannot have both managed cluster and controlplane set at the same time for image updater")
		}

		// check if control plane delegation is allowed
		if !config.IsSelfHosted && (spec.IsGitDelegateInControlPlane() || spec.IsImageUpdaterInControlPlane()) {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "repo server and image updater cannot run in controlplane")
		}

		if spec.IsGitDelegateInManagedCluster() && spec.RepoServerDelegate.ManagedCluster.ClusterName == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "repo server managed cluster cannot be empty")
		}
		if spec.IsImageUpdaterInManagedCluster() && spec.ImageUpdaterDelegate.ManagedCluster.ClusterName == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "image updater managed cluster cannot be empty")
		}
		if spec.IsAppSetInManagedCluster() && spec.AppSetDelegate.ManagedCluster.ClusterName == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "app set controller managed cluster cannot be empty")
		}

		if err := ValidateIpAllowlist(spec.IpAllowlist); err != nil {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		if err := validateHostAliases(spec.HostAliases); err != nil {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		if err := s.validateFQDN(ctx, instanceID, cfg.FQDN, cfg.Basepath); err != nil {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		if err := s.validateAppSetPlugins(spec.AppsetPlugins); err != nil {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		if err := s.validateKubeVisionSettings(spec.KubeVisionConfig); err != nil {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		// css is managed separately
		spec.Css = existingSpec.Css
		if err := cfg.SetSpec(spec); err != nil {
			return err
		}

		if spec.HasArgocdExtension() && spec.HasArgocdExtension() != existingSpec.HasArgocdExtension() {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetAkuityArgocdExtensions().Enabled() {
				return status.Error(codes.PermissionDenied, "akuity argo cd extensions feature is not enabled")
			}
		}

		if spec.MultiClusterK8sDashboardEnabled && existingSpec.MultiClusterK8sDashboardEnabled != spec.MultiClusterK8sDashboardEnabled {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetMultiClusterK8SDashboard().Enabled() {
				return status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
			}
		}

		cm, err := cfg.GetArgoCDConfigMap()
		if err != nil {
			return err
		}
		// accounts and resources customizations are managed separately
		cm.Accounts = existingCM.Accounts
		cm.ResourceCustomizations = existingCM.ResourceCustomizations
		cm.CrossplaneExtension = spec.CrossplaneExtension
		if err := cfg.SetArgoCDConfigMap(cm); err != nil {
			return err
		}

		rbacCM, err := cfg.GetArgoCDRbacConfigMap()
		if err != nil {
			return err
		}
		if err := s.validateRbacConfig(rbacCM); err != nil {
			return err
		}

		if err := s.validateResourceSettings(cm); err != nil {
			return err
		}

		return nil
	})
}

func (s *Service) validateAppSetPlugins(plugins []models.AppsetPlugins) error {
	for _, plugin := range plugins {
		if !strings.HasPrefix(plugin.Name, "plugin-") {
			return fmt.Errorf("appset plugin name must start with 'plugin-'")
		}
		if err := validator.ValidateResourceName(plugin.Name, 8, 50); err != nil {
			return fmt.Errorf("appset invalid plugin name %v, %v", plugin.Name, err.Error())
		}
		if plugin.Token == "" {
			return fmt.Errorf("appset plugin token cannot be empty")
		}
		if plugin.BaseUrl == "" {
			return fmt.Errorf("appset plugin base url cannot be empty")
		}
	}
	return nil
}

func (s *Service) CreateInstance(ctx context.Context, name, version, description string, hardLimit int64, shard, workspaceId string) (*ArgoCDInstance, error) {
	if version == "" {
		version = "latest"
	}
	instance := &models.ArgoCDInstance{Name: name, Description: null.StringFrom(description), Shard: shard}

	if err := validator.ValidateResourceName(instance.Name, s.nameConfig.MinInstanceNameLength, maxInstanceNameLength); err != nil {
		return nil, err
	}

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	if _, err := s.instancesSource.GetByName(txCtx, instance.Name); err == nil {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("instance with name '%s' already exists", instance.Name))
	}

	quota, err := s.featSvc.GetOrgQuotas(ctx, s.organizationID)
	if err != nil {
		return nil, err
	}

	statuses := s.featSvc.GetFeatureStatuses(ctx, &s.organizationID)

	shards := statuses.GetShards()
	if len(shards) == 1 {
		// force shard if only one specified
		shard = shards[0]
		instance.Shard = shard
	}
	if !validateShards(shards, shard) {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgInstanceShardError)
	}

	if config.IsSelfHosted {
		licenseData := config.GetLicense()
		count, err := s.ArgoCDInstances().Count(ctx)
		if err != nil {
			return nil, err
		}
		if licenseData.Instances > 0 && licenseData.Instances < uint64(count+1) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, LicenseInstanceLimitError)
		}
	} else {
		// if no hardlimit set skip check
		if hardLimit != 0 {
			count, err := s.ArgoCDInstances().Count(ctx)
			if err != nil {
				return nil, err
			}
			if hardLimit <= count {
				s.log.Info("instance hardlimit hit!", "org_id", s.organizationID)
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, InstanceHardLimitError)
			}
		}

		res, err := s.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.OrganizationOwner.EQ(s.organizationID)).Count(txCtx)
		if err != nil {
			return nil, err
		}

		org, err := s.Organizations().GetByID(txCtx, s.organizationID)
		if err != nil {
			return nil, err
		}

		maxInstances := quota.MaxInstances
		if org.MaxInstances == 0 || org.MaxInstances > int(maxInstances) {
			maxInstances = int64(org.MaxInstances) // take the more permissive limit
		}
		// TODO(billing-neg1): change this check when we begin representing unlimited as -1
		if maxInstances != 0 && maxInstances < res+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgInstanceLimitError)
		}

		status, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if status != nil && !status.Trial && status.ExpiryTime > 0 &&
			time.Now().After(time.Unix(status.ExpiryTime, 0).Add(s.gracePeriodDuration)) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgPlanExpiredError)
		}
	}

	instance.OrganizationOwner = s.organizationID
	if workspaceId != "" {
		instance.WorkspaceID = null.StringFrom(workspaceId)
	}

	if err := s.RepoSet.ArgoCDInstances().Create(txCtx, instance); err != nil {
		return nil, err
	}
	instanceConfig, err := s.generateInstanceConfig(ctx, instance.ID, version)
	if err != nil {
		return nil, err
	}
	if err := s.RepoSet.ArgoCDInstanceConfigs().Create(txCtx, instanceConfig); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return s.GetInstanceByID(ctx, instance.ID)
}

func (s *Service) generateInstanceConfig(ctx context.Context, instanceID, version string) (*models.ArgoCDInstanceConfig, error) {
	instanceConfig := &models.ArgoCDInstanceConfig{
		InstanceID: instanceID,
		Version:    null.StringFrom(version),
		Subdomain:  instanceID,
	}

	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return nil, err
	}
	k3sDefaultValues := controlplane.NewDataValuesK3s()
	if k3sDefaultValues.Image != nil {
		internalSpec.K3sImage = *k3sDefaultValues.Image
	}

	// Initial new tenant Autoscaler types
	internalSpec.K3sType = "k3s.small"
	internalSpec.K3sProxyType = "k3s-proxy.small"
	internalSpec.ApplicationControllerType = "application-controller.small"
	internalSpec.RedisType = "redis.small"

	if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
		return nil, err
	}

	// set spec
	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}

	appOfAppsEnabled := s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetAppOfApps().Enabled()

	appsetDisabled := !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetApplicationSetController().Enabled()

	// enable in-cluster controller (allows app-of-apps)
	spec.DeclarativeManagementEnabled = appOfAppsEnabled
	// disable app-set controller if feature is disabled
	spec.AppsetDisabled = appsetDisabled

	// enable KubeVision by default if feature is available for the organization
	kubeVisionEnabled := s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetMultiClusterK8SDashboard().Enabled()
	spec.MultiClusterK8sDashboardEnabled = kubeVisionEnabled

	// enable AI Support Engineer by default if feature is available for the organization
	aiSupportEngineerEnabled := s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetAiSupportEngineer().Enabled()
	spec.AkuityIntelligenceExtension.AISupportEngineerEnabled = aiSupportEngineerEnabled

	spec.ClusterCustomizationDefaults.RedisTunneling = s.redisTunnelingDefault

	spec.AppReconciliationsRateLimiting = &models.AppReconciliationsRateLimiting{
		BucketRateLimiting: models.NewBucketRateLimitingWithDefaults(),
		ItemRateLimiting:   models.NewItemRateLimitingWithDefaults(),
	}

	if err := instanceConfig.SetSpec(spec); err != nil {
		return nil, err
	}

	val := &models.ArgoCDConfigMap{
		// make sure default admin account is disabled
		Accounts:            nil,
		CrossplaneExtension: spec.CrossplaneExtension,
	}
	if err := instanceConfig.SetArgoCDConfigMap(val); err != nil {
		return nil, err
	}

	return instanceConfig, nil
}

// ConfigureInstanceNotificationsSecret
func (s *Service) ConfigureInstanceNotificationsSecret(ctx context.Context, instanceID string, secretData map[string]string) error {
	cfg, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}
	if err := cfg.SetArgoCDNotificationsSecret(secretData); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, cfg, models.ArgoCDInstanceConfigColumns.ArgocdNotificationsSecret)
}

// PatchInstanceSecret updates argocd-secret Secret data
func (s *Service) PatchInstanceSecret(ctx context.Context, instanceID string, secretPatch map[string]*string) error {
	if _, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		secretData, err := config.GetArgocdSecret()
		if err != nil {
			return err
		}
		if secretData == nil {
			secretData = map[string]string{}
		}
		for key, value := range secretPatch {
			if IsManagedArgoCDSecretKey(key) {
				return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("the '%s' secret key is managed by Akuity Platform and cannot be set", key))
			}
			if value == nil {
				delete(secretData, key)
			} else {
				if isAccountPasswordKey(key) {
					if err := verifyBcryptPassword(*value); err != nil {
						return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("the %s is not a valid password in bcrypt format", key))
					}
					// If the key is account password, and it doesn't equal to the current password, then we update the passwordTime to the current time.
					if secretData[key] != *value {
						mTime := time.Now().UTC().Format(time.RFC3339)
						secretData[fmt.Sprintf("%sMtime", key)] = mTime
					}
				}
				secretData[key] = *value
			}

			// if the google sa secret key is modified/deleted, verify dex config validity
			if key == models.AkpDexGoogleSASecretKey {
				cm, err := config.GetArgoCDConfigMap()
				if err != nil {
					return err
				}
				if cm.DexConfig != "" {
					if _, err := config.HydrateAndValidateDexConfig(cm.DexConfig, secretData, false); err != nil {
						return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("modifying %s makes the existing dex config invalid : %v", key, err.Error()))
					}
				}
			}
		}
		return config.SetArgocdSecret(secretData)
	}); err != nil {
		return err
	}
	return nil
}

func verifyBcryptPassword(hashedPassword string) error {
	if _, err := bcrypt.Cost([]byte(hashedPassword)); err != nil {
		return err
	}
	return nil
}

// PatchInstanceNotificationsSecret patches the notifications secret
func (s *Service) PatchInstanceNotificationsSecret(ctx context.Context, instanceID string, secretPatch map[string]*string) error {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		secretData, err := config.GetArgoCDNotificationsSecret()
		if err != nil {
			return err
		}
		if secretData == nil {
			secretData = map[string]string{}
		}
		for key, value := range secretPatch {
			if value == nil {
				delete(secretData, key)
				continue
			}
			secretData[key] = *value
		}
		return config.SetArgoCDNotificationsSecret(secretData)
	})
	return err
}

// ConfigureInstanceNotificationsConfig
func (s *Service) ConfigureInstanceNotificationsConfig(ctx context.Context, instanceID string, cm map[string]string) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}
	existingCM, err := config.GetArgoCDNotificationsConfigMap()
	if err != nil {
		return err
	}
	if models.MapsEqual(existingCM, cm) {
		return nil
	}
	if err = config.SetArgoCDNotificationsConfigMap(cm); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config)
}

// ConfigureInstanceImageUpdaterConfig
func (s *Service) ConfigureInstanceImageUpdaterConfig(ctx context.Context, instanceID string, cm map[string]string, version string) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}

	existingCM, err := config.GetArgoCDImageUpdaterConfigMap()
	if err != nil {
		return err
	}

	spec, err := config.GetSpec()
	if err != nil {
		return err
	}

	if models.MapsEqual(existingCM, cm) && version == spec.ImageUpdaterVersion {
		return nil
	}

	if err := config.SetArgoCDImageUpdaterConfigMap(cm); err != nil {
		return err
	}

	if err := s.validateImageUpdaterVersion(version); err != nil {
		return err
	}
	spec.ImageUpdaterVersion = version
	if err := config.SetSpec(spec); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config,
		models.ArgoCDInstanceConfigColumns.ArgocdImageUpdaterCM,
		models.ArgoCDInstanceConfigColumns.Spec)
}

// ConfigureInstanceImageUpdaterSSHConfig
func (s *Service) ConfigureInstanceImageUpdaterSSHConfig(ctx context.Context, instanceID string, cm map[string]string) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}
	existingCM, err := config.GetArgoCDImageUpdaterSSHConfigMap()
	if err != nil {
		return err
	}
	if models.MapsEqual(existingCM, cm) {
		return nil
	}
	if err := config.SetArgoCDImageUpdaterSSHConfigMap(cm); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config, models.ArgoCDInstanceConfigColumns.ArgocdImageUpdaterSSHCM)
}

// PatchInstanceImageUpdaterSecret patches the notifications secret
func (s *Service) PatchInstanceImageUpdaterSecret(ctx context.Context, instanceID string, secretPatch map[string]*string) error {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		secretData, err := config.GetArgoCDImageUpdaterSecret()
		if err != nil {
			return err
		}
		if secretData == nil {
			secretData = map[string]string{}
		}
		for key, value := range secretPatch {
			if value == nil {
				delete(secretData, key)
				continue
			}
			secretData[key] = *value
		}
		return config.SetArgoCDImageUpdaterSecret(secretData)
	})
	return err
}

// ConfigureInstanceImageUpdaterSecret
func (s *Service) ConfigureInstanceImageUpdaterSecret(ctx context.Context, instanceID string, secretData map[string]string) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}
	if err := config.SetArgoCDImageUpdaterSecret(secretData); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config, models.ArgoCDInstanceConfigColumns.ArgocdImageUpdaterSecret)
}

func (s *Service) DeleteInstance(ctx context.Context, id string) error {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	kargoAgents, err := s.RepoSet.KargoAgents().Filter(models.KargoAgentWhere.RemoteArgocdInstanceID.EQ(null.StringFrom(id))).ListAll(ctx, "instance_id", "name")
	if err != nil {
		return err
	}
	if len(kargoAgents) > 0 {
		errorStr := "instance has some connected kargo agents, delete them before deleting instance : "
		for _, kargoAgent := range kargoAgents {
			errorStr += fmt.Sprintf(" (kargo_instance_id=%s, agent_name=%s)", kargoAgent.InstanceID, kargoAgent.Name)
		}
		return errorsutil.NewAPIStatus(http.StatusBadRequest, errorStr)
	}
	instance, err := s.RepoSet.ArgoCDInstances().GetByID(ctx, id)
	if err != nil {
		return err
	}

	orgID := instance.OrganizationOwner
	org, err := s.RepoSet.Organizations().GetByID(txCtx, orgID)
	if err != nil {
		return err
	}
	orgSpec, err := org.GetSpec()
	if err != nil {
		return err
	}
	existingArgoSpec := orgSpec.ArgoCDInstanceQuota
	if existingArgoSpec != nil {
		delete(existingArgoSpec, id)
	}
	if err := org.SetSpec(*orgSpec); err != nil {
		return err
	}
	if err := s.RepoSet.Organizations().Update(txCtx, org, models.OrganizationColumns.Spec); err != nil {
		return err
	}

	instance.DeletionTimestamp = null.TimeFrom(time.Now())
	if err := s.RepoSet.ArgoCDInstances().Update(txCtx, instance, models.ArgoCDInstanceColumns.DeletionTimestamp); err != nil {
		return err
	}
	return tx.Commit()
}

func validateHostAliases(hostAliases []models.HostAliases) error {
	for _, entry := range hostAliases {
		if ipAddr := net.ParseIP(entry.Ip); ipAddr == nil {
			return fmt.Errorf("invalid ip in list %s", entry.Ip)
		}
	}
	return nil
}

func ValidateIpAllowlist(allowList []models.IpAllowlistEntry) error {
	for _, entry := range allowList {
		if ipAddr := net.ParseIP(entry.Ip); ipAddr != nil {
			continue
		}

		if _, _, err := net.ParseCIDR(entry.Ip); err != nil {
			return fmt.Errorf("invalid ip in list %s: %w", entry.Ip, err)
		}
	}
	return nil
}

func (s *Service) GetSummary(ctx context.Context) (*InstancesSummary, error) {
	return s.instancesSource.GetSummary(ctx)
}

func (s *Service) GetConfigurationSummary(ctx context.Context) (*InstancesConfigurationSummary, error) {
	return s.instancesSource.GetConfigurationSummary(ctx)
}

func (s *Service) validateHostname(ctx context.Context, instanceID, subdomain, basepath string) error {
	if subdomain != instanceID && subdomain != "" {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdCustomSubdomain().Enabled() {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "argo cd custom subdomain feature is not enabled")
		}
	}

	if err := ValidateSubdomain(subdomain); err != nil {
		return err
	}
	cnt, err := s.RepoSet.ArgoCDInstanceConfigs().Filter(
		models.ArgoCDInstanceConfigWhere.Subdomain.EQ(subdomain),
		qm.And("argo_cd_instance_config.basepath = ? or argo_cd_instance_config.basepath = '' or ? = ''", basepath, basepath),
		models.ArgoCDInstanceConfigWhere.InstanceID.NEQ(instanceID),
	).Count(ctx)
	if err != nil {
		return err
	}
	if cnt > 0 {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("subdomain '%s' and path '%s' (or root path '/') is already in use", subdomain, basepath))
	}
	cnt, err = s.RepoSet.ArgoCDInstanceConfigs().Filter(
		qm.Load(models.ArgoCDInstanceConfigRels.Instance),
		models.ArgoCDInstanceConfigWhere.Subdomain.EQ(subdomain),
		qm.InnerJoin("argo_cd_instance on argo_cd_instance_config.instance_id = argo_cd_instance.id"),
		models.ArgoCDInstanceWhere.OrganizationOwner.NEQ(s.organizationID),
	).Count(ctx)
	if err != nil {
		return err
	}
	if cnt > 0 {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("subdomain '%s' is already in use", subdomain))
	}
	return nil
}

func (s *Service) UpsertAccount(ctx context.Context, instanceID string, account models.Account) (*models.Account, error) {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		cm, err := config.GetArgoCDConfigMap()
		if err != nil {
			return err
		}
		if index := cm.AccountIndexByName(account.Name); index < 0 {
			cm.Accounts = append(cm.Accounts, account)
		} else {
			existing := cm.Accounts[index]
			existing.Capabilities = account.Capabilities
			existing.Disabled = account.Disabled
			existing.Disabled = account.Disabled
			cm.Accounts[index] = existing
		}

		spec, err := config.GetSpec()
		if err != nil {
			return err
		}

		cm.CrossplaneExtension = spec.CrossplaneExtension
		return config.SetArgoCDConfigMap(cm)
	})
	return &account, err
}

func (s *Service) DeleteAccount(ctx context.Context, instanceID, accountName string) error {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		cm, err := config.GetArgoCDConfigMap()
		if err != nil {
			return err
		}
		if index := cm.AccountIndexByName(accountName); index > -1 {
			cm.Accounts = append(cm.Accounts[:index], cm.Accounts[index+1:]...)
		}

		spec, err := config.GetSpec()
		if err != nil {
			return err
		}

		cm.CrossplaneExtension = spec.CrossplaneExtension
		return config.SetArgoCDConfigMap(cm)
	})
	return err
}

var (
	accountSecretKeyPattern   = regexp.MustCompile(`^accounts\.(.*)\.passwordMtime$`)
	accountPasswordKeyPattern = regexp.MustCompile(`^accounts\.(.*)\.password$`)
)

// restrictedKeys are the argocd-secret keys which we do not allow users to manage
var restrictedKeys = map[string]bool{
	"server.secretkey":    true,
	"admin.passwordMtime": true,
}

// IsManagedArgoCDSecretKey returns true if the Argo CD secret key is a managed by Akuity Platform and cannot be updated by the user.
func IsManagedArgoCDSecretKey(key string) bool {
	return restrictedKeys[key] || accountSecretKeyPattern.MatchString(key)
}

// isAccountPasswordKey returns true if the Argo CD secret key is an account password.
func isAccountPasswordKey(key string) bool {
	return key == "admin.password" || accountPasswordKeyPattern.MatchString(key)
}

func (s *Service) UpdatePassword(ctx context.Context, instanceID, accountName, password string) (string, error) {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		cm, err := config.GetArgoCDConfigMap()
		if err != nil {
			return err
		}
		if index := cm.AccountIndexByName(accountName); index < 0 {
			return errorsutil.NewAPIStatus(http.StatusNotFound, "account not found")
		}
		secretData := map[string]string{}
		if !config.ArgocdSecret.IsZero() {
			secretData, err = config.GetArgocdSecret()
			if err != nil {
				return err
			}
		}
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		mTime := time.Now().UTC().Format(time.RFC3339)
		if accountName == "admin" {
			secretData["admin.password"] = string(hashedPassword)
			secretData["admin.passwordMtime"] = mTime
		} else {
			secretData[fmt.Sprintf("accounts.%s.password", accountName)] = string(hashedPassword)
			secretData[fmt.Sprintf("accounts.%s.passwordMtime", accountName)] = mTime
		}
		return config.SetArgocdSecret(secretData)
	})
	return password, err
}

func (s *Service) UpsertResourceCustomization(ctx context.Context, instanceID string, resourceCustomizations []models.ResourceCustomization) (*[]models.ResourceCustomization, error) {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		cm, err := config.GetArgoCDConfigMap()
		if err != nil {
			return err
		}

		cm.ResourceCustomizations = resourceCustomizations

		spec, err := config.GetSpec()
		if err != nil {
			return err
		}

		cm.CrossplaneExtension = spec.CrossplaneExtension
		return config.SetArgoCDConfigMap(cm)
	})
	return &resourceCustomizations, err
}

func (s *Service) GetAIAssistantUsageStats(ctx context.Context, instancIds []string) (*AIAssistantUsageStats, error) {
	return s.instancesSource.GetAIAssistantUsageStats(ctx, instancIds)
}

func (s *Service) GetSyncOperationStats(ctx context.Context, mods []qm.QueryMod, interval GroupByInterval, groupByField string) ([]SyncOperationStats, error) {
	return s.instancesSource.GetSyncOperationStats(ctx, mods, interval, groupByField)
}

func (s *Service) GetSyncOperationEvents(ctx context.Context, mods []qm.QueryMod) ([]*models.ArgoCDSyncOperation, error) {
	return s.instancesSource.GetSyncOperationEvents(ctx, mods)
}

func (s *Service) GetSyncOperationEventsCount(ctx context.Context, mods []qm.QueryMod) (int64, error) {
	return s.instancesSource.GetSyncOperationEventsCount(ctx, mods)
}

func (s *Service) GetSyncOperationEventsField(ctx context.Context, mods []qm.QueryMod, field string) ([]string, error) {
	return s.instancesSource.GetSyncOperationEventsField(ctx, mods, field)
}

// ConfigureInstanceAppsetSecret
func (s *Service) ConfigureInstanceAppsetSecret(ctx context.Context, instanceID string, secretData map[string]string) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}
	if err := config.SetArgoCDAppsetSecret(secretData); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config, models.ArgoCDInstanceConfigColumns.ArgocdAppsetSecret)
}

// PatchInstanceNotificationsSecret patches the appset secret
func (s *Service) PatchInstanceAppsetSecret(ctx context.Context, instanceID string, secretPatch map[string]*string) error {
	_, err := s.updateInstanceConfig(ctx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		secretData, err := config.GetArgoCDAppsetSecret()
		if err != nil {
			return err
		}
		if secretData == nil {
			secretData = map[string]string{}
		}
		for key, value := range secretPatch {
			if value == nil {
				delete(secretData, key)
				continue
			}
			secretData[key] = *value
		}
		return config.SetArgoCDAppsetSecret(secretData)
	})
	return err
}

func (s *Service) GetInstanceAppsetSecret(ctx context.Context, instanceID string) (map[string]string, error) {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	return config.GetArgoCDAppsetSecret()
}

// ConfigureInstanceConfigManagementPlugins
func (s *Service) ConfigureInstanceConfigManagementPlugins(ctx context.Context, instanceID string, plugins models.ConfigManagementPlugins) error {
	config, err := s.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}

	oldCMPConfig, err := config.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return err
	}

	if oldCMPConfig.Equals(&plugins) {
		return nil
	} else if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetConfigManagementPlugins().Enabled() {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "config management plugins feature is not enabled")
	}

	if err := s.validateConfigManagementPlugins(plugins); err != nil {
		return err
	}
	if err := config.SetArgoCDConfigManagementPlugins(plugins); err != nil {
		return err
	}
	return s.RepoSet.ArgoCDInstanceConfigs().Update(ctx, config, models.ArgoCDInstanceConfigColumns.ArgocdConfigManagementPlugins)
}

func (s *Service) validateConfigManagementPlugins(plugins models.ConfigManagementPlugins) error {
	invalidGeneratePlugins := []string{}
	for _, p := range plugins.Plugins {
		if p.Name == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "plugin name must not be empty")
		}
		if !configManagementPluginRegex.MatchString(p.Name) {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "plugin name must consist of alphanumeric characters, '-', '_' or '.' (e.g. 'key.name',  or 'KEY_NAME',  or 'key-name'")
		}
		if p.Spec == nil || p.Spec.Generate == nil || len(p.Spec.Generate.Command) == 0 {
			invalidGeneratePlugins = append(invalidGeneratePlugins, p.Name)
		}
	}
	if len(invalidGeneratePlugins) != 0 {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("invalid configuration for plugin(s): %s, spec.generate.command must not be empty", strings.Join(invalidGeneratePlugins, ",")))
	}
	return nil
}

func validateShards(shards []string, shard string) bool {
	for _, s := range shards {
		if s == shard {
			return true
		}
	}
	if len(shards) == 0 && shard == "" {
		return true
	}
	return false
}

func (s *Service) Backup(ctx context.Context, instanceID string) error {
	instanceConfig, err := s.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}

	specBackup, err := instanceConfig.GetSpec()
	if err != nil {
		return err
	}

	cmBackup, err := instanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return err
	}

	pluginsBackup, err := instanceConfig.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return err
	}

	backup := models.ArgoCDInstanceBackup{
		Date:                    time.Now(),
		Spec:                    specBackup,
		ConfigMap:               *cmBackup,
		ConfigManagementPlugins: pluginsBackup,
	}

	instance, err := s.ArgoCDInstances().GetByID(ctx, instanceID)
	if err != nil {
		return err
	}

	err = instance.SetArgoCDInstanceBackup(backup)
	if err != nil {
		return err
	}

	return s.ArgoCDInstances().Update(ctx, instance, models.ArgoCDInstanceColumns.Backup)
}

func (s *Service) BumpInstancesAndClustersGeneration(ctx context.Context, orgId string) error {
	txCtx, cancel := context.WithCancel(ctx)

	defer cancel()

	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	query := fmt.Sprintf("UPDATE %s SET generation = generation + 1 WHERE organization_owner = $1", models.TableNames.ArgoCDInstance)

	_, err = s.db.ExecContext(txCtx, query, orgId)
	if err != nil {
		return err
	}

	instances, err := s.RepoSet.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.OrganizationOwner.EQ(orgId)).ListAll(txCtx, "id")
	if err != nil {
		return err
	}

	instanceIds := make([]string, len(instances))

	for _, instance := range instances {
		instanceIds = append(instanceIds, instance.ID)
	}

	query = fmt.Sprintf("UPDATE %s SET generation = generation + 1 WHERE instance_id = ANY($1)", models.TableNames.ArgoCDCluster)

	_, err = s.db.ExecContext(txCtx, query, pq.Array(instanceIds))
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (s *Service) DisableRestrictedFeatures(ctx context.Context, instanceID string, featureStatuses *featuresv1.FeatureStatuses) error {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}
	if !featureStatuses.GetArgocdAgentStateReplication().Enabled() {
		// disable agent state replication for all clusters in the instance
		rawSql := fmt.Sprintf(
			"UPDATE argo_cd_cluster SET spec = jsonb_set(\"spec\"::jsonb, '{appReplication}', 'false') WHERE instance_id = '%s'",
			instanceID,
		)
		_, err = queries.Raw(rawSql).QueryContext(txCtx, s.db)
		if err != nil {
			return err
		}
	}

	if err := s.Backup(txCtx, instanceID); err != nil {
		return err
	}

	if _, err = s.updateInstanceConfig(txCtx, instanceID, func(config *models.ArgoCDInstanceConfig) error {
		spec, err := config.GetSpec()
		if err != nil {
			return err
		}

		cm, err := config.GetArgoCDConfigMap()
		if err != nil {
			return err
		}

		if !featureStatuses.GetArgocdSso().Enabled() {
			cm.DexConfig = ""
			cm.OidcConfig = ""
		}

		if !featureStatuses.GetArgocdDeepLinks().Enabled() {
			cm.ApplicationLinks = nil
			cm.ProjectLinks = nil
			cm.ResourceLinks = nil
		}

		if !featureStatuses.GetAkuityArgocdExtensions().Enabled() {
			spec.Extensions = nil
		}

		if !featureStatuses.GetAppOfApps().Enabled() {
			spec.DeclarativeManagementEnabled = false
		} else {
			spec.DeclarativeManagementEnabled = true
		}

		if !featureStatuses.GetArgocdFlexibleArchitecture().Enabled() {
			spec.RepoServerDelegate = nil
		}

		if !featureStatuses.GetArgocdCustomStyles().Enabled() {
			spec.Css = ""
		}

		if !featureStatuses.GetApplicationSetController().Enabled() {
			spec.AppsetDisabled = true
		} else {
			spec.AppsetDisabled = false
		}

		if !featureStatuses.GetArgocdFlexibleArchitecture().Enabled() {
			spec.RepoServerDelegate = nil
			spec.AppSetDelegate = nil
			spec.ImageUpdaterDelegate = nil
		}

		if !featureStatuses.GetConfigManagementPlugins().Enabled() {
			if err := config.SetArgoCDConfigManagementPlugins(models.ConfigManagementPlugins{
				Plugins: nil,
			}); err != nil {
				return err
			}
		}

		if !featureStatuses.GetArgocdCustomSubdomain().Enabled() {
			config.Subdomain = instanceID
		}

		if err := config.SetArgoCDConfigMap(cm); err != nil {
			return err
		}

		if err := config.SetSpec(spec); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}

	return tx.Commit()
}

func (s *Service) validateFQDN(ctx context.Context, instanceID string, fqdn null.String, basepath string) error {
	if !fqdn.IsZero() {
		argoCnt, err := s.RepoSet.ArgoCDInstanceConfigs().Filter(
			models.ArgoCDInstanceConfigWhere.FQDN.EQ(fqdn),
			qm.And("argo_cd_instance_config.basepath = ? or argo_cd_instance_config.basepath = '' or ? = ''", basepath, basepath),
			models.ArgoCDInstanceConfigWhere.InstanceID.NEQ(instanceID),
		).Count(ctx)
		if err != nil {
			return err
		}
		cnt, err := s.RepoSet.ArgoCDInstanceConfigs().Filter(
			qm.Load(models.ArgoCDInstanceConfigRels.Instance),
			models.ArgoCDInstanceConfigWhere.FQDN.EQ(fqdn),
			qm.InnerJoin("argo_cd_instance on argo_cd_instance_config.instance_id = argo_cd_instance.id"),
			models.ArgoCDInstanceWhere.OrganizationOwner.NEQ(s.organizationID),
		).Count(ctx)
		if err != nil {
			return err
		}
		if cnt > 0 {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("domain '%s' is already in use", fqdn.String))
		}
		kargoCnt := int64(0)
		// kargo don't support base paths
		if basepath != "" {
			kargoCnt, err = s.RepoSet.KargoInstanceConfigs().Filter(
				models.KargoInstanceConfigWhere.FQDN.EQ(fqdn),
			).Count(ctx)
			if err != nil {
				return err
			}
		}

		if argoCnt+kargoCnt > 0 {
			return errors.New(fmt.Sprintf("domain '%s' and path '%s' (or root path '/') is already in use", fqdn.String, basepath))
		}
	}
	return nil
}

func (s *Service) validateImageUpdaterVersion(version string) error {
	if version == "" {
		return nil
	}
	for _, supportedVersion := range agentClient.ListArgoCDImageUpdaterVersions() {
		if version == supportedVersion {
			return nil
		}
	}
	return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("image updater version %v not supported", version))
}

func (s *Service) validateKubeVisionSettings(cfg models.KubeVisionConfig) error {
	countByName := lo.CountValuesBy(cfg.AIConfig.Runbooks, func(item models.Runbook) string {
		return item.Name
	})
	duplicatedNames := lo.Map(lo.Filter(lo.Entries(countByName), func(item lo.Entry[string, int], index int) bool {
		return item.Value > 1
	}), func(item lo.Entry[string, int], index int) string {
		return item.Key
	})
	if len(duplicatedNames) > 0 {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("runbook names must be unique, found duplicates: %s", strings.Join(duplicatedNames, ", ")))
	}
	return nil
}
