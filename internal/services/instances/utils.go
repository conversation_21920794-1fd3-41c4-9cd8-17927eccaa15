package instances

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/volatiletech/sqlboiler/v4/boil"
	"k8s.io/client-go/rest"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/config"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
)

func GetApplicationLimits(ctx context.Context, gracePeriod time.Duration, executor boil.ContextExecutor, org *models.Organization, instanceConfig *models.ArgoCDInstanceConfig) (int, int, int, int, bool, error) {
	maxApps := 0
	maxInstanceApps := -1
	expired := false
	var summary *InstancesSummary
	var instanceApplicationCount int
	var spec *models.OrganizationSpec
	if config.IsSelfHosted {
		var err error

		licenseData := config.GetLicense()
		maxApps = int(licenseData.Applications)

		summary, err = NewInstancesSource(executor, org.ID).GetGlobalSummary(ctx)
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
	} else {
		var err error
		maxApps = org.MaxApplications
		status, err := org.GetOrgStatus()
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if status != nil && !status.Trial && status.ExpiryTime > 0 &&
			time.Now().After(time.Unix(status.ExpiryTime, 0).Add(gracePeriod)) {
			expired = true
		}
		summary, err = NewInstancesSource(executor, org.ID).GetSummary(ctx)
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
	}

	if instanceConfig == nil {
		return maxApps, maxInstanceApps, summary.ApplicationsCount, instanceApplicationCount, expired, nil
	}

	spec, err := org.GetSpec()
	if err != nil {
		return 0, -1, 0, 0, false, err
	}

	if spec != nil && spec.ArgoCDInstanceQuota != nil {
		if v, ok := spec.ArgoCDInstanceQuota[instanceConfig.InstanceID]; ok {
			maxInstanceApps = int(v)
		}
	}

	instanceApplicationCount, err = NewInstancesSource(executor, org.ID).GetInstanceApplicationCount(ctx, instanceConfig.InstanceID)
	if err != nil {
		return 0, -1, 0, 0, false, err
	}
	return maxApps, maxInstanceApps, summary.ApplicationsCount, instanceApplicationCount, expired, nil
}

// GetConfigManagementPluginEnabled determines if fallback repo-server, config management plugin on control plane and
// config management plugin on cluster agent should be enabled based on if the platform is SaaS or self-hosted and whether
// or not the Repo Server Delegate is enabled.
// If the platform is self-hosted, we allow user to run CMPv2 workloads on both control-plane and cluster agent, and fallback
// can be enabled.
// If the platform is SaaS platform, we don't let user run CMPv2 workloads as sidecars of repo server on control-plane,
// also, it cannot fallback to control-plane repo-server because auto-detection is non-deterministic.
func GetConfigManagementPluginEnabled(configManagementPlugins models.ConfigManagementPlugins) (fallbackRepoServerEnabled, controlPlaneCMPEnabled, clusterAgentCMPEnabled bool) {
	cmpEnabled := false
	for _, p := range configManagementPlugins.Plugins {
		if p != nil && p.Enabled {
			cmpEnabled = true
			break
		}
	}

	if config.IsSelfHosted {
		fallbackRepoServerEnabled = true
		controlPlaneCMPEnabled = cmpEnabled
		clusterAgentCMPEnabled = cmpEnabled
		return
	}

	fallbackRepoServerEnabled = !cmpEnabled
	controlPlaneCMPEnabled = false
	clusterAgentCMPEnabled = cmpEnabled
	return
}

func ValidateSubdomain(subdomain string) error {
	if subdomain == "" {
		return nil
	}
	if len(subdomain) < minSubdomainLength || len(subdomain) > maxSubdomainLength {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("subdomain length must be >= %v and <= %v", minSubdomainLength, maxSubdomainLength))
	}

	if !subdomainRegex.MatchString(subdomain) {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "invalid subdomain")
	}
	return nil
}

type hostnameInjector struct {
	rt       http.RoundTripper
	hostname string
}

func (i *hostnameInjector) Unwrap() http.RoundTripper {
	return i.rt
}

func (i *hostnameInjector) RoundTrip(req *http.Request) (*http.Response, error) {
	newReq := req.Clone(req.Context())
	newReq.Host = i.hostname
	return i.rt.RoundTrip(newReq)
}

func ClusterRestConfig(clusterName, instanceID string) *rest.Config {
	return &rest.Config{
		Host: fmt.Sprintf("http://cluster-%s.%s%s:8001", clusterName, common.ArgoCDHostNamespace(instanceID), client.GetInClusterServicePostfix()),
		WrapTransport: func(rt http.RoundTripper) http.RoundTripper {
			return &hostnameInjector{rt: rt, hostname: fmt.Sprintf("cluster-%s", clusterName)}
		},
	}
}
