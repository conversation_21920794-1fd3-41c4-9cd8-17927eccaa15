package functions

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/yaml.v2"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/restmapper"

	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/ai/clients"
	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/internal/utils/secret"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (fc *Controller) addSharedFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "k8s-get-resource",
			Description: `Retrieves JSON serialized kubernetes resource.`,
		},
		DisplayName: "Get Kubernetes resource",
	}, fc.getK8sResource); err != nil {
		return err
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "k8s-verify-resource-patch",
			Description: `Verifies if the provided json serialized strategic merged patch is valid.`,
		},
		DisplayName: "Verify Kubernetes resource patch",
	}, fc.verifyK8sPatch); err != nil {
		return err
	}

	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "k8s-patch-resource",
			Description: `Applies provided json serialized strategic merged patch to the given resource.`,
		},
		DisplayName: "Patch Kubernetes resource",
	}, fc.patchK8sResource); err != nil {
		return err
	}

	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "k8s-get-workload-logs",
			Description: `
				The Tool retrieves logs from the specified Kubernetes resources,
				including "apps/Deployment", "apps/StatefulSet", "apps/DaemonSet", "apps/ReplicaSet",
				"apps/ReplicationController", and "/Pod". This function provides detailed runtime
				information, such as application output and error logs.
				
				Note: This only retrieves recent logs from the last 1 hour. Please specify "tail" to retrieve the number of lines you want to fetch. But don't set it to a very large number, it will cause the response to be too long.`,
		},
		DisplayName: "Get Kubernetes workload logs",
		PromptSuggestion: &organizationv1.AIConversationSuggestion{
			Description: "Review logs",
			Prompt:      `Could you analyze the workload logs to help identify any errors, warnings, or unusual patterns in the runtime output?`,
		},
	}, fc.getLogs); err != nil {
		return err
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "k8s-delete-resource",
			Description: `Deletes the specified Kubernetes resource.`,
		},
		DisplayName: "Delete Kubernetes resource",
	}, fc.deleteK8sResource); err != nil {
		return err
	}

	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "k8s-get-version",
			Description: `Returns K8S and akuity agent version.`,
		},
		DisplayName: "Get Kubernetes version",
	}, fc.getK8SVersion); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getAppClusterId(ctx context.Context, appCtx models.ArgoCDApplicationConversationContext) (string, error) {
	argocdClient := fc.ArgoCDClientSet.GetArgoCDClient(appCtx.InstanceID)
	if argocdClient == nil {
		return "", util.NewInvalidArgsError("Invalid ArgoCD instance ID")
	}
	app, err := argocdClient.GetApplication(ctx, appCtx.Name)
	if err != nil {
		if ok, err := checkArgoCDAppError(appCtx.Name, err); ok {
			return "", err
		}
		return "", err
	}
	clusterName := argocd.GetNameFromDestination(app.Spec.Destination)
	cluster, err := fc.RepoSet.ArgoCDClusters(models.ArgoCDClusterWhere.InstanceID.EQ(appCtx.InstanceID), models.ArgoCDClusterWhere.Name.EQ(clusterName)).One(ctx)
	if err != nil {
		return "", err
	}
	return cluster.ID, nil
}

func (fc *Controller) getIdsFromContext(
	ctx context.Context,
	argoCDApp *models.ArgoCDApplicationConversationContext,
	k8sNamespace *models.K8SNamespaceConversationContext,
) (string, string, error) {
	if k8sNamespace != nil {
		return k8sNamespace.InstanceID, k8sNamespace.ClusterID, nil
	} else if argoCDApp != nil {
		clusterID, err := fc.getAppClusterId(ctx, *argoCDApp)
		if err != nil {
			return "", "", err
		}
		return argoCDApp.InstanceID, clusterID, nil
	}
	return "", "", util.NewInvalidArgsError("argoCDApp or k8sNamespace must be provided")
}

func (fc *Controller) getK8sResource(ctx context.Context, args struct {
	ResourceID   ResourceID                                   `json:"resourceID" required:"true"`
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	k8sClient, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	if k8sClient == nil {
		return "", nil
	}
	resource, err := k8sClient.GetResource(ctx, schema.GroupVersionKind{Group: args.ResourceID.Group, Version: args.ResourceID.Version, Kind: args.ResourceID.Kind}, args.ResourceID.Namespace, args.ResourceID.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			return "", util.NewInvalidArgsError(fmt.Sprintf("Resource %s/%s/%s not found", args.ResourceID.Group, args.ResourceID.Kind, args.ResourceID.Name))
		}
		return "", err
	}
	resource.SetManagedFields(nil)
	if resource.GetAPIVersion() == "v1" && resource.GetKind() == "Secret" {
		secret.ReductSecretData(resource.Object)
	}
	resourceJSON, err := json.Marshal(resource)
	if err != nil {
		return "", err
	}

	return string(resourceJSON), nil
}

type logsSender struct {
	Ctx    context.Context
	Sender func(response *organizationv1.GetKubernetesLogsResponse) error
}

func (s *logsSender) Context() context.Context {
	return s.Ctx
}

func (s *logsSender) Send(response *organizationv1.GetKubernetesLogsResponse) error {
	return s.Sender(response)
}

// getLogs fetches logs for the specified resource
func (fc *Controller) getLogs(ctx context.Context, args struct {
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`
	ResourceID   string                                       `json:"resourceID" required:"true" description:"Resource UID"`
	Previous     bool                                         `json:"previous" required:"false" description:"If true, fetch logs from previous container (if pod has restarted)"`
	Tail         int                                          `json:"tail" required:"true" description:"Number of lines to fetch, don't be a very large number unless you are debugging a problem"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	client, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	if client == nil {
		return "", nil
	}
	var entries []map[string]any
	if args.Tail == 0 {
		args.Tail = 100
	}
	if err := fc.ResSvc.GetLogs(&logsSender{ctx, func(response *organizationv1.GetKubernetesLogsResponse) error {
		entries = append(entries, map[string]interface{}{
			"timestamp":     response.Timestamp,
			"containerName": response.ContainerName,
			"content":       response.Content,
		})
		return nil
	}}, client.KubernetesClientset, instanceID, clusterID, args.ResourceID, 0, timestamppb.New(time.Now().Add(-1*time.Hour)), int64(args.Tail), false, nil, args.Previous, ""); err != nil {
		if errors.IsNotFound(err) {
			return "", util.NewInvalidArgsError(fmt.Sprintf("Resource %s not found", args.ResourceID))
		}
	}
	res, err := json.Marshal(entries)
	if err != nil {
		return "", fmt.Errorf("failed to marshal logs: %w", err)
	}
	return string(res), nil
}

func (fc *Controller) getResourceInterface(client *clients.K8SClient, resourceID ResourceID) (dynamic.ResourceInterface, error) {
	gr, err := restmapper.GetAPIGroupResources(client.KubernetesClientset.Discovery())
	if err != nil {
		return nil, err
	}
	mapper := restmapper.NewDiscoveryRESTMapper(gr)
	mapping, err := mapper.RESTMapping(schema.GroupKind{Group: resourceID.Group, Kind: resourceID.Kind}, resourceID.Version)
	if err != nil {
		return nil, err
	}
	gvr := mapping.Resource
	var resIf dynamic.ResourceInterface
	if resourceID.Namespace != "" {
		resIf = client.DynamicClientset.Resource(gvr).Namespace(resourceID.Namespace)
	} else {
		resIf = client.DynamicClientset.Resource(gvr)
	}
	return resIf, nil
}

func (fc *Controller) verifyK8sPatch(ctx context.Context, args struct {
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`

	ResourceID ResourceID `json:"resourceID"`
	Patch      string     `json:"patch"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	client, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	if client == nil {
		return "", nil
	}
	resIf, err := fc.getResourceInterface(client, args.ResourceID)
	if err != nil {
		return "", err
	}
	// get the old resource
	oldRes, err := resIf.Get(ctx, args.ResourceID.Name, metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	oldStr, err := senitizeResource(oldRes)
	if err != nil {
		return "", err
	}

	// dry run the patch
	res, err := resIf.Patch(ctx, args.ResourceID.Name, types.StrategicMergePatchType, []byte(args.Patch), metav1.PatchOptions{DryRun: []string{"All"}})
	if err != nil {
		if errors.IsNotFound(err) {
			return "", util.NewInvalidArgsError(fmt.Sprintf("Resource %s/%s/%s not found", args.ResourceID.Group, args.ResourceID.Kind, args.ResourceID.Name))
		}
		// we return the error to AI so that it understands why the patch is not working and can prepare a new patch
		return err.Error(), nil
	}
	newStr, err := senitizeResource(res)
	if err != nil {
		return "", fmt.Errorf("failed to marshal dry run result: %w", err)
	}
	return fmt.Sprintf(`Resource patch is valid, the dry run result is:
new:
%s

old:
%s`, newStr, oldStr), nil
}

func (fc *Controller) patchK8sResource(ctx context.Context, args struct {
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`

	ResourceID ResourceID `json:"resourceID"`
	Patch      string     `json:"patch"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	client, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	if client == nil {
		return "", nil
	}
	resIf, err := fc.getResourceInterface(client, args.ResourceID)
	if err != nil {
		return "", err
	}
	if _, err = resIf.Patch(ctx, args.ResourceID.Name, types.StrategicMergePatchType, []byte(args.Patch), metav1.PatchOptions{}); err != nil {
		if errors.IsNotFound(err) {
			return "", util.NewInvalidArgsError(fmt.Sprintf("Resource %s/%s/%s not found", args.ResourceID.Group, args.ResourceID.Kind, args.ResourceID.Name))
		}
		return "", err
	}
	return "Resource patched successfully", nil
}

func (fc *Controller) deleteK8sResource(ctx context.Context, args struct {
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`

	ResourceID ResourceID `json:"resourceID"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	client, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	if client == nil {
		return "", nil
	}
	resIf, err := fc.getResourceInterface(client, args.ResourceID)
	if err != nil {
		return "", err
	}
	if err = resIf.Delete(ctx, args.ResourceID.Name, metav1.DeleteOptions{}); err != nil {
		if errors.IsNotFound(err) {
			return "", util.NewInvalidArgsError(fmt.Sprintf("Resource %s/%s/%s not found", args.ResourceID.Group, args.ResourceID.Kind, args.ResourceID.Name))
		}
		return "", err
	}
	return "Resource deleted successfully", nil
}

func (fc *Controller) getK8SVersion(ctx context.Context, args struct {
	ArgoCDApp    *models.ArgoCDApplicationConversationContext `json:"argoCDApp" required:"false" description:"ArgoCD application context"`
	K8SNamespace *models.K8SNamespaceConversationContext      `json:"k8sNamespace" required:"false" description:"Kubernetes Namespace context"`
},
) (string, error) {
	instanceID, clusterID, err := fc.getIdsFromContext(ctx, args.ArgoCDApp, args.K8SNamespace)
	if err != nil {
		return "", err
	}
	cluster, err := fc.RepoSet.ArgoCDClusters().GetByID(ctx, clusterID)
	if err != nil {
		return "", err
	}
	client, err := fc.K8sClientSet.GetK8sClient(ctx, instanceID, clusterID)
	if err != nil {
		return "", err
	}
	k8sVersion, err := client.KubernetesClientset.Discovery().ServerVersion()
	if err != nil {
		return "", err
	}
	state, err := cluster.GetAgentState(true)
	if err != nil {
		return "", err
	}
	agentVersion := "Uknown"
	if state != nil {
		agentVersion = state.Version
	}
	return fmt.Sprintf("Kubernetes Version: %s; Akuity Agent Version: %s", k8sVersion.GitVersion, agentVersion), nil
}

func checkArgoCDAppError(appName string, err error) (bool, error) {
	if s, ok := status.FromError(err); ok && (s.Code() == codes.NotFound || s.Code() == codes.PermissionDenied) {
		return true, util.NewInvalidArgsError(fmt.Sprintf("Application %s not found", appName))
	}
	return false, err
}

func senitizeResource(res *unstructured.Unstructured) (string, error) {
	res.SetManagedFields(nil)
	unstructured.RemoveNestedField(res.Object, "status")
	unstructured.RemoveNestedField(res.Object, "metadata", "annotations", "kubectl.kubernetes.io/last-applied-configuration")
	// remove the generation since the generation will be bumped after dry run patch, which causes the diff in the viewer.
	unstructured.RemoveNestedField(res.Object, "metadata", "generation")
	resYaml, err := yaml.Marshal(res.Object)
	if err != nil {
		return "", err
	}
	return string(resYaml), nil
}
