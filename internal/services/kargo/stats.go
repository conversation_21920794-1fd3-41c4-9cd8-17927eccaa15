package kargo

import (
	"context"
	"fmt"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/models/models"

	_ "embed"
)

// instancesQuery returns kargo instances with configs and number of agents filtered by organization
//
//go:embed instances.sql
var instancesQuery string

var fromInstancesMod = qm.From(fmt.Sprintf("(%s) as instances", instancesQuery))

// Source provides method to list Argo CD instances
type Source interface {
	GetSummary(ctx context.Context) (*InstancesSummary, error)
	GetGlobalSummary(ctx context.Context) (*InstancesSummary, error)
}

type statSource struct {
	db          boil.ContextExecutor
	ownerFilter qm.QueryMod
}

func NewKargoStatSource(db boil.ContextExecutor, orgID string) *statSource {
	return &statSource{db: db, ownerFilter: qm.And("organization_owner = ?", orgID)}
}

type InstancesSummary struct {
	InstancesCount  int         `boil:"instances_count" json:"instancesCount"`
	FirstInstanceID null.String `boil:"first_instance_id" json:"firstInstanceID"`
	AgentsCount     int         `boil:"agents_count" json:"agentsCount"`
	ProjectsCount   int         `boil:"projects_count" json:"projectsCount"`
	StagesCount     int         `boil:"stages_count" json:"stagesCount"`
}

func (s *statSource) GetSummary(ctx context.Context) (*InstancesSummary, error) {
	counts := &InstancesSummary{}
	err := models.NewQuery(fromInstancesMod, s.ownerFilter,
		qm.Select(`
min(instances.id) as first_instance_id,
coalesce(sum(instances.agents_count),0) as agents_count,
count(*) as instances_count, 
coalesce(sum((status_info->'kargoStats'->>'projectCount')::int),0) as projects_count, coalesce(sum((status_info->'kargoStats'->>'stageCount')::int),0) as stages_count`)).Bind(ctx, s.db, counts)
	return counts, err
}

func (s *statSource) GetGlobalSummary(ctx context.Context) (*InstancesSummary, error) {
	counts := &InstancesSummary{}
	err := models.NewQuery(fromInstancesMod,
		qm.Select(`
min(instances.id) as first_instance_id,
coalesce(sum(instances.agents_count),0) as agents_count,
count(*) as instances_count, 
coalesce(sum((status_info->'kargoStats'->>'projectCount')::int),0) as projects_count, coalesce(sum((status_info->'kargoStats'->>'stageCount')::int),0) as stages_count`)).Bind(ctx, s.db, counts)
	return counts, err
}

func (s *statSource) GetInstanceStageCount(ctx context.Context, instanceID string) (int, error) {
	var count int
	err := models.NewQuery(
		fromInstancesMod,
		s.ownerFilter,
		qm.And("id = ?", instanceID),
		qm.Select("coalesce((status_info->'kargoStats'->>'stageCount')::int, 0) as stages_count"),
	).QueryRowContext(ctx, s.db).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get stage count for kargo instance '%s': %w", instanceID, err)
	}
	return count, nil
}

func GetKargoLimits(ctx context.Context, gracePeriod time.Duration, executor boil.ContextExecutor, org *models.Organization, instanceConfig *models.KargoInstanceConfig) (int, int, int, int, bool, error) {
	maxStages := 0
	maxInstanceStages := -1
	expired := false
	var summary *InstancesSummary
	var instanceStageCount int
	var spec *models.OrganizationSpec
	if config.IsSelfHosted {
		var err error
		licenseData := config.GetLicense()
		maxStages = int(licenseData.KargoStages)

		summary, err = NewKargoStatSource(executor, org.ID).GetGlobalSummary(ctx)
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
	} else {
		maxStages = org.MaxKargoStages

		status, err := org.GetOrgStatus()
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if status != nil && !status.Trial && status.ExpiryTime > 0 &&
			time.Now().After(time.Unix(status.ExpiryTime, 0).Add(gracePeriod)) {
			expired = true
		}
		summary, err = NewKargoStatSource(executor, org.ID).GetSummary(ctx)
		if err != nil {
			return 0, -1, 0, 0, false, err
		}
	}

	if instanceConfig == nil {
		return maxStages, maxInstanceStages, summary.StagesCount, instanceStageCount, expired, nil
	}

	spec, err := org.GetSpec()
	if err != nil {
		return 0, -1, 0, 0, false, err
	}
	if spec != nil && spec.KargoInstanceQuota != nil && instanceConfig != nil {
		if v, ok := spec.KargoInstanceQuota[instanceConfig.InstanceID]; ok {
			maxInstanceStages = int(v)
		}
	}
	instanceStageCount, err = NewKargoStatSource(executor, org.ID).GetInstanceStageCount(ctx, instanceConfig.InstanceID)
	if err != nil {
		return 0, -1, 0, 0, false, err
	}
	return maxStages, maxInstanceStages, summary.StagesCount, instanceStageCount, expired, nil
}
