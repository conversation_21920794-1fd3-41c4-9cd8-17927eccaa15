package k8sresource

import (
	"context"
	"fmt"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"

	_ "embed"
)

func (s *Service) ListUniqueNamespaces(ctx context.Context, instanceID string, clusterIDs []string, nodeName string) ([]string, error) {
	if nodeName != "" {
		// If nodeName is present, get the pods in the node and then get the namespaces from the pods
		pods, err := s.ListResources(ctx, instanceID, "", "", "Pod", "v1", clusterIDs, nil, 0, 0, "", "", models.ArgoCDClusterK8SObjectColumns.Name, "nodeName="+nodeName, nil)
		if err != nil {
			return nil, err
		}
		nsMap := make(map[string]struct{})
		for _, pod := range pods {
			nsMap[pod.Namespace.String] = struct{}{}
		}
		res := make([]string, 0, len(nsMap))
		for ns := range nsMap {
			res = append(res, ns)
		}
		return res, nil
	}
	mods := []qm.QueryMod{
		qm.Distinct(models.ArgoCDClusterK8SObjectColumns.Name),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Namespace")),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
	}

	if instanceID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID))
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs))
	}
	mods = append(mods, qm.OrderBy(models.ArgoCDClusterK8SObjectColumns.Name))
	namespaces, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx, models.ArgoCDClusterK8SObjectColumns.Name)
	if err != nil {
		return nil, err
	}
	res := make([]string, 0, len(namespaces))
	for _, n := range namespaces {
		if n.Name != "" {
			res = append(res, n.Name)
		}
	}

	if s.enforcer != nil {
		allowedDestinations, err := s.enforcer.GetAllowedDestinations(ctx, instanceID)
		if err != nil {
			return nil, err
		}
		res = lo.Filter(res, func(item string, _ int) bool {
			return lo.ContainsBy(allowedDestinations, func(dest Destination) bool {
				return dest.MatchNamespace(item)
			})
		})
	}
	return res, nil
}

func (s *Service) GetNamespaceDetail(ctx context.Context, instanceID, clusterID, namespaceID string) (*organizationv1.NamespaceDetail, error) {
	var result []models.ArgoCDClusterK8SObject
	args := []any{s.organizationID, instanceID, pq.Array([]string{clusterID}), pq.Array([]string{namespaceID})}
	akiPermissionFilter := ""
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &args)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch filter: %w", err)
		}
		akiPermissionFilter = "AND " + filter
	}
	if err := models.NewQuery(qm.SQL(fmt.Sprintf(namespaceDetailQuery, akiPermissionFilter), args...)).Bind(ctx, s.db, &result); err != nil {
		return nil, fmt.Errorf("failed to fetch namespace details: %w", err)
	}

	var namespace models.ArgoCDClusterK8SObject
	var containers []models.ArgoCDClusterK8SObject
	var pods []models.ArgoCDClusterK8SObject
	for _, r := range result {
		switch r.Kind.String {
		case "Namespace":
			namespace = r
		case "Container":
			containers = append(containers, r)
		case "Pod":
			pods = append(pods, r)
		}
	}
	namespaceDetail := &organizationv1.NamespaceDetail{
		Id:              namespace.ID,
		Name:            namespace.Name,
		ClusterId:       namespace.ClusterID,
		InstanceId:      namespace.InstanceID,
		UsageCpu:        new(float64),
		UsageMemory:     new(float64),
		RequestCpu:      new(float64),
		RequestMemory:   new(float64),
		PodCount:        new(int32),
		RunningPodCount: new(int32),
	}

	for _, cont := range containers {
		columns := &ContainerColumns{}
		if err := cont.Columns.Unmarshal(columns); err != nil {
			return nil, err
		}

		if columns.State != "running" {
			continue
		}

		if columns.UsageCPU != nil {
			*namespaceDetail.UsageCpu += *columns.UsageCPU
		}
		if columns.UsageMemory != nil {
			*namespaceDetail.UsageMemory += *columns.UsageMemory
		}
		if columns.RequestsCPU != nil {
			*namespaceDetail.RequestCpu += *columns.RequestsCPU
		}
		if columns.RequestsMemory != nil {
			*namespaceDetail.RequestMemory += *columns.RequestsMemory
		}
	}

	for _, pod := range pods {
		columns := &PodColumns{}
		if err := pod.Columns.Unmarshal(columns); err != nil {
			return nil, err
		}

		if columns.Phase == "Running" {
			*namespaceDetail.RunningPodCount++
		}
		*namespaceDetail.PodCount++
	}

	return namespaceDetail, nil
}

func getNamespaceGroupByValue(groupBy []organizationv1.NamespaceGroupBy, clusterName string) []string {
	res := make([]string, 0, len(groupBy))
	for _, g := range groupBy {
		switch g {
		case organizationv1.NamespaceGroupBy_NAMESPACE_GROUP_BY_UNSPECIFIED, organizationv1.NamespaceGroupBy_NAMESPACE_GROUP_BY_CLUSTER:
			res = append(res, clusterName)
		}
	}
	return res
}

//go:embed namespace_detail.sql
var namespaceDetailQuery string

func (s *Service) ListNamespacesDetails(ctx context.Context, instanceID string, clusterIDs []string, groupBys []organizationv1.NamespaceGroupBy, filler organizationv1.NamespaceFiller) ([]*organizationv1.NamespaceDetail, error) {
	var result []models.ArgoCDClusterK8SObject
	args := []any{s.organizationID, instanceID, pq.Array(clusterIDs), nil}
	akiPermissionFilter := ""
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &args)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch filter: %w", err)
		}
		akiPermissionFilter = "AND " + filter
	}
	if err := models.NewQuery(qm.SQL(fmt.Sprintf(namespaceDetailQuery, akiPermissionFilter), args...)).Bind(ctx, s.db, &result); err != nil {
		return nil, fmt.Errorf("failed to fetch namespace details: %w", err)
	}

	namespaces := make([]models.ArgoCDClusterK8SObject, 0, len(result))
	containers := make([]models.ArgoCDClusterK8SObject, 0, len(result))
	for _, r := range result {
		switch r.Kind.String {
		case "Namespace":
			namespaces = append(namespaces, r)
		case "Container":
			containers = append(containers, r)
		}
	}

	makeKey := func(instanceID, clusterID, namespace string) string {
		return fmt.Sprintf("%s/%s/%s", instanceID, clusterID, namespace)
	}
	details := map[string]*organizationv1.NamespaceDetail{}
	for _, ns := range namespaces {
		details[makeKey(ns.InstanceID, ns.ClusterID, ns.Name)] = &organizationv1.NamespaceDetail{
			Id:         ns.ID,
			Name:       ns.Name,
			ClusterId:  ns.ClusterID,
			InstanceId: ns.InstanceID,
			FillValue:  nil,
			Groups:     getNamespaceGroupByValue(groupBys, ns.ClusterID),
		}
	}

	cpuUsage := make(map[string]float64)
	cpuRequest := make(map[string]float64)
	memUsage := make(map[string]float64)
	memRequest := make(map[string]float64)
	for _, cont := range containers {
		key := makeKey(cont.InstanceID, cont.ClusterID, cont.Namespace.String)
		detail := details[key]
		if detail == nil {
			continue
		}

		columns := &ContainerColumns{}
		if err := cont.Columns.Unmarshal(columns); err != nil {
			return nil, err
		}

		if columns.State != "running" {
			continue
		}

		switch filler {
		case organizationv1.NamespaceFiller_NAMESPACE_FILLER_UNSPECIFIED,
			organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_CPU:
			if columns.UsageCPU == nil {
				continue
			}
			if detail.FillValue == nil {
				detail.FillValue = new(float64)
			}
			*detail.FillValue += *columns.UsageCPU
		case organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_MEMORY:
			if columns.UsageMemory == nil {
				continue
			}
			if detail.FillValue == nil {
				detail.FillValue = new(float64)
			}
			*detail.FillValue += *columns.UsageMemory
		case organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_CPU:
			if columns.UsageCPU != nil {
				cpuUsage[key] += *columns.UsageCPU
			}
			if columns.RequestsCPU != nil {
				cpuRequest[key] += *columns.RequestsCPU
			}
		case organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_MEMORY:
			if columns.UsageMemory != nil {
				memUsage[key] += *columns.UsageMemory
			}
			if columns.RequestsMemory != nil {
				memRequest[key] += *columns.RequestsMemory
			}
		}
		details[key] = detail
	}

	switch filler {
	case organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_CPU:
		for k := range details {
			uv, uok := cpuUsage[k]
			rv, rok := cpuRequest[k]
			if uok {
				details[k].UsageCpu = &uv
			}
			if rok {
				details[k].RequestCpu = &rv
			}
			if uok && rok && rv != 0 {
				v := uv / rv * 100
				details[k].FillValue = &v
			}
		}
	case organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_MEMORY:
		for k := range details {
			uv, uok := memUsage[k]
			rv, rok := memRequest[k]
			if uok {
				details[k].UsageMemory = &uv
			}
			if rok {
				details[k].RequestMemory = &rv
			}
			if uok && rok && rv != 0 {
				v := uv / rv * 100
				details[k].FillValue = &v
			}
		}
	}

	detailsList := lo.MapToSlice(details, func(ns string, detail *organizationv1.NamespaceDetail) *organizationv1.NamespaceDetail {
		return detail
	})

	return detailsList, nil
}
