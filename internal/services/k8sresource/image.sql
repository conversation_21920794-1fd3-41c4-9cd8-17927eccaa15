WITH images_data AS (
  SELECT
    cluster_id,
    instance_id,
    organization_id,
    columns->>'image' AS name,
    columns->>'tag' AS tag,
    COALESCE(columns->>'digest', '') AS digest,
    COALESCE(columns->>'cves', '') AS cves,
    -- default to -1 if cveCount is not present
    COALESCE((columns->>'cveCount')::int, -1) AS cve_count,
    COALESCE(columns->>'cveLastScanTime', '') AS cve_last_scan_time,
    COALESCE((columns->>'containerCount')::int, 0) AS container_count
  FROM
    argo_cd_cluster_k8s_object
  WHERE
    kind = 'Image'
    AND "group" = 'dashboard.akuity.io'
    AND columns->>'image' IS NOT NULL
    AND columns->>'tag' IS NOT NULL
    AND organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
),

containers_data AS (
  SELECT
    cluster_id,
    instance_id,
    organization_id,
    columns->>'image' AS name,
    columns->>'tag' AS tag,
    COALESCE(columns->>'digest', '') AS digest,
    COUNT(*) AS container_count
  FROM
    argo_cd_cluster_k8s_object
  WHERE
    kind = 'Container'
    AND "group" = 'dashboard.akuity.io'
    AND columns->>'image' IS NOT NULL
    AND columns->>'tag' IS NOT NULL
    AND organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
  GROUP BY
    cluster_id,
    instance_id,
    organization_id,
    columns->>'image',
    columns->>'tag',
    COALESCE(columns->>'digest', '')
),

results AS (
  SELECT
    COALESCE(images_data.cluster_id, containers_data.cluster_id) AS cluster_id,
    COALESCE(images_data.instance_id, containers_data.instance_id) AS instance_id,
    COALESCE(images_data.organization_id, containers_data.organization_id) AS organization_id,
    COALESCE(images_data.name, containers_data.name) AS name,
    COALESCE(images_data.tag, containers_data.tag) AS tag,
    COALESCE(images_data.digest, containers_data.digest) AS digest,
    COALESCE(images_data.cves, '') AS cves,
    COALESCE(images_data.cve_count, -1) AS cve_count,
    COALESCE(images_data.cve_last_scan_time, '') AS cve_last_scan_time,
    GREATEST(images_data.container_count, containers_data.container_count) AS container_count
  FROM
    containers_data
  INNER JOIN
    images_data
  ON
    containers_data.cluster_id = images_data.cluster_id
    AND containers_data.instance_id = images_data.instance_id
    AND containers_data.organization_id = images_data.organization_id
    AND containers_data.name = images_data.name
    AND containers_data.tag = images_data.tag
    AND containers_data.digest = images_data.digest
  WHERE
    ($4::text IS NULL OR containers_data.name ilike '%%'||$4||'%%')
    AND ($5::text IS NULL OR containers_data.name = $5)
    AND ($6::text IS NULL OR containers_data.tag = $6)
    AND ($7::text IS NULL OR containers_data.digest = $7)
),

scoped_results AS (
  SELECT
    name,
    tag,
    digest,
    MAX(cves) AS cves,
    MAX(cve_count) AS cve_count,
    MAX(cve_last_scan_time) AS cve_last_scan_time,
    SUM(container_count) AS container_count
  FROM results
  GROUP BY 
    name, tag, digest
),

scoped_results_count AS (
  SELECT COUNT(*) AS image_count FROM scoped_results
)

SELECT * FROM scoped_results
LEFT OUTER JOIN scoped_results_count
ON TRUE -- just add image_count to the result
%[2]s
OFFSET $8
LIMIT $9
