package k8sresource

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/runtime/schema"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"

	_ "embed"
)

var (
	serviceAccountGK     = schema.GroupKind{Group: "", Kind: "ServiceAccount"}
	serviceGK            = schema.GroupKind{Group: "", Kind: "Service"}
	podGK                = schema.GroupKind{Group: "", Kind: "Pod"}
	namespaceGK          = schema.GroupKind{Group: "", Kind: "Namespace"}
	roleGK               = schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "Role"}
	roleBindingGK        = schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "RoleBinding"}
	clusterRoleGK        = schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "ClusterRole"}
	clusterRoleBindingGK = schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "ClusterRoleBinding"}
	ingressGK            = schema.GroupKind{Group: "networking.k8s.io", Kind: "Ingress"}
	endpointSliceGK      = schema.GroupKind{Group: "discovery.k8s.io", Kind: "EndpointSlice"}
	endpointGK           = schema.GroupKind{Group: "", Kind: "Endpoint"}
	deploymentGK         = schema.GroupKind{Group: "apps", Kind: "Deployment"}
	replicaSetGK         = schema.GroupKind{Group: "apps", Kind: "ReplicaSet"}
	daemonSetGK          = schema.GroupKind{Group: "apps", Kind: "DaemonSet"}
	statefulSetGK        = schema.GroupKind{Group: "apps", Kind: "StatefulSet"}
	cronJobGK            = schema.GroupKind{Group: "batch", Kind: "CronJob"}
	jobGK                = schema.GroupKind{Group: "batch", Kind: "Job"}
)

func clusterScoped(gk schema.GroupKind) bool {
	return gk == clusterRoleGK || gk == clusterRoleBindingGK
}

func getAlwaysDisplayResourceKinds() map[schema.GroupKind]bool {
	alwaysDisplay := make(map[schema.GroupKind]bool)

	for _, gk := range []schema.GroupKind{
		// Workload resources
		deploymentGK,
		replicaSetGK,
		daemonSetGK,
		statefulSetGK,
		cronJobGK,
		jobGK,
		podGK,

		// Service and networking resources
		serviceGK,

		// RBAC resources
		serviceAccountGK,
		roleBindingGK,
		roleGK,
	} {
		alwaysDisplay[gk] = true
	}

	return alwaysDisplay
}

type RelationKey struct {
	Source schema.GroupKind
	Target schema.GroupKind
}

type ResourceRelation struct {
	Source    schema.GroupKind
	Target    schema.GroupKind
	JoinField string
	RefKind   RefKind
}

func (r ResourceRelation) Key() RelationKey {
	return RelationKey{Source: r.Source, Target: r.Target}
}

type RelationRegistry struct {
	relations map[RelationKey]ResourceRelation
}

func NewRelationRegistry() *RelationRegistry {
	return &RelationRegistry{
		relations: make(map[RelationKey]ResourceRelation),
	}
}

func (r *RelationRegistry) Add(relation ResourceRelation) {
	key := relation.Key()
	if _, exists := r.relations[key]; !exists {
		r.relations[key] = relation
	}
}

func (r *RelationRegistry) GetRelations(view ResourceView) []ResourceRelation {
	uniqueRelations := make(map[RelationKey]ResourceRelation)
	seen := make(map[schema.GroupKind]bool)

	var findRelations func(gk schema.GroupKind)
	findRelations = func(gk schema.GroupKind) {
		if seen[gk] {
			return
		}
		seen[gk] = true

		for key, relation := range r.relations {
			if relation.Source == gk {
				uniqueRelations[key] = relation
				findRelations(relation.Target)
			}
		}
	}

	for _, entryPoint := range view.EntryPoints {
		findRelations(entryPoint)
	}

	for _, relation := range view.Relations {
		key := relation.Key()
		if _, exists := uniqueRelations[key]; !exists {
			uniqueRelations[key] = relation
			findRelations(relation.Target)
		}
	}

	result := make([]ResourceRelation, 0, len(uniqueRelations))
	for _, relation := range uniqueRelations {
		result = append(result, relation)
	}
	return result
}

type ResourceView struct {
	EntryPoints     []schema.GroupKind
	Relations       []ResourceRelation
	ExcludeChildren []schema.GroupKind
}

var GlobalRegistry = initGlobalRegistry()

type RefKind int

const (
	RefKindSource RefKind = iota
	RefKindTarget
)

func initGlobalRegistry() *RelationRegistry {
	registry := NewRelationRegistry()
	commonRelations := []ResourceRelation{
		{
			Source:    serviceAccountGK,
			Target:    roleBindingGK,
			JoinField: "subjects",
			RefKind:   RefKindTarget,
		},
		{
			Source:    serviceAccountGK,
			Target:    clusterRoleBindingGK,
			JoinField: "subjects",
			RefKind:   RefKindTarget,
		},
		{
			Source:    roleBindingGK,
			Target:    roleGK,
			JoinField: "role",
			RefKind:   RefKindSource,
		},
		{
			Source:    roleBindingGK,
			Target:    clusterRoleGK,
			JoinField: "clusterrole",
			RefKind:   RefKindSource,
		},
		{
			Source:    clusterRoleBindingGK,
			Target:    clusterRoleGK,
			JoinField: "clusterrole",
			RefKind:   RefKindSource,
		},
		{
			Source:    ingressGK,
			Target:    serviceGK,
			JoinField: "services",
			RefKind:   RefKindSource,
		},
		{
			Source:    serviceGK,
			Target:    podGK,
			JoinField: "pods",
			RefKind:   RefKindSource,
		},
	}

	for _, relation := range commonRelations {
		registry.Add(relation)
	}

	return registry
}

var Views = map[schema.GroupKind]ResourceView{
	ingressGK: {
		EntryPoints: []schema.GroupKind{ingressGK},
		Relations: []ResourceRelation{
			{Source: ingressGK, Target: serviceGK},
		},
	},
	serviceGK: {
		EntryPoints: []schema.GroupKind{serviceGK},
		Relations: []ResourceRelation{
			{Source: serviceGK, Target: podGK},
		},
	},
	serviceAccountGK: {
		EntryPoints: []schema.GroupKind{serviceAccountGK},
		Relations: []ResourceRelation{
			{Source: serviceAccountGK, Target: roleBindingGK},
			{Source: serviceAccountGK, Target: clusterRoleBindingGK},
		},
	},
	roleBindingGK: {
		EntryPoints: []schema.GroupKind{roleBindingGK},
		Relations: []ResourceRelation{
			{Source: roleBindingGK, Target: roleGK},
			{Source: roleBindingGK, Target: clusterRoleGK},
		},
	},
	clusterRoleBindingGK: {
		EntryPoints: []schema.GroupKind{clusterRoleBindingGK},
		Relations: []ResourceRelation{
			{Source: clusterRoleBindingGK, Target: clusterRoleGK},
		},
	},
	namespaceGK: {
		EntryPoints: []schema.GroupKind{
			serviceAccountGK,
			ingressGK,
			serviceGK,
		},
		ExcludeChildren: []schema.GroupKind{
			endpointSliceGK,
			endpointGK,
		},
	},
}

func (s *Service) ListResourcesTreeview(ctx context.Context, instanceID, group, kind, version string, clusterIDs, namespaces []string, resourceNameContains string, resourceKinds []string, healthStatuses []organizationv1.HealthStatus, name string) ([]*organizationv1.ClusterResource, error) {
	if !isViewSupported(schema.GroupKind{Group: group, Kind: kind}) {
		return nil, status.Error(codes.InvalidArgument, "unsupported resource type")
	}

	queryParams := []any{
		s.organizationID,
		instanceID,
		pq.Array(clusterIDs),
		name,
	}
	view := Views[schema.GroupKind{Group: group, Kind: kind}]
	var childExclusions []string
	var exclusion string
	for _, exclude := range view.ExcludeChildren {
		childExclusions = append(childExclusions, fmt.Sprintf(`("group" = '%s' AND kind = '%s')`,
			exclude.Group,
			exclude.Kind,
		))
	}
	if len(childExclusions) > 0 {
		exclusion += fmt.Sprintf(" AND NOT (%s)", strings.Join(childExclusions, " OR "))
	}

	additionalResourceConditions := buildResourceConditions(view, GlobalRegistry)

	instances, err := s.ArgoCDInstances().ListAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch argocd instances: %w", err)
	}
	instanceHostnameMap := lo.SliceToMap(instances, func(item *models.ArgoCDInstance) (string, string) {
		return item.ID, item.StatusHostname.String
	})

	// get all resources in this namespace, and all cluster-scoped resources
	var referenceResources []*models.ArgoCDClusterK8SObject

	akiPermissionFilter := ""
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &queryParams)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch filter: %w", err)
		}
		akiPermissionFilter = "AND " + filter
	}
	referenceQuery := fmt.Sprintf(referenceResourceTreeviewQuery, additionalResourceConditions, exclusion, akiPermissionFilter)
	err = models.NewQuery(qm.SQL(referenceQuery, queryParams...)).Bind(ctx, s.db, &referenceResources)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch reference resources: %w", err)
	}

	relations := GlobalRegistry.GetRelations(view)
	results := s.buildResourceTree(referenceResources, relations, instanceHostnameMap, resourceNameContains, resourceKinds, healthStatuses)
	return results, nil
}

func buildResourceConditions(view ResourceView, registry *RelationRegistry) string {
	conditions := make(map[schema.GroupKind]bool)
	seen := make(map[schema.GroupKind]bool)

	var traverse func(gk schema.GroupKind)
	traverse = func(gk schema.GroupKind) {
		if seen[gk] {
			return
		}
		seen[gk] = true
		conditions[gk] = true

		for _, relation := range registry.relations {
			if relation.Source == gk {
				traverse(relation.Target)
			}
		}
	}

	for _, entryPoint := range view.EntryPoints {
		traverse(entryPoint)
	}

	for _, relation := range view.Relations {
		conditions[relation.Source] = true
		conditions[relation.Target] = true
	}

	var parts []string
	for gk := range conditions {
		excluded := false
		for _, exclude := range view.ExcludeChildren {
			if gk == exclude {
				excluded = true
				break
			}
		}
		if excluded {
			continue
		}

		if clusterScoped(gk) {
			parts = append(parts, fmt.Sprintf(`(namespace = '' AND "group" = '%s' AND kind = '%s')`,
				gk.Group,
				gk.Kind,
			))
		} else {
			parts = append(parts, fmt.Sprintf(`(namespace = $4 AND "group" = '%s' AND kind = '%s')`,
				gk.Group,
				gk.Kind,
			))
		}
	}
	if len(parts) == 0 {
		return ""
	}
	return fmt.Sprintf(" OR (%s)", strings.Join(parts, " OR "))
}

func isViewSupported(gk schema.GroupKind) bool {
	return gk == namespaceGK
}

//go:embed treeview_reference_resource.sql
var referenceResourceTreeviewQuery string

type TreeViewRow struct {
	ID                    string    `db:"id"`
	ClusterID             string    `db:"cluster_id"`
	OrganizationID        string    `db:"organization_id"`
	InstanceID            string    `db:"instance_id"`
	Name                  string    `db:"name"`
	Namespace             string    `db:"namespace"`
	Group                 string    `db:"group"`
	Version               string    `db:"version"`
	Kind                  string    `db:"kind"`
	Columns               null.JSON `db:"columns"`
	CreationTimestamp     time.Time `db:"creation_timestamp"`
	ArgocdApplicationInfo null.JSON `db:"argocd_application_info"`
	OwnerID               string    `db:"owner_id"`
}

func translateTreeviewColumnValues(row *models.ArgoCDClusterK8SObject, columns map[string]any) map[string]string {
	gk := schema.GroupKind{
		Group: row.Group.String,
		Kind:  row.Kind.String,
	}
	newColumns := make(map[string]string)
	if columnTranslators, ok := columnTranslators[gk]; ok {
		newColumns = columnTranslators.valueTranslator(columns)
	} else {
		for n, v := range columns {
			newColumns[n] = toString(v)
		}
	}
	return newColumns
}

type resourceKey struct {
	GroupKind schema.GroupKind
	Name      string
	Namespace string
}

func (s *Service) buildResourceTree(rawResources []*models.ArgoCDClusterK8SObject, relations []ResourceRelation, instanceHostnameMap map[string]string, resourceNameContains string, resourceKinds []string, healthStatuses []organizationv1.HealthStatus) []*organizationv1.ClusterResource {
	// if the owner is not in the resources, do not include the resource in the treeview
	m := make(map[string]struct{}, len(rawResources))
	for _, r := range rawResources {
		m[r.ID] = struct{}{}
	}
	resources := make([]*models.ArgoCDClusterK8SObject, 0, len(rawResources))
	for _, r := range rawResources {
		if r.OwnerID.String == "" {
			resources = append(resources, r)
			continue
		}
		if _, ok := m[r.OwnerID.String]; ok {
			resources = append(resources, r)
		}
	}

	resourceMap := make(map[string]*organizationv1.ClusterResource)
	resourceKeyToID := make(map[resourceKey]string)
	hasRelationship := make(map[string]bool)
	makeKey := func(r *models.ArgoCDClusterK8SObject) resourceKey {
		return resourceKey{
			GroupKind: schema.GroupKind{Group: r.Group.String, Kind: r.Kind.String},
			Name:      r.Name,
			Namespace: r.Namespace.String,
		}
	}

	// initialize all resources, only add non-nil
	for _, r := range resources {
		clusterResource := convertToClusterResource(r, instanceHostnameMap[r.InstanceID])
		if clusterResource == nil {
			continue
		}
		resourceMap[r.ID] = clusterResource
		resourceKeyToID[makeKey(r)] = r.ID
		if r.OwnerID.String != "" {
			hasRelationship[r.ID] = true
			hasRelationship[r.OwnerID.String] = true
		}
	}

	relationLookup := make(map[schema.GroupKind][]ResourceRelation)
	for _, relation := range relations {
		if relation.RefKind == RefKindSource {
			relationLookup[relation.Source] = append(relationLookup[relation.Source], relation)
		} else {
			relationLookup[relation.Target] = append(relationLookup[relation.Target], relation)
		}
	}

	// build resource defined relationship
	for _, resource := range resources {
		// build owner reference relationship
		if parent, exists := resourceMap[resource.OwnerID.String]; exists {
			parent.ReferenceResources = append(parent.ReferenceResources, resource.ID)
		}

		resourceGK := schema.GroupKind{Group: resource.Group.String, Kind: resource.Kind.String}
		relevantRelations, exists := relationLookup[resourceGK]
		if !exists {
			continue
		}

		columns, err := resource.GetColumns()
		if err != nil {
			continue
		}

		// build custom defined relationships
		for _, relation := range relevantRelations {
			otherGK := relation.Target
			if relation.RefKind == RefKindTarget {
				otherGK = relation.Source
			}

			refs := extractReferences(columns, relation.JoinField, resource.Namespace.String, otherGK)
			resourceKey := resourceKey{
				GroupKind: resourceGK,
				Name:      resource.Name,
				Namespace: resource.Namespace.String,
			}
			currentResourceID := resourceKeyToID[resourceKey]

			for _, ref := range refs {
				if otherResourceID, exists := resourceKeyToID[ref]; exists {
					if relation.RefKind == RefKindSource {
						resourceMap[currentResourceID].ReferenceResources = append(
							resourceMap[currentResourceID].ReferenceResources,
							resourceMap[otherResourceID].Uid,
						)
					} else {
						resourceMap[otherResourceID].ReferenceResources = append(
							resourceMap[otherResourceID].ReferenceResources,
							resourceMap[currentResourceID].Uid,
						)
					}
					// cluster resources are obtained from the query as well. If they are not associated with namespaced resources,
					// for example only clusterrolebinding -> clusterrole, we need to not show them in the treeview
					if !clusterScoped(resourceGK) || !clusterScoped(otherGK) || hasRelationship[currentResourceID] || hasRelationship[otherResourceID] {
						hasRelationship[currentResourceID] = true
						hasRelationship[otherResourceID] = true
					}
				}
			}
		}
	}

	// create initial filtered resource map, only include resources with relationships
	filteredResourcesMap := make(map[string]*organizationv1.ClusterResource)
	results := make([]*organizationv1.ClusterResource, 0)

	alwaysDisplayKinds := getAlwaysDisplayResourceKinds()

	for id, resource := range resourceMap {
		resourceGK := schema.GroupKind{Group: resource.Group, Kind: resource.Kind}
		if hasRelationship[id] || alwaysDisplayKinds[resourceGK] {
			filteredResourcesMap[id] = resource
		}
	}

	// Apply health status filtering
	if len(healthStatuses) > 0 {
		isHealthyStatusContains := lo.Contains(healthStatuses, organizationv1.HealthStatus_HEALTH_STATUS_HEALTHY)
		// Find resources matching health status criteria
		healthyResourceIDs := make(map[string]bool)
		for id, resource := range filteredResourcesMap {
			// If the resource has no health status, treat it as healthy. Keep the filter handling logic similar to argoCD.
			if resource.ArgocdApplicationInfo == nil || resource.ArgocdApplicationInfo.HealthStatus.String() == "" {
				if isHealthyStatusContains {
					healthyResourceIDs[id] = true
				}
				continue
			}

			for _, h := range healthStatuses {
				if h == resource.ArgocdApplicationInfo.HealthStatus {
					healthyResourceIDs[id] = true
					break
				}
			}
		}

		// If no resources match health status criteria, return empty result
		if len(healthyResourceIDs) == 0 {
			return []*organizationv1.ClusterResource{}
		}

		// Mark all resources related to resources with matching health status
		healthRelatedResourceIDs := make(map[string]bool)
		workingResources := make(map[string]*organizationv1.ClusterResource)
		for id, resource := range filteredResourcesMap {
			workingResources[id] = resource
		}

		for id := range healthyResourceIDs {
			markRelatedResourcesInTree(id, workingResources, healthRelatedResourceIDs)
		}

		// Keep only resources related to health status matches
		for id := range filteredResourcesMap {
			if !healthRelatedResourceIDs[id] {
				delete(filteredResourcesMap, id)
			}
		}
	}

	// Apply name and kind filtering
	if resourceNameContains != "" || len(resourceKinds) > 0 {
		nameKindMatchingIDs := make(map[string]bool)

		for id, resource := range filteredResourcesMap {
			// Check kind
			kindMatch := len(resourceKinds) == 0
			for _, k := range resourceKinds {
				if k == resource.Kind {
					kindMatch = true
					break
				}
			}

			// Check name
			nameMatch := resourceNameContains == ""
			if resourceNameContains != "" && strings.Contains(strings.ToLower(resource.Name), strings.ToLower(resourceNameContains)) {
				nameMatch = true
			}

			if kindMatch && nameMatch {
				nameKindMatchingIDs[id] = true
			}
		}

		// Keep only resources that match both name and kind filter criteria
		for id := range filteredResourcesMap {
			if !nameKindMatchingIDs[id] {
				delete(filteredResourcesMap, id)
			}
		}
	}

	// Collect results
	for _, resource := range filteredResourcesMap {
		results = append(results, resource)
	}

	// Sort results to ensure consistent order for all possible scenarios
	sort.SliceStable(results, func(i, j int) bool {
		return compareResources(results[i], results[j])
	})

	return results
}

// compareResources compares two resources for sorting
// Returns true if the first resource should come before the second
func compareResources(res1, res2 *organizationv1.ClusterResource) bool {
	// 1. First sort by Group
	if res1.Group != res2.Group {
		// Core API group (empty string) comes first
		if res1.Group == "" {
			return true
		}
		if res2.Group == "" {
			return false
		}
		return res1.Group < res2.Group
	}

	// 2. Then sort by Kind
	if res1.Kind != res2.Kind {
		return res1.Kind < res2.Kind
	}

	// 3. Then sort by Namespace
	if res1.Namespace != res2.Namespace {
		return res1.Namespace < res2.Namespace
	}

	// 4. Finally sort by Name
	return res1.Name < res2.Name
}

// markRelatedResourcesInTree marks all resources related to the specified resource using non-recursive approach
func markRelatedResourcesInTree(resourceID string, filteredResources map[string]*organizationv1.ClusterResource, markedIDs map[string]bool) {
	// Use a queue to store resource IDs that need to be processed
	pendingIDs := []string{resourceID}
	// This is a safety measure in case there are circular dependencies
	maxDepth := 10
	visited := make(map[string]bool)
	for depth := 0; len(pendingIDs) > 0 && depth < maxDepth; depth++ {
		var nextBatchIDs []string

		// Process all resources at current depth level
		for _, currentID := range pendingIDs {
			// Skip if already visited to avoid circular references
			if visited[currentID] {
				continue
			}
			visited[currentID] = true

			// Mark current resource
			markedIDs[currentID] = true

			resource, exists := filteredResources[currentID]
			if !exists {
				continue
			}

			// Collect all resources referenced by this resource
			for _, refID := range resource.ReferenceResources {
				if !visited[refID] {
					nextBatchIDs = append(nextBatchIDs, refID)
				}
			}

			// Collect all resources referencing this resource
			for uid, otherResource := range filteredResources {
				if visited[uid] {
					continue
				}
				for _, refID := range otherResource.ReferenceResources {
					if refID == currentID {
						nextBatchIDs = append(nextBatchIDs, uid)
					}
				}
			}
			delete(filteredResources, currentID)
		}

		// Update queue with next batch of resources to process
		pendingIDs = nextBatchIDs
	}
}

func extractReferences(columns map[string]any, joinField, defaultNamespace string, targetGK schema.GroupKind) []resourceKey {
	value, exists := columns[joinField]
	if !exists {
		return nil
	}

	switch joinField {
	case "subjects":
		return extractSubjectRefs(value, defaultNamespace, targetGK)
	case "role":
		return extractRoleRefs(value, defaultNamespace, targetGK)
	case "clusterrole":
		return extractClusterRoleRefs(value, targetGK)
	case "services":
		return extractServiceRefs(value, defaultNamespace, targetGK)
	case "pods":
		return extractPodRefs(value, targetGK)
	default:
		return nil
	}
}

func extractSubjectRefs(value any, defaultNamespace string, targetGK schema.GroupKind) []resourceKey {
	var subjects []string
	if err := json.Unmarshal([]byte(value.(string)), &subjects); err != nil {
		return nil
	}

	refs := make([]resourceKey, 0, len(subjects))
	for _, subject := range subjects {
		parts := strings.Split(subject, "/")
		if len(parts) == 2 {
			namespace := parts[0]
			if namespace == "" {
				namespace = defaultNamespace
			}
			refs = append(refs, resourceKey{
				GroupKind: targetGK,
				Namespace: namespace,
				Name:      parts[1],
			})
		}
	}
	return refs
}

func extractRoleRefs(value any, defaultNamespace string, targetGK schema.GroupKind) []resourceKey {
	return []resourceKey{{
		GroupKind: targetGK,
		Namespace: defaultNamespace,
		Name:      fmt.Sprintf("%v", value),
	}}
}

func extractClusterRoleRefs(value any, targetGK schema.GroupKind) []resourceKey {
	return []resourceKey{{
		GroupKind: targetGK,
		Name:      fmt.Sprintf("%v", value),
	}}
}

func extractServiceRefs(value any, defaultNamespace string, targetGK schema.GroupKind) []resourceKey {
	var services []string
	if err := json.Unmarshal([]byte(value.(string)), &services); err != nil {
		return nil
	}

	refs := make([]resourceKey, 0, len(services))
	for _, svc := range services {
		namespace := defaultNamespace
		name := svc
		if parts := strings.Split(svc, "/"); len(parts) == 2 {
			namespace = parts[0]
			name = parts[1]
		}
		refs = append(refs, resourceKey{
			GroupKind: targetGK,
			Namespace: namespace,
			Name:      name,
		})
	}
	return refs
}

func extractPodRefs(value any, targetGK schema.GroupKind) []resourceKey {
	var pods []string
	if err := json.Unmarshal([]byte(value.(string)), &pods); err != nil {
		return nil
	}

	refs := make([]resourceKey, 0, len(pods))
	for _, pod := range pods {
		parts := strings.Split(pod, "/")
		if len(parts) == 2 {
			refs = append(refs, resourceKey{
				GroupKind: targetGK,
				Namespace: parts[0],
				Name:      parts[1],
			})
		}
	}
	return refs
}

func convertToClusterResource(row *models.ArgoCDClusterK8SObject, hostname string) *organizationv1.ClusterResource {
	if row == nil {
		return nil
	}

	columns, err := row.GetColumns()
	if err != nil {
		return nil
	}
	newColumns := translateTreeviewColumnValues(row, columns)

	var argoInfo *agentv1.ArgoCDApplicationInfo
	if !row.ArgocdApplicationInfo.IsZero() {
		if err := row.ArgocdApplicationInfo.Unmarshal(&argoInfo); err != nil {
			return nil
		}
	}

	appInfo := translateArgoAppInfo(argoInfo, nil)
	if appInfo != nil {
		appInfo.Link = buildArgoCDLink(row.Group.String, row.Kind.String, row.Namespace.String, row.Name, hostname, appInfo.Name)
	}

	return &organizationv1.ClusterResource{
		InstanceId:            row.InstanceID,
		ClusterId:             row.ClusterID,
		Name:                  row.Name,
		Namespace:             row.Namespace.String,
		Group:                 row.Group.String,
		Version:               row.Version.String,
		Kind:                  row.Kind.String,
		Uid:                   row.ID,
		CreateTime:            row.CreationTimestamp.Format(time.RFC3339),
		Columns:               newColumns,
		ArgocdApplicationInfo: appInfo,
	}
}
