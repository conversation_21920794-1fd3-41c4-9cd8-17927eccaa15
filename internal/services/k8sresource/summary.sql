WITH node_count AS (
  SELECT COUNT(*) AS count
  FROM argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND "group" = ''
    AND kind = 'Node'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
),

pod_count AS (
  SELECT COUNT(*) AS count
  FROM argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND "group" = ''
    AND kind = 'Pod'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
),

container_count AS (
  SELECT COUNT(*) AS count
  FROM argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND "group" = 'dashboard.akuity.io'
    AND kind = 'Container'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
),

stuck_deletion_count AS (
  SELECT COUNT(*) AS count
  FROM argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND deletion_timestamp IS NOT NULL
    AND deletion_timestamp < NOW() - INTERVAL '1 hour'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
),

container_image_data AS (
  SELECT
    columns->>'image' AS container_image_name,
    columns->>'tag' AS container_image_tag,
    COALESCE(columns->>'digest', '') AS container_image_digest
  FROM argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND "group" = 'dashboard.akuity.io'
    AND kind = 'Container'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    %[1]s
  GROUP BY
    columns->>'image',
    columns->>'tag',
    COALESCE(columns->>'digest', '')
),

image_data AS (
  SELECT DISTINCT
    CONCAT(argo_cd_cluster_k8s_object.columns->>'image', ':', argo_cd_cluster_k8s_object.columns->>'tag', ':', COALESCE(argo_cd_cluster_k8s_object.columns->>'digest', '')) AS image_key,
    (argo_cd_cluster_k8s_object.columns->>'cveCount')::int > 0 AS has_cve
  FROM argo_cd_cluster_k8s_object
  INNER JOIN 
    container_image_data
  ON 
    argo_cd_cluster_k8s_object.columns->>'image' = container_image_data.container_image_name
    AND argo_cd_cluster_k8s_object.columns->>'tag' = container_image_data.container_image_tag
    AND COALESCE(argo_cd_cluster_k8s_object.columns->>'digest', '') = container_image_data.container_image_digest
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND "group" = 'dashboard.akuity.io'
    AND kind = 'Image'
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    AND argo_cd_cluster_k8s_object.columns->>'image' IS NOT NULL
    AND argo_cd_cluster_k8s_object.columns->>'tag' IS NOT NULL
    %[1]s
),

image_stats AS (
  SELECT
    COUNT(*) AS image_count,
    SUM(CASE WHEN has_cve THEN 1 ELSE 0 END) AS cve_count
  FROM image_data
),

incidents_data AS (
  SELECT COUNT(*) AS incident_count
  FROM ai_conversation
  WHERE
    organization_id = $1
    AND (metadata->>'incident' IS NOT NULL AND metadata->>'incident' != '')
    AND (metadata->'incident'->>'resolvedAt' IS NULL OR metadata->'incident'->>'resolvedAt' = '')
    AND (public = true OR user_id = 'assistant' OR user_id = $4)
)

SELECT
  n.count AS node_count,
  p.count AS pod_count,
  c.count AS container_count,
  s.count AS stuck_deletion_count,
  i.image_count AS image_count,
  COALESCE(i.cve_count, 0) AS cve_count,
  inc.incident_count
FROM 
  node_count n,
  pod_count p,
  container_count c,
  stuck_deletion_count s,
  image_stats i,
  incidents_data inc