package k8sresource

import (
	"context"
	"math"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type podCount struct {
	NodeName string `boil:"node_name"`
	PodCount int    `boil:"pod_count"`
}

type FillerInfo struct {
	MinVal float64
	MaxVal float64
	Unit   organizationv1.FillValueUnit
}

func (s *Service) ListKubernetesNodes(ctx context.Context, clusterIDs []string,
	groupBy []organizationv1.NodeGroupBy, filler organizationv1.NodeFiller) ([]*organizationv1.KubernetesNode, *FillerInfo, error,
) {
	mods := []qm.QueryMod{
		models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Node")),
	}
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilter(ctx, "")
		if err != nil {
			return nil, nil, err
		}
		mods = append(mods, filter)
	}

	nodes, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx)
	if err != nil {
		return nil, nil, err
	}

	var countsMap map[string]int
	if filler == organizationv1.NodeFiller_NODE_FILLER_USAGE_PODS || filler == organizationv1.NodeFiller_NODE_FILLER_ALLOCATED_PODS {
		podMods := []qm.QueryMod{
			models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs),
			models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
			models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Pod")),
			qm.Select("coalesce(columns->>'nodeName', '') as node_name", "count(*) as pod_count"),
			qm.GroupBy("coalesce(columns->>'nodeName', '')"),
			qm.Where("(columns->>'phase' is null or columns->>'phase' != 'Succeeded')"),
		}

		counts := make([]podCount, 0)
		err = models.ArgoCDClusterK8SObjects(podMods...).Bind(ctx, s.db, &counts)
		if err != nil {
			return nil, nil, err
		}
		countsMap = lo.SliceToMap(counts, func(item podCount) (string, int) {
			return item.NodeName, item.PodCount
		})
	}

	apiNodes := make([]*organizationv1.KubernetesNode, 0, len(nodes))
	fi := &FillerInfo{}
	var minVal, maxVal float64
	switch filler {
	case organizationv1.NodeFiller_NODE_FILLER_UNSPECIFIED,
		organizationv1.NodeFiller_NODE_FILLER_USAGE_CPU,
		organizationv1.NodeFiller_NODE_FILLER_USAGE_MEMORY,
		organizationv1.NodeFiller_NODE_FILLER_USAGE_PODS:
		fi.Unit = organizationv1.FillValueUnit_FILL_VALUE_UNIT_PERCENTAGE
		minVal = 0
		maxVal = 100
	default:
		fi.Unit = organizationv1.FillValueUnit_FILL_VALUE_UNIT_COUNT
		minVal = math.MaxFloat64
		maxVal = 0
	}
	for _, n := range nodes {
		columns := &NodeColumns{}
		if err := n.Columns.Unmarshal(columns); err != nil {
			return nil, nil, err
		}
		clusterName := ""
		if n.R != nil && n.R.Cluster != nil {
			clusterName = n.R.Cluster.Name
		}
		instanceName := ""
		if n.R != nil && n.R.Instance != nil {
			instanceName = n.R.Instance.Name
		}
		node := &organizationv1.KubernetesNode{
			Id:           n.ID,
			Name:         n.Name,
			Groups:       getNodeGroupByValue(n, columns, groupBy),
			ClusterName:  clusterName,
			InstanceName: instanceName,
			InstanceId:   n.InstanceID,
		}

		fillValue := getNodeFillValue(countsMap[n.Name], columns, filler)
		if fi.Unit != organizationv1.FillValueUnit_FILL_VALUE_UNIT_PERCENTAGE {
			if fillValue == nil {
				minVal = 0
			} else {
				minVal = math.Min(minVal, *fillValue)
				maxVal = math.Max(maxVal, *fillValue)
			}
		}
		node.FillValue = fillValue

		apiNodes = append(apiNodes, node)
	}
	fi.MinVal = minVal
	fi.MaxVal = maxVal

	return apiNodes, fi, nil
}

type NodeColumns struct {
	CRI               string   `json:"cri,omitempty"`
	Zone              string   `json:"zone,omitempty"`
	Region            string   `json:"region,omitempty"`
	Kubelet           string   `json:"kubelet,omitempty"`
	Hostname          string   `json:"hostname,omitempty"`
	Platform          string   `json:"platform,omitempty"`
	AllocatableCPU    float64  `json:"allocatable.cpu,omitempty"`
	AllocatableMemory float64  `json:"allocatable.memory,omitempty"`
	UsageCPU          *float64 `json:"usage.cpu,omitempty"`
	UsageMemory       *float64 `json:"usage.memory,omitempty"`
	AllocatablePod    int      `json:"allocatable.pod,omitempty"`
}

func getNodeFillValue(podCount int, columns *NodeColumns, filler organizationv1.NodeFiller) *float64 {
	switch filler {
	case organizationv1.NodeFiller_NODE_FILLER_UNSPECIFIED, organizationv1.NodeFiller_NODE_FILLER_USAGE_CPU:
		return safePercentage(columns.UsageCPU, columns.AllocatableCPU)
	case organizationv1.NodeFiller_NODE_FILLER_USAGE_MEMORY:
		return safePercentage(columns.UsageMemory, columns.AllocatableMemory)
	case organizationv1.NodeFiller_NODE_FILLER_ALLOCATED_PODS:
		return ptr.To[float64](float64(podCount))
	case organizationv1.NodeFiller_NODE_FILLER_USAGE_PODS:
		return safePercentage(ptr.To(float64(podCount)), float64(columns.AllocatablePod))
	}
	return nil
}

func safePercentage(numerator *float64, denominator float64) *float64 {
	if numerator == nil {
		return nil
	}
	if denominator <= 0 {
		return nil
	}
	if *numerator > denominator {
		return ptr.To(100.0)
	}
	result := *numerator / denominator * 100
	return &result
}

func getNodeGroupByValue(object *models.ArgoCDClusterK8SObject, columns *NodeColumns, groupBy []organizationv1.NodeGroupBy) []string {
	res := make([]string, 0, len(groupBy))
	for _, g := range groupBy {
		switch g {
		case organizationv1.NodeGroupBy_NODE_GROUP_BY_UNSPECIFIED, organizationv1.NodeGroupBy_NODE_GROUP_BY_CLUSTER:
			if object.R != nil && object.R.Cluster != nil {
				res = append(res, object.R.Cluster.Name)
			} else {
				res = append(res, "")
			}
		case organizationv1.NodeGroupBy_NODE_GROUP_BY_AVAILABILITY_ZONE:
			res = append(res, columns.Zone)
		case organizationv1.NodeGroupBy_NODE_GROUP_BY_REGION:
			res = append(res, columns.Region)
		case organizationv1.NodeGroupBy_NODE_GROUP_BY_HOSTNAME:
			res = append(res, columns.Hostname)
		default:
			res = append(res, "")
		}
	}
	return res
}

func (s *Service) GetKubernetesNode(ctx context.Context, instanceID, clusterID, nodeID string) (*organizationv1.KubernetesNode, error) {
	node, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Node")),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
	).GetByID(ctx, nodeID)
	if err != nil {
		return nil, err
	}

	columns := &NodeColumns{}
	if err := node.Columns.Unmarshal(columns); err != nil {
		return nil, err
	}

	var clusterName string
	if node.R != nil && node.R.Cluster != nil {
		clusterName = node.R.Cluster.Name
	}
	var instanceName string
	if node.R != nil && node.R.Instance != nil {
		instanceName = node.R.Instance.Name
	}
	apiNode := &organizationv1.KubernetesNode{
		Id:               node.ID,
		Name:             node.Name,
		Hostname:         optionalString(columns.Hostname),
		Cri:              optionalString(columns.CRI),
		AvailabilityZone: optionalString(columns.Zone),
		Region:           optionalString(columns.Region),
		Platform:         optionalString(columns.Platform),
		KubeletVersion:   optionalString(columns.Kubelet),
		ClusterName:      clusterName,
		InstanceName:     instanceName,
		InstanceId:       instanceID,
	}
	if columns.UsageCPU != nil {
		value := *columns.UsageCPU / columns.AllocatableCPU * 100
		apiNode.UsageCpu = &value
	}
	if columns.UsageMemory != nil {
		value := *columns.UsageMemory / columns.AllocatableMemory * 100
		apiNode.UsageMemory = &value
	}

	podMods := []qm.QueryMod{
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Pod")),
		qm.Where("coalesce(columns->>'nodeName', '') = ?", node.Name),
		qm.Where("(columns->>'phase' is null or columns->>'phase' != 'Succeeded')"),
	}
	podCount, err := models.ArgoCDClusterK8SObjects(podMods...).Count(ctx, s.db)
	if err != nil {
		return nil, err
	}
	apiNode.AllocatedPods = &podCount

	return apiNode, nil
}

func optionalString(value string) *string {
	if value == "" {
		return nil
	}
	return &value
}
