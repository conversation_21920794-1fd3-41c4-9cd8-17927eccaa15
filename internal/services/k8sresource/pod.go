package k8sresource

import (
	"context"
	"fmt"
	"math"

	"github.com/aws/smithy-go/ptr"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *Service) ListKubernetesPods(ctx context.Context, enabledClusters []*EnabledCluster,
	groupBy []organizationv1.PodGroupBy, filler organizationv1.PodFiller) ([]*organizationv1.KubernetesPod, *FillerInfo, error,
) {
	clusterIDs := lo.Map(enabledClusters, func(cluster *EnabledCluster, _ int) string {
		return cluster.ID
	})
	clusterMappings := lo.SliceToMap(enabledClusters, func(cluster *EnabledCluster) (string, string) {
		return cluster.ID, cluster.Name
	})
	mods := []qm.QueryMod{
		models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs),
		qm.Where(
			`(("group" = '' and kind = 'Pod') or ("group" = '' and kind = 'Node'))`,
		),
	}
	if s.enforcer != nil {
		akiPermissionFilter, err := s.enforcer.GetK8SResourceListFilter(ctx, "")
		if err != nil {
			return nil, nil, err
		}
		mods = append(mods, akiPermissionFilter)
	}

	objs, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx)
	if err != nil {
		return nil, nil, err
	}

	pods := map[string]*models.ArgoCDClusterK8SObject{}
	nodesInfo := map[string]NodeColumns{}
	nodeKey := func(clusterID, nodeName string) string {
		return fmt.Sprintf("%s/%s", clusterID, nodeName)
	}
	podKey := func(clusterID, objectID string) string {
		return fmt.Sprintf("%s/%s", clusterID, objectID)
	}
	for _, obj := range objs {
		if obj.IsPod() {
			pods[podKey(obj.ClusterID, obj.ID)] = obj
		} else if obj.IsNode() {
			var columns NodeColumns
			if err := obj.Columns.Unmarshal(&columns); err != nil {
				return nil, nil, err
			}
			nodesInfo[nodeKey(obj.ClusterID, obj.Name)] = columns
		}
	}

	results := make([]*organizationv1.KubernetesPod, 0)
	var fi *FillerInfo
	switch filler {
	case organizationv1.PodFiller_POD_FILLER_UNSPECIFIED, organizationv1.PodFiller_POD_FILLER_USAGE_CPU, organizationv1.PodFiller_POD_FILLER_USAGE_MEMORY:
		fi = &FillerInfo{
			Unit:   organizationv1.FillValueUnit_FILL_VALUE_UNIT_COUNT,
			MinVal: math.MaxFloat64,
			MaxVal: 0,
		}
	case organizationv1.PodFiller_POD_FILLER_STATUS:
		fi = &FillerInfo{
			Unit:   organizationv1.FillValueUnit_FILL_VALUE_UNIT_PERCENTAGE,
			MinVal: 0,
			MaxVal: 100,
		}
	default:
		return nil, nil, fmt.Errorf("unsupported filler %v", filler)
	}
	var hasData bool
	for _, pod := range pods {
		columns := &PodColumns{}
		if err := pod.Columns.Unmarshal(columns); err != nil {
			return nil, nil, err
		}
		fillValue := getPodFillValue(filler, columns)
		if fillValue != nil {
			hasData = true
			if fi.Unit == organizationv1.FillValueUnit_FILL_VALUE_UNIT_PERCENTAGE {
				fillValue = ptr.Float64(*fillValue * 100)
			}
			if *fillValue < fi.MinVal {
				fi.MinVal = *fillValue
			}
			if *fillValue > fi.MaxVal {
				fi.MaxVal = *fillValue
			}
		}

		nodeInfo := nodesInfo[nodeKey(pod.ClusterID, columns.NodeName)]
		instanceName := ""
		if pod.R != nil && pod.R.Instance != nil {
			instanceName = pod.R.Instance.Name
		}
		results = append(results, &organizationv1.KubernetesPod{
			Id:           pod.GetID(),
			Name:         pod.Name,
			Namespace:    pod.Namespace.String,
			ClusterName:  clusterMappings[pod.ClusterID],
			FillValue:    fillValue,
			Groups:       getPodGroupByValue(groupBy, columns.NodeName, clusterMappings[pod.ClusterID], nodeInfo),
			InstanceName: instanceName,
			InstanceId:   pod.InstanceID,
		})
	}
	if !hasData {
		fi = nil
	}
	return results, fi, nil
}

func (s *Service) GetKubernetesPod(ctx context.Context, instanceID string, cluster *EnabledCluster, podID string) (*organizationv1.KubernetesPodDetail, error) {
	pod, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Pod")),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(cluster.ID),
	).GetByID(ctx, podID)
	if err != nil {
		return nil, err
	}
	podColumns := &PodColumns{}
	if err := pod.Columns.Unmarshal(podColumns); err != nil {
		return nil, err
	}

	node, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom("")),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Node")),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(cluster.ID),
		models.ArgoCDClusterK8SObjectWhere.Name.EQ(podColumns.NodeName),
	).One(ctx)
	if err != nil {
		return nil, err
	}
	nodeColumns := &NodeColumns{}
	if err := node.Columns.Unmarshal(nodeColumns); err != nil {
		return nil, err
	}

	var status organizationv1.KubernetesPodStatus
	switch podColumns.Phase {
	case "Failed":
		status = organizationv1.KubernetesPodStatus_KUBERNETES_POD_STATUS_FAILED
	case "Pending":
		status = organizationv1.KubernetesPodStatus_KUBERNETES_POD_STATUS_PENDING
	case "Running":
		status = organizationv1.KubernetesPodStatus_KUBERNETES_POD_STATUS_RUNNING
	case "Succeeded":
		status = organizationv1.KubernetesPodStatus_KUBERNETES_POD_STATUS_SUCCEEDED
	}

	var usageCpu, usageMemory *float64
	if podColumns.UsageCPU != nil {
		usageCpu = ptr.Float64(*podColumns.UsageCPU)
	}
	if podColumns.UsageMemory != nil {
		usageMemory = ptr.Float64(*podColumns.UsageMemory)
	}
	instanceName := ""
	if pod.R != nil && pod.R.Instance != nil {
		instanceName = pod.R.Instance.Name
	}
	return &organizationv1.KubernetesPodDetail{
		Id:               pod.GetID(),
		Name:             pod.Name,
		Namespace:        pod.Namespace.String,
		NodeName:         podColumns.NodeName,
		ClusterName:      cluster.Name,
		Region:           nodeColumns.Region,
		AvailabilityZone: nodeColumns.Zone,
		Status:           status,
		UsageCpu:         usageCpu,
		UsageMemory:      usageMemory,
		InstanceName:     instanceName,
		InstanceId:       instanceID,
	}, nil
}

type PodColumns struct {
	Phase       string   `json:"phase"`
	NodeName    string   `json:"nodeName"`
	UsageCPU    *float64 `json:"usage.cpu,omitempty"`
	UsageMemory *float64 `json:"usage.memory,omitempty"`
}

func getPodFillValue(filler organizationv1.PodFiller, podColumns *PodColumns) *float64 {
	var value *float64
	switch filler {
	case organizationv1.PodFiller_POD_FILLER_UNSPECIFIED, organizationv1.PodFiller_POD_FILLER_USAGE_CPU:
		if podColumns.UsageCPU != nil {
			value = podColumns.UsageCPU
		}
	case organizationv1.PodFiller_POD_FILLER_USAGE_MEMORY:
		if podColumns.UsageMemory != nil {
			value = podColumns.UsageMemory
		}
	case organizationv1.PodFiller_POD_FILLER_STATUS:
		var percent float64
		// Based on https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#pod-phase.
		switch podColumns.Phase {
		case "Failed":
			percent = 1
		case "Pending":
			percent = 0.5
		case "Running":
			// TODO handle the case where the pod is running but not ready
			percent = 0
		case "Succeeded":
			percent = 0.2
		}
		value = &percent
	}
	return value
}

func getPodGroupByValue(groupBy []organizationv1.PodGroupBy, nodeName, clusterName string, nodeInfo NodeColumns) []string {
	res := make([]string, 0, len(groupBy))
	for _, g := range groupBy {
		switch g {
		case organizationv1.PodGroupBy_POD_GROUP_BY_UNSPECIFIED, organizationv1.PodGroupBy_POD_GROUP_BY_CLUSTER:
			res = append(res, clusterName)
		case organizationv1.PodGroupBy_POD_GROUP_BY_AVAILABILITY_ZONE:
			res = append(res, nodeInfo.Zone)
		case organizationv1.PodGroupBy_POD_GROUP_BY_REGION:
			res = append(res, nodeInfo.Region)
		case organizationv1.PodGroupBy_POD_GROUP_BY_NODE:
			res = append(res, nodeName)
		}
	}
	return res
}
