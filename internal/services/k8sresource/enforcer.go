package k8sresource

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"

	argocdclient "github.com/akuityio/agent/pkg/client"
	argocdv1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/types/v1"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/features"
	metadatautil "github.com/akuityio/akuity-platform/internal/utils/grpc/metadata"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/utils/grpc/metadata"
)

type Destination struct {
	PermissionSpec   argocdv1.AppProjectSpec
	ClusterID        string
	ClusterName      string
	NamespacePattern string
}

func (d *Destination) MatchNamespace(namespace string) bool {
	return matchGlob(d.NamespacePattern, namespace)
}

type Enforcer struct {
	orgID      string
	actor      *accesscontrol.Actor
	repoSet    client.RepoSet
	db         *sql.DB
	hostConfig *rest.Config
	logger     logr.Logger
	featureSvc features.Service
}

func NewPermissionEnforcer(actor *accesscontrol.Actor, organizationID string, repoSet client.RepoSet, db *sql.DB, config *rest.Config, log logr.Logger, featureSvc features.Service) *Enforcer {
	return &Enforcer{actor: actor, orgID: organizationID, repoSet: repoSet, db: db, hostConfig: config, logger: log, featureSvc: featureSvc}
}

func (e *Enforcer) listAppProjects(ctx context.Context, instanceID string) ([]argocdv1.AppProject, error) {
	tenant, err := argocdclient.NewArgoCDTenant(e.hostConfig, e.logger, instanceID)
	if err != nil {
		return nil, err
	}
	clientSet, err := tenant.ControlPlaneDynamicClientset(ctx)
	if err != nil {
		return nil, err
	}
	projs, err := clientSet.Resource(misc.AppprojectsGVR).Namespace(argoproj.K3sArgoCDNamespace).List(ctx, v1.ListOptions{})
	if err != nil {
		return nil, err
	}
	var projects []argocdv1.AppProject
	for _, obj := range projs.Items {
		data, err := json.Marshal(&obj)
		if err != nil {
			return nil, err
		}
		var project argocdv1.AppProject
		if err := json.Unmarshal(data, &project); err != nil {
			return nil, err
		}
		projects = append(projects, project)

	}
	return projects, nil
}

func matchGlob(pattern, val string) bool {
	if pattern == "*" {
		return true
	}
	if ok, err := filepath.Match(pattern, val); ok && err == nil {
		return true
	}
	return false
}

func (e *Enforcer) getArgoCDInstanceConfigs(ctx context.Context, instanceID string) ([]models.ArgoCDInstanceConfig, error) {
	var configs []models.ArgoCDInstanceConfig
	if err := models.NewQuery(
		qm.Select("cfg.instance_id, cfg.argocd_rbac_cm"),
		qm.From("argo_cd_instance_config cfg"),
		qm.InnerJoin("argo_cd_instance i ON i.id = cfg.instance_id"),
		lo.If(instanceID != "", qm.Where("cfg.instance_id = ?", instanceID)).Else(qm.Where("i.organization_owner = ?", e.orgID)),
	).Bind(ctx, e.db, &configs); err != nil {
		return nil, err
	}
	return configs, nil
}

func (e *Enforcer) getAllowedProjects(ctx context.Context, cfg models.ArgoCDInstanceConfig) (map[string]argocdv1.AppProject, error) {
	rbacCM, err := cfg.GetArgoCDRbacConfigMap()
	if err != nil {
		return nil, err
	}
	projects, err := e.listAppProjects(ctx, cfg.InstanceID)
	if err != nil {
		return nil, err
	}

	permissions, err := e.getActorPermissions(rbacCM, projects)
	if err != nil {
		return nil, err
	}

	allowedProjects := map[string]argocdv1.AppProject{}
	for _, perm := range permissions {
		// Assume that actor has access to the project if permission allows getting applications in the project
		if len(perm) < 5 || perm[1] != "applications" || perm[2] != "get" {
			continue
		}
		expr := strings.Split(perm[3], "/")
		if len(expr) < 2 {
			continue
		}
		for _, project := range projects {
			if matchGlob(expr[0], project.Name) {
				allowedProjects[project.Name] = project
			}
		}
	}

	return allowedProjects, nil
}

// getActorPermissions retrieves the permissions for the actor based on the RBAC configuration and projects
func (e *Enforcer) getActorPermissions(rbacCM *models.ArgoCDRbacConfigMap, projects []argocdv1.AppProject) ([][]string, error) {
	policies := append([]string{argocd.BuiltInPolicy, rbacCM.PolicyCSV}, lo.Map(rbacCM.OverlayPolicies, func(overlay models.OverlayPolicy, _ int) string {
		return overlay.Policy
	})...)
	groups, _ := e.actor.Extras["groups"].([]string)

	for _, project := range projects {
		for _, role := range project.Spec.Roles {
			if _, yes := lo.Find(groups, func(group string) bool {
				return lo.Contains(role.Groups, group)
			}); yes {
				policies = append(policies, role.Policies...)
			}
		}
	}

	policy := strings.Join(policies, "\n")

	enforcer, err := argocd.NewEnforcer(policy, argocd.CasbinModel)
	if err != nil {
		return nil, err
	}
	var permissions [][]string
	for _, claim := range append(groups, []string{fmt.Sprintf("%v", e.actor.Extras["username"]), rbacCM.DefaultPolicy}...) {
		if claim == "" {
			continue
		}
		if val, err := enforcer.GetImplicitPermissionsForUser(claim); err == nil {
			permissions = append(permissions, val...)
		}
	}
	return permissions, nil
}

func (s *Enforcer) getK8SResourceListFilter(ctx context.Context, instanceID string, argStartIdx int) (string, []any, error) {
	if !s.featureSvc.GetFeatureStatuses(ctx, &s.orgID).GetAkiPermissionModel().Enabled() {
		return "true", []any{}, nil // always true
	}

	// TODO: now only support ArgoCD, add support for other platforms
	platform := metadatautil.ExtractPlatform(ctx)
	if platform != metadata.PlatformArgoCD {
		return "true", []any{}, nil // always true
	}

	allowedDestinations, err := s.GetAllowedDestinations(ctx, instanceID)
	if err != nil {
		return "false", []any{}, err
	}

	args := []any{}
	destinationFilters := []string{}
	for _, dest := range allowedDestinations {
		filterConditions := []string{}

		// namespaceScopedFilter
		var namespaceScopedFilter []string
		var namespaceWhitelist []string
		for _, groupKind := range dest.PermissionSpec.NamespaceResourceWhitelist {
			idx := argStartIdx + len(args)
			gkFilter := fmt.Sprintf(`("group" LIKE $%d AND "kind" LIKE $%d AND "namespace" != '' AND "namespace" LIKE $%d)`, idx, idx+1, idx+2)
			args = append(args, strings.ReplaceAll(groupKind.Group, "*", "%"), strings.ReplaceAll(groupKind.Kind, "*", "%"), strings.ReplaceAll(dest.NamespacePattern, "*", "%"))
			namespaceWhitelist = append(namespaceWhitelist, gkFilter)
		}
		if len(namespaceWhitelist) > 0 {
			namespaceScopedFilter = append(namespaceScopedFilter, fmt.Sprintf("(%s)", strings.Join(namespaceWhitelist, " OR ")))
		}
		var namespaceBlacklist []string
		for _, groupKind := range dest.PermissionSpec.NamespaceResourceBlacklist {
			idx := argStartIdx + len(args)
			gkFilter := fmt.Sprintf(`("group" NOT LIKE $%d OR "kind" NOT LIKE $%d AND "namespace" != '' AND "namespace" LIKE $%d)`, idx, idx+1, idx+2)
			args = append(args, strings.ReplaceAll(groupKind.Group, "*", "%"), strings.ReplaceAll(groupKind.Kind, "*", "%"), strings.ReplaceAll(dest.NamespacePattern, "*", "%"))
			namespaceBlacklist = append(namespaceBlacklist, gkFilter)
		}
		if len(namespaceBlacklist) > 0 {
			namespaceScopedFilter = append(namespaceScopedFilter, fmt.Sprintf("(%s)", strings.Join(namespaceBlacklist, " AND ")))
		}
		if len(namespaceScopedFilter) == 0 {
			idx := argStartIdx + len(args)
			namespaceScopedFilter = append(namespaceScopedFilter, fmt.Sprintf(`("namespace" != '' AND "namespace" LIKE $%d)`, idx))
			args = append(args, strings.ReplaceAll(dest.NamespacePattern, "*", "%"))
		}
		filterConditions = append(filterConditions, fmt.Sprintf("(%s)", strings.Join(namespaceScopedFilter, " AND ")))

		// clusterScopedFilter
		var clusterScopedFilter []string
		var clusterWhitelist []string
		for _, groupKind := range dest.PermissionSpec.ClusterResourceWhitelist {
			idx := argStartIdx + len(args)
			gkFilter := fmt.Sprintf(`("group" LIKE $%d AND "kind" LIKE $%d AND "namespace" = '')`, idx, idx+1)
			args = append(args, strings.ReplaceAll(groupKind.Group, "*", "%"), strings.ReplaceAll(groupKind.Kind, "*", "%"))
			clusterWhitelist = append(clusterWhitelist, gkFilter)
		}
		if len(clusterWhitelist) > 0 {
			clusterScopedFilter = append(clusterScopedFilter, fmt.Sprintf("(%s)", strings.Join(clusterWhitelist, " OR ")))
		}
		var clusterBlacklist []string
		for _, groupKind := range dest.PermissionSpec.ClusterResourceBlacklist {
			idx := argStartIdx + len(args)
			gkFilter := fmt.Sprintf(`("group" NOT LIKE $%d OR "kind" NOT LIKE $%d AND "namespace" = '')`, idx, idx+1)
			args = append(args, strings.ReplaceAll(groupKind.Group, "*", "%"), strings.ReplaceAll(groupKind.Kind, "*", "%"))
			clusterBlacklist = append(clusterBlacklist, gkFilter)
		}
		if len(clusterBlacklist) > 0 {
			clusterScopedFilter = append(clusterScopedFilter, fmt.Sprintf("(%s)", strings.Join(clusterBlacklist, " AND ")))
		}
		if len(clusterScopedFilter) == 0 {
			clusterScopedFilter = append(clusterScopedFilter, `("namespace" = '')`)
		}
		filterConditions = append(filterConditions, fmt.Sprintf("(%s)", strings.Join(clusterScopedFilter, " AND ")))

		// For each destination, namespace and cluster filters are combined with OR
		idx := argStartIdx + len(args)
		destinationFilters = append(destinationFilters, fmt.Sprintf(`((%s) AND "cluster_id" = $%d)`, strings.Join(filterConditions, " OR "), idx))
		args = append(args, dest.ClusterID)
	}

	if len(destinationFilters) == 0 {
		return "false", []any{}, nil
	}
	return fmt.Sprintf("(%s)", strings.Join(destinationFilters, " OR ")), args, nil
}

func (e *Enforcer) GetAllowedDestinations(ctx context.Context, instanceID string) ([]Destination, error) {
	configs, err := e.getArgoCDInstanceConfigs(ctx, instanceID)
	if err != nil {
		return nil, err
	}

	akiPermissionEnabled := e.featureSvc.GetFeatureStatuses(ctx, &e.orgID).GetAkiPermissionModel().Enabled()
	allowedDestinations := []Destination{}
	for _, cfg := range configs {
		allowedProjects, err := e.getAllowedProjects(ctx, cfg)
		if err != nil {
			return nil, err
		}
		clusters, err := e.repoSet.ArgoCDClusters().Filter(
			models.ArgoCDClusterWhere.InstanceID.EQ(cfg.InstanceID)).ListAll(ctx, models.ArgoCDClusterColumns.ID, models.ArgoCDClusterColumns.Name)
		if err != nil {
			return nil, err
		}
		for _, proj := range allowedProjects {
			for _, dest := range proj.Spec.Destinations {
				for _, cluster := range clusters {
					if !akiPermissionEnabled {
						allowedDestinations = append(allowedDestinations, Destination{
							PermissionSpec:   proj.Spec,
							ClusterID:        cluster.ID,
							ClusterName:      cluster.Name,
							NamespacePattern: "*",
						})
						continue
					}
					if dest.Name != "" && matchGlob(dest.Name, cluster.Name) ||
						dest.Server != "" && matchGlob(dest.Server, fmt.Sprintf("http://cluster-%s:8001", cluster.Name)) {
						allowedDestinations = append(allowedDestinations, Destination{
							PermissionSpec:   proj.Spec,
							ClusterID:        cluster.ID,
							ClusterName:      cluster.Name,
							NamespacePattern: dest.Namespace,
						})
					}
				}
			}
		}
	}

	return lo.UniqBy(allowedDestinations, func(item Destination) string {
		return fmt.Sprintf("%s:%s", item.ClusterID, item.NamespacePattern)
	}), nil
}

func (e *Enforcer) GetK8SResourceListFilter(ctx context.Context, instanceID string) (qm.QueryMod, error) {
	sql, args, err := e.getK8SResourceListFilter(ctx, instanceID, 0)
	if err != nil {
		return nil, err
	}
	// Note this is a hack to replace the $1, $2, etc. with ?
	// However ? does not support with ordering, we need to be careful when
	// creating the sql query. The new args shall be always at the end of the
	// query.
	re := regexp.MustCompile(`\$\d+`)
	sql = re.ReplaceAllString(sql, "?")
	return qm.Where(sql, args...), nil
}

func (e *Enforcer) GetK8SResourceListFilterSQL(ctx context.Context, instanceID string, sqlArgs *[]any) (string, error) {
	sql, args, err := e.getK8SResourceListFilter(ctx, instanceID, len(*sqlArgs)+1)
	if err != nil {
		return "", err
	}
	*sqlArgs = append(*sqlArgs, args...)
	return sql, nil
}

func (e *Enforcer) GetActor() *accesscontrol.Actor {
	return e.actor
}
