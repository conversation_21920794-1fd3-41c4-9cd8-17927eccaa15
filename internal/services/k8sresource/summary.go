package k8sresource

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/lib/pq"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/models/models"

	_ "embed"
)

//go:embed summary.sql
var summarySQL string

type ResourceCounts struct {
	NodeCount          uint32 `boil:"node_count"`
	PodCount           uint32 `boil:"pod_count"`
	ContainerCount     uint32 `boil:"container_count"`
	StuckDeletionCount int    `boil:"stuck_deletion_count"`
	ImageCount         uint32 `boil:"image_count"`
	CVECount           uint32 `boil:"cve_count"`
	IncidentCount      uint32 `boil:"incident_count"`
}

// GetResourceCounts fetches the count of nodes, pods, containers,
// total objects and objects stuck in deletion in one SQL query
func (s *Service) GetSummaryCounts(ctx context.Context, instanceID string, clusterIDs []string) (*ResourceCounts, error) {
	actor := s.enforcer.GetActor()
	var counts ResourceCounts

	args := []any{s.organizationID, instanceID, pq.Array(clusterIDs), fmt.Sprintf("%s:%s", actor.Type, actor.ID)}
	akiPermissionFilter := ""
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &args)
		if err != nil {
			return nil, err
		}
		akiPermissionFilter = "AND " + filter
	}
	if err := models.NewQuery(qm.SQL(fmt.Sprintf(summarySQL, akiPermissionFilter), args...)).Bind(ctx, s.db, &counts); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &counts, nil
		}
		return nil, fmt.Errorf("failed to fetch resource counts: %w", err)
	}

	return &counts, nil
}
