WITH namespace_data AS (
  SELECT
    id,
    name,
    kind,
    namespace,
    cluster_id,
    instance_id,
    columns
  FROM
    argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    AND ($4::text[] IS NULL OR $4 = '{}' OR id = ANY($4))
    AND "group" = ''
    AND kind = 'Namespace'
    %[1]s
),

pod_data AS (
  SELECT
    id,
    name,
    kind,
    namespace,
    cluster_id,
    instance_id,
    columns
  FROM
    argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    AND "group" = ''
    AND kind = 'Pod'
    AND namespace = ANY(SELECT name FROM namespace_data)
    %[1]s
),

container_data AS (
  SELECT
    id,
    name,
    kind,
    namespace,
    cluster_id,
    instance_id,
    columns
  FROM
    argo_cd_cluster_k8s_object
  WHERE
    organization_id = $1
    AND ($2::text IS NULL OR $2 = '' OR instance_id = $2)
    AND ($3::text[] IS NULL OR $3 = '{}' OR cluster_id = ANY($3))
    AND "group" = 'dashboard.akuity.io'
    AND kind = 'Container'
    AND owner_id IN (SELECT id FROM pod_data)
    %[1]s
)

SELECT 
  id,
  name,
  namespace,
  kind,
  cluster_id,
  instance_id,
  columns
FROM namespace_data

UNION ALL

SELECT
  id,
  name,
  namespace,
  kind,
  cluster_id,
  instance_id,
  columns
FROM pod_data

UNION ALL

SELECT 
  id,
  name,
  namespace,
  kind,
  cluster_id,
  instance_id,
  columns
FROM container_data
