package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateArgocdInstancesQuota(
	ctx context.Context,
	req *organizationv1.UpdateArgocdInstancesQuotaRequest,
) (*organizationv1.UpdateArgocdInstancesQuotaResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId())); err != nil {
		return nil, err
	}

	instanceQuota := req.GetInstanceQuota()

	org, err := s.RepoSet.Organizations().GetByID(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	spec, err := org.GetSpec()
	if err != nil {
		return nil, err
	}

	for instanceID, maxApplications := range instanceQuota {
		instanceApplicationCount, err := instances.NewInstancesSource(s.Db, org.ID).GetInstanceApplicationCount(ctx, instanceID)
		if err != nil {
			return nil, err
		}
		if int(maxApplications) < instanceApplicationCount || (instanceApplicationCount < 0) {
			return nil, status.Errorf(codes.InvalidArgument,
				"maximum applications (%d) cannot be less than the current application count (%d)",
				maxApplications, instanceApplicationCount)
		}
	}
	spec.ArgoCDInstanceQuota = instanceQuota
	if err := org.SetSpec(*spec); err != nil {
		return nil, err
	}
	if err := s.RepoSet.Organizations().Update(ctx, org, models.OrganizationColumns.Spec); err != nil {
		return nil, err
	}

	return &organizationv1.UpdateArgocdInstancesQuotaResponse{}, nil
}
