package organization

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/boil"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListArgocdInstancesQuota(
	ctx context.Context,
	req *organizationv1.ListArgocdInstancesQuotaRequest,
) (*organizationv1.ListArgocdInstancesQuotaResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId())); err != nil {
		return nil, err
	}

	org, err := s.RepoSet.Organizations().GetByID(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	spec, err := org.GetSpec()
	if err != nil {
		return nil, err
	}

	existingSpec := spec.ArgoCDInstanceQuota
	instances := make(map[*models.ArgoCDInstance]int)

	for instanceID, maxApplications := range existingSpec {
		instance, err := s.RepoSet.ArgoCDInstances().GetByID(ctx, instanceID, models.ArgoCDInstanceColumns.ID, models.ArgoCDInstanceColumns.Name)
		if err != nil {
			return nil, err
		}
		instances[instance] = int(maxApplications)
	}

	instancesQuota, err := newArgocdInstancesQuotaV1(instances, s.Db, req.GetOrganizationId(), ctx)
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListArgocdInstancesQuotaResponse{
		Instances: instancesQuota,
	}, nil
}

func newArgocdInstancesQuotaV1(argocdInstances map[*models.ArgoCDInstance]int, db boil.ContextExecutor, orgID string, ctx context.Context) ([]*organizationv1.InstanceQuota, error) {
	var argocdQuota []*organizationv1.InstanceQuota
	for instance, quota := range argocdInstances {
		instanceApplicationCount, err := instances.NewInstancesSource(db, orgID).GetInstanceApplicationCount(ctx, instance.ID)
		if err != nil {
			return nil, err
		}

		argocdQuota = append(argocdQuota, &organizationv1.InstanceQuota{
			Instance: &organizationv1.InstanceQuotaSummary{
				Id:   instance.ID,
				Name: instance.Name,
			},
			MaxAppsCount:     int32(quota),
			CurrentAppsCount: int32(instanceApplicationCount),
		})
	}

	return argocdQuota, nil
}
