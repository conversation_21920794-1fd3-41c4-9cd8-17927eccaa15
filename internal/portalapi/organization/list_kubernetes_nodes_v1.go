package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesNodes(
	ctx context.Context,
	req *organizationv1.ListKubernetesNodesRequest,
) (*organizationv1.ListKubernetesNodesResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	nodes, fillerInfo, err := resSvc.ListKubernetesNodes(ctx, clusterInfo.GetClusterIDs(), req.GetGroupBy(), req.GetFiller())
	if err != nil {
		return nil, err
	}
	if fillerInfo == nil {
		fillerInfo = &k8sresource.FillerInfo{}
	}
	return &organizationv1.ListKubernetesNodesResponse{
		Nodes:         nodes,
		FillValueUnit: fillerInfo.Unit,
		MinFillValue:  fillerInfo.MinVal,
		MaxFillValue:  fillerInfo.MaxVal,
	}, nil
}
