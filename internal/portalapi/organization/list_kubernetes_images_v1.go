package organization

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesImages(
	ctx context.Context,
	req *organizationv1.ListKubernetesImagesRequest,
) (*organizationv1.ListKubernetesImagesResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	clusterIDs := clusterInfo.GetClusterIDs()

	images, err := resSvc.ListImages(ctx, req.GetInstanceId(), clusterIDs, req.GetOrderBy(), req.GetNameContains(), req.GetDigest(), offset, limit)
	if err != nil {
		return nil, err
	}
	count, err := resSvc.CountImages(ctx, req.GetInstanceId(), clusterIDs, req.GetNameContains(), req.GetDigest())
	if err != nil {
		return nil, fmt.Errorf("failed to count images: %w", err)
	}
	return &organizationv1.ListKubernetesImagesResponse{
		Images: mapImagesToRPCEntity(images),
		Count:  count,
	}, nil
}

func (s *OrganizationV1Server) ListKubernetesImagesToCSV(
	req *organizationv1.ListKubernetesImagesRequest,
	srv organizationv1.OrganizationService_ListKubernetesImagesToCSVServer,
) error {
	ctx := srv.Context()

	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return err
	}
	clusterIDs := clusterInfo.GetClusterIDs()

	offset := 0
	limit := 100
	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=images_%d.csv", time.Now().UTC().Unix()),
	})
	if err != nil {
		return err
	}

	body := &httpbody.HttpBody{
		ContentType: "text/csv",
		Data: []byte(strings.Join([]string{
			"Image Name",
			"Containers Count",
			"Image Tag",
			"Digest",
			"CVE Last Scan",
			"CVE Count",
			"CVEs",
		}, ",")),
		Extensions: []*anypb.Any{httpHeaders},
	}
	err = srv.Send(body)
	if err != nil {
		return err
	}

	count, err := resSvc.CountImages(ctx, req.GetInstanceId(), clusterIDs, req.GetNameContains(), req.GetDigest())
	if err != nil {
		return fmt.Errorf("failed to count images: %w", err)
	}
	for {
		images, err := resSvc.ListImages(ctx, req.GetInstanceId(), clusterIDs, req.GetOrderBy(), req.GetNameContains(), req.GetDigest(), offset, limit)
		if err != nil {
			return err
		}
		rpcEntities := mapImagesToRPCEntity(images)
		rows := lo.Map(rpcEntities, func(entity *organizationv1.Image, idx int) string {
			row := []string{
				entity.Name,
				fmt.Sprintf("%d", entity.ContainerCount),
				entity.Tag,
				entity.Digest,
			}

			if entity.CveScanResult != nil {
				row = append(row, entity.CveScanResult.CveLastScanTime.AsTime().Format(time.RFC3339))
				row = append(row, fmt.Sprintf("%d", entity.CveScanResult.CveCount))
				row = append(row, strings.Join(lo.Map(entity.CveScanResult.Cves, func(cve *organizationv1.ImageCVE, idx int) string {
					return cve.VulnerabilityId
				}), ";"))
			} else {
				row = append(row, "N/A", "N/A", "N/A")
			}
			return strings.Join(row, ",")
		})

		body := &httpbody.HttpBody{
			ContentType: "text/csv",
			Data:        []byte(strings.Join(rows, "\n")),
			Extensions:  []*anypb.Any{httpHeaders},
		}
		err = srv.Send(body)
		if err != nil {
			return err
		}

		offset += limit
		if int64(offset) >= int64(count) {
			break
		}
	}
	return nil
}

func mapImageToRPCEntity(image k8sresource.Image) *organizationv1.Image {
	cves := []map[string]string{}
	_ = json.Unmarshal([]byte(image.CVEs), &cves)
	imageCves := make([]*organizationv1.ImageCVE, 0, len(cves))
	for _, cve := range cves {
		imageCVE := &organizationv1.ImageCVE{
			VulnerabilityId:  cve["VulnerabilityID"],
			Title:            cve["Title"],
			Description:      cve["Description"],
			Severity:         cve["Severity"],
			PrimaryUrl:       cve["PrimaryURL"],
			Status:           cve["Status"],
			InstalledVersion: cve["InstalledVersion"],
			FixedVersion:     cve["FixedVersion"],
		}
		imageCves = append(imageCves, imageCVE)
	}
	sort.Slice(imageCves, func(i, j int) bool {
		severityOrder := map[string]int{
			"CRITICAL": 0,
			"HIGH":     1,
			"MEDIUM":   2,
			"LOW":      3,
			"NONE":     4,
		}
		severityA := severityOrder[strings.ToUpper(imageCves[i].Severity)]
		if severityA == 0 {
			severityA = math.MaxInt
		}
		severityB := severityOrder[strings.ToUpper(imageCves[j].Severity)]
		if severityB == 0 {
			severityB = math.MaxInt
		}
		if severityA == severityB {
			return imageCves[i].VulnerabilityId < imageCves[j].VulnerabilityId
		}
		return severityA > severityB
	})

	var cveScanResult *organizationv1.ImageCVEScanResult
	cveLastScanTime, _ := time.Parse(time.RFC3339, image.CVELastScanTime)
	if image.CVEs != "" && image.CVECount >= 0 {
		cveScanResult = &organizationv1.ImageCVEScanResult{
			Cves:            imageCves,
			CveCount:        uint32(image.CVECount),
			CveLastScanTime: timestamppb.New(cveLastScanTime),
		}
	}

	return &organizationv1.Image{
		Name:           image.Name,
		Tag:            image.Tag,
		ContainerCount: image.ContainerCount,
		Digest:         image.Digest,
		CveScanResult:  cveScanResult,
	}
}

func mapImagesToRPCEntity(images []k8sresource.Image) []*organizationv1.Image {
	res := make([]*organizationv1.Image, 0, len(images))
	for _, image := range images {
		res = append(res, mapImageToRPCEntity(image))
	}
	return res
}
