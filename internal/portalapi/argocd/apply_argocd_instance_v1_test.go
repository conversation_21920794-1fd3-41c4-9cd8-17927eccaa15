package argocd

import (
	"encoding/base64"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/types/known/structpb"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/akuitycli/argocd/declarative"
	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/encryption"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

var existingArgoCDConfigMapJSON = []byte(`
{
	"users.anonymous.enabled": "true",
	"kustomize.buildOptions": "--load_restrictor none",
	"admin.enabled": "false",
	"accounts.alice": "apiKey, login",
	"accounts.alice.enabled": "true",
	"accounts.bob": "login",
	"accounts.bob.enabled": "false",
	"accounts.chuck": "apiKey",
	"accounts.chuck.enabled": "true"
}`)

func init() {
	cryptKey, err := base64.StdEncoding.DecodeString("m8PTL8tiENXfaOPqop78ljrdaoloXn+w/HeTIWMUgO4=")
	if err != nil {
		panic(err)
	}
	if err := encryption.SetCryptKey(cryptKey); err != nil {
		panic(err)
	}
}

func Test_ConvertApplyRequest(t *testing.T) {
	applyReq, err := utils.ParseFiles(display.NewBufferedRenderer(display.OutputFormatWide), []string{"./testdata/"}, declarative.ConvertToApplyInstanceRequest)
	require.NoError(t, err)
	applyReq.OrganizationId = "orgabc123"
	instanceID := "instabc123"
	existing := &instances.ArgoCDInstance{
		ArgoCDInstanceConfig: models.ArgoCDInstanceConfig{
			ArgocdCM: null.JSON{
				JSON:  existingArgoCDConfigMapJSON,
				Valid: true,
			},
		},
	}
	existingClusters := []*models.ArgoCDCluster{
		{
			ID:                  "test-update",
			InstanceID:          instanceID,
			Name:                "test-cluster-update",
			Namespace:           "test",
			AutoUpgradeDisabled: false,
			NamespaceScoped:     false,
		},
		{
			ID:                  "test-delete",
			InstanceID:          instanceID,
			Name:                "test-cluster-delete",
			Namespace:           "test",
			AutoUpgradeDisabled: false,
			NamespaceScoped:     false,
		},
	}

	existingCMPs := []*argocdv1.ConfigManagementPlugin{
		{
			Name:    "kasane",
			Enabled: false,
			Image:   "test.io/kasaneapp/kasane",
			Spec: &argocdv1.PluginSpec{
				Version:          "v1.1",
				PreserveFileMode: true,
			},
		},
		{
			Name:    "test",
			Enabled: true,
			Image:   "gcr.io/kasaneapp/kasane",
			Spec: &argocdv1.PluginSpec{
				Version:          "v1.0",
				PreserveFileMode: true,
			},
		},
	}
	err = existing.SetArgocdSecret(map[string]string{
		"webhook.gitlab.secret": "to-be-deleted",
	})
	require.NoError(t, err)
	err = existing.SetArgoCDNotificationsSecret(map[string]string{
		"my-old-email-password": "to-be-deleted",
	})
	require.NoError(t, err)
	err = existing.SetArgoCDImageUpdaterSecret(map[string]string{
		"my-old-docker-credentials": "to-be-deleted",
	})
	require.NoError(t, err)
	err = existing.SetArgoCDAppsetSecret(map[string]string{
		"my-appset-secret":  "xyz4567",
		"my-appset-secret2": "xyz4567",
	})
	require.NoError(t, err)

	var cpu, memory resource.Quantity
	cpuStr := "500m"
	memoryStr := "1Gi"
	cpu, err = resource.ParseQuantity(cpuStr)
	require.NoError(t, err)
	memory, err = resource.ParseQuantity(memoryStr)
	require.NoError(t, err)
	asc := common.AutoScalerConfig{
		ApplicationController: &common.AppControllerAutoScalingConfig{
			ResourceMinimum: common.WorkloadResourceValues{
				Memory: memory,
				CPU:    cpu,
			},
		},
	}
	reqs, err := convertApplyRequest(applyReq, instanceID, existing, existingClusters, existingCMPs, map[argocdv1.PruneResourceType]bool{argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_CONFIG_MANAGEMENT_PLUGINS: true}, asc)
	require.NoError(t, err)

	assert.NotNil(t, reqs.patchInstanceRequest)
	assert.Equal(t, applyReq.OrganizationId, reqs.patchInstanceRequest.OrganizationId)
	assert.Equal(t, instanceID, reqs.patchInstanceRequest.Id)
	patchMap := reqs.patchInstanceRequest.Patch.AsMap()
	assert.Contains(t, patchMap, "config")
	assert.Contains(t, patchMap, "spec")
	assert.Contains(t, patchMap, "rbacConfig")
	assert.Equal(t, patchMap["name"], "test-inst")
	assert.Equal(t, patchMap["version"], "v2.6.0")
	assert.Equal(t, patchMap["description"], "test-inst")
	assert.Len(t, patchMap, 6)
	assert.Equal(t, "500m", reqs.createInstanceClusterRequests[0].Data.AutoscalerConfig.ApplicationController.ResourceMinimum.Cpu)
	assert.Equal(t, "1Gi", reqs.createInstanceClusterRequests[0].Data.AutoscalerConfig.ApplicationController.ResourceMinimum.Mem)

	assert.NotNil(t, reqs.patchInstanceSecretRequest)
	assert.Equal(t, applyReq.OrganizationId, reqs.patchInstanceSecretRequest.OrganizationId)
	assert.Equal(t, instanceID, reqs.patchInstanceSecretRequest.Id)
	assert.Equal(t, "shhhh! it's a github secret", *reqs.patchInstanceSecretRequest.Secret["webhook.github.secret"].Value)
	assert.Equal(t, "my-github-oidc-secret", *reqs.patchInstanceSecretRequest.Secret["dex.github.clientSecret"].Value)
	assert.Nil(t, reqs.patchInstanceSecretRequest.Secret["webhook.gitlab.secret"].Value)
	assert.Len(t, reqs.patchInstanceSecretRequest.Secret, 3)

	assert.NotNil(t, reqs.updateInstanceNotificationConfigRequest)
	assert.Equal(t, applyReq.OrganizationId, reqs.updateInstanceNotificationConfigRequest.OrganizationId)
	assert.Equal(t, instanceID, reqs.updateInstanceNotificationConfigRequest.Id)
	expectedConfig := map[string]string{
		"defaultTriggers":                "- on-sync-status-unknown\n",
		"template.my-custom-template":    "message: |\n  Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.\n",
		"trigger.on-sync-status-unknown": "- when: app.status.sync.status == 'Unknown'\n  send: [my-custom-template]\n",
	}
	assert.Equal(t, expectedConfig, reqs.updateInstanceNotificationConfigRequest.Config)

	assert.NotNil(t, reqs.patchInstanceNotificationSecretRequest)
	assert.Equal(t, applyReq.OrganizationId, reqs.patchInstanceNotificationSecretRequest.OrganizationId)
	assert.Equal(t, instanceID, reqs.patchInstanceNotificationSecretRequest.Id)
	assert.Equal(t, "<EMAIL>", *reqs.patchInstanceNotificationSecretRequest.Secret["email-username"].Value)
	assert.Equal(t, "password", *reqs.patchInstanceNotificationSecretRequest.Secret["email-password"].Value)
	assert.Nil(t, reqs.patchInstanceNotificationSecretRequest.Secret["my-old-email-password"].Value)
	assert.Len(t, reqs.patchInstanceNotificationSecretRequest.Secret, 3)

	assert.Len(t, reqs.upsertInstanceAccountRequests, 2)
	for _, req := range reqs.upsertInstanceAccountRequests {
		assert.Equal(t, applyReq.OrganizationId, req.OrganizationId)
		assert.Equal(t, instanceID, req.InstanceId)
		switch req.Name {
		case "alice":
			disabled := req.Disabled != nil && *req.Disabled
			assert.True(t, req.Capabilities.ApiKey)
			assert.True(t, req.Capabilities.Login)
			assert.False(t, disabled)
		case "bob":
			disabled := req.Disabled != nil && *req.Disabled
			assert.False(t, req.Capabilities.ApiKey)
			assert.True(t, req.Capabilities.Login)
			assert.True(t, disabled)
		default:
			t.Fail()
		}
	}

	assert.Len(t, reqs.deleteInstanceAccountRequests, 1)
	assert.Equal(t, applyReq.OrganizationId, reqs.deleteInstanceAccountRequests[0].OrganizationId)
	assert.Equal(t, instanceID, reqs.deleteInstanceAccountRequests[0].InstanceId)
	assert.Equal(t, "chuck", reqs.deleteInstanceAccountRequests[0].Name)

	assert.NotNil(t, reqs.updateInstanceImageUpdaterConfigRequest)
	expectedConfig = map[string]string{
		"git.email": "<EMAIL>",
		"git.user":  "akuitybot",
		"registries.conf": `registries:
  - prefix: docker.io
    name: Docker
    api_url: https://registry-1.docker.io
    credentials: secret:argocd/argocd-image-updater-secret#my-docker-credentials`,
	}
	assert.Equal(t, expectedConfig, reqs.updateInstanceImageUpdaterConfigRequest.Config)
	assert.NotNil(t, reqs.updateInstanceImageUpdaterSSHConfigRequest)
	expectedConfig = map[string]string{
		"config": `Host *
      PubkeyAcceptedAlgorithms +ssh-rsa
      HostkeyAlgorithms +ssh-rsa`,
	}
	assert.Equal(t, expectedConfig, reqs.updateInstanceImageUpdaterSSHConfigRequest.Config)
	assert.NotNil(t, reqs.patchInstanceImageUpdaterSecretRequest)
	assert.Len(t, reqs.patchInstanceImageUpdaterSecretRequest.Secret, 2)
	assert.Equal(t, "abcd1234", *reqs.patchInstanceImageUpdaterSecretRequest.Secret["my-docker-credentials"].Value)
	assert.Nil(t, reqs.patchInstanceImageUpdaterSecretRequest.Secret["my-old-docker-credentials"].Value)

	assert.NotNil(t, reqs.updateInstanceResourceCustomizationsRequest)
	assert.Len(t, reqs.updateInstanceResourceCustomizationsRequest.Resources, 3)
	assert.Equal(t, "certmanager.k8s.io", reqs.updateInstanceResourceCustomizationsRequest.Resources[0].Group)
	assert.Equal(t, "Certificate", reqs.updateInstanceResourceCustomizationsRequest.Resources[0].Kind)
	assert.NotEmpty(t, reqs.updateInstanceResourceCustomizationsRequest.Resources[0].Health)

	assert.Len(t, reqs.createInstanceClusterRequests, 2)
	assert.Equal(t, "test-cluster-create", reqs.createInstanceClusterRequests[0].Name)
	assert.Equal(t, "kargo-test-integration", reqs.createInstanceClusterRequests[1].Name)
	assert.Len(t, reqs.updateInstanceClusterRequests, 1)
	assert.Equal(t, "test-update", reqs.updateInstanceClusterRequests[0].Id)
	assert.Len(t, reqs.deleteInstanceClusterRequests, 1)
	assert.Equal(t, "test-delete", reqs.deleteInstanceClusterRequests[0].Id)

	assert.NotNil(t, reqs.patchInstanceAppSetSecretRequest)
	assert.Equal(t, applyReq.OrganizationId, reqs.patchInstanceAppSetSecretRequest.OrganizationId)
	assert.Equal(t, instanceID, reqs.patchInstanceAppSetSecretRequest.Id)
	assert.Equal(t, "xyz456", *reqs.patchInstanceAppSetSecretRequest.Secret["my-appset-secret"].Value)
	assert.Nil(t, reqs.patchInstanceAppSetSecretRequest.Secret["my-appset-secret2"].Value)
	assert.Len(t, reqs.patchInstanceAppSetSecretRequest.Secret, 2)

	assert.NotNil(t, reqs.updateConfigManagementPluginsRequest)
	assert.Len(t, reqs.updateConfigManagementPluginsRequest.Plugins, 2)
	assert.Equal(t, "kasane", reqs.updateConfigManagementPluginsRequest.Plugins[0].Name)
	assert.Equal(t, "gcr.io/kasaneapp/kasane", reqs.updateConfigManagementPluginsRequest.Plugins[0].Image)
	assert.False(t, reqs.updateConfigManagementPluginsRequest.Plugins[0].Enabled)
	assert.Equal(t, "v1.0", reqs.updateConfigManagementPluginsRequest.Plugins[0].Spec.Version)
	assert.Len(t, reqs.updateConfigManagementPluginsRequest.Plugins[0].Spec.Init.Command, 2)
	assert.Len(t, reqs.updateConfigManagementPluginsRequest.Plugins[0].Spec.Generate.Command, 2)
	assert.Equal(t, "helm-kustomize", reqs.updateConfigManagementPluginsRequest.Plugins[1].Name)
}

func Test_ConvertApplyK3sResources(t *testing.T) {
	applyReq, err := utils.ParseFiles(display.NewBufferedRenderer(display.OutputFormatWide), []string{"./testdata/"}, declarative.ConvertToApplyInstanceRequest)
	require.NoError(t, err)
	objs, err := convertApplyK3sResources(applyReq)
	require.NoError(t, err)

	assert.Len(t, objs, 8)
	objByName := map[string]*unstructured.Unstructured{}
	for _, obj := range objs {
		objByName[obj.GetName()] = obj
	}
	knownHostsConfigMap, ok := objByName[argoproj.ArgoCDKnownHostsConfigMapName]
	require.True(t, ok)
	assert.Equal(t, "ConfigMap", knownHostsConfigMap.GetKind())
	assert.Contains(t, knownHostsConfigMap.Object, "data")
	assert.Contains(t, knownHostsConfigMap.Object["data"], "ssh_known_hosts")
	tlsCertsConfigMap, ok := objByName[argoproj.ArgoCDTLSCertsConfigMapName]
	require.True(t, ok)
	assert.Equal(t, "ConfigMap", tlsCertsConfigMap.GetKind())
	assert.Contains(t, tlsCertsConfigMap.Object, "data")
	assert.Contains(t, tlsCertsConfigMap.Object["data"], "server.example.com")
	httpsCreds, ok := objByName["repo-argoproj-https-creds"]
	require.True(t, ok)
	assert.Equal(t, "Secret", httpsCreds.GetKind())
	assert.Equal(t, argoproj.LabelValueSecretTypeRepoCreds, httpsCreds.GetLabels()[argoproj.LabelKeySecretType])
	assert.Contains(t, httpsCreds.Object, "stringData")
	assert.Contains(t, httpsCreds.Object["stringData"], "username")
	privateRepo, ok := objByName["repo-my-private-https-repo"]
	require.True(t, ok)
	assert.Equal(t, "Secret", privateRepo.GetKind())
	assert.Equal(t, argoproj.LabelValueSecretTypeRepository, privateRepo.GetLabels()[argoproj.LabelKeySecretType])
	assert.Contains(t, privateRepo.Object, "stringData")
	assert.Contains(t, privateRepo.Object["stringData"], "url")

	app, ok := objByName["helm-guestbook"]
	require.True(t, ok)
	assert.Equal(t, "argoproj.io/v1alpha1", app.GetAPIVersion())
	assert.Equal(t, "Application", app.GetKind())
	assert.Equal(t, "default", app.Object["spec"].(map[string]interface{})["project"])

	appset, ok := objByName["guestbook"]
	require.True(t, ok)
	assert.Equal(t, "argoproj.io/v1alpha1", appset.GetAPIVersion())
	assert.Equal(t, "ApplicationSet", appset.GetKind())
	assert.Equal(t, "default", appset.Object["spec"].(map[string]interface{})["template"].(map[string]interface{})["spec"].(map[string]interface{})["project"])

	appProject, ok := objByName["my-project"]
	require.True(t, ok)
	assert.Equal(t, "argoproj.io/v1alpha1", appProject.GetAPIVersion())
	assert.Equal(t, "AppProject", appProject.GetKind())
	assert.Equal(t, "Example Project", appProject.Object["spec"].(map[string]interface{})["description"])
}

func TestValidateArgoCDConfigMap(t *testing.T) {
	tests := []struct {
		name        string
		configMap   map[string]interface{}
		configName  string
		obj         extObject
		expectError string
	}{
		{
			name: "valid argocd-cm fields",
			configMap: map[string]interface{}{
				"data": map[string]interface{}{
					"admin.enabled":                  "false",
					"accounts.test-user.enabled":     "true",
					"exec.enabled":                   "true",
					"ga.anonymizeusers":              "false",
					"helm.enabled":                   "true",
					"kustomize.enabled":              "true",
					"server.rbac.log.enforce.enable": "false",
					"statusbadge.enabled":            "false",
					"ui.bannerpermanent":             "false",
					"users.anonymous.enabled":        "true",
					"accounts.alice":                 "login",
					"accounts.random":                "apiKey",
					"application.instanceLabelKey":   "argocd.argoproj.io/instance",
					"project.links":                  `[{"title": "ArgoCD", "url": "https://argocd.example.com", "description": "ArgoCD Instance", "icon.class": "fa-link"}]`,
					"invalid1":                       "true",
				},
			},
			configName:  "argocd-cm",
			obj:         &models.ArgoCDConfigMap{},
			expectError: "unsupported field in argocd-cm: invalid1",
		},
		{
			name: "valid argocd-rbac-cm fields",
			configMap: map[string]interface{}{
				"data": map[string]interface{}{
					"policy.default": "role:readonly",
					"policy.csv": `
         p, role:org-admin, applications, *, */*, allow
         p, role:org-admin, clusters, get, *, allow
         g, your-github-org:your-team, role:org-admin
`,
					"invalid2": "true",
				},
			},
			configName:  "argocd-rbac-cm",
			obj:         &models.ArgoCDRbacConfigMap{},
			expectError: "unsupported field in argocd-rbac-cm: invalid2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configStruct, err := structpb.NewStruct(tt.configMap)
			require.NoError(t, err)

			err = validateArgoCDConfigMap(configStruct, tt.configName, tt.obj)
			if tt.expectError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectError)
				return
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
