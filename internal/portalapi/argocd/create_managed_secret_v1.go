package argocd

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const (
	argocdManagedSecretTypeLabel = "argocd.argoproj.io/secret-type"
)

var errorNotManagedSecret = errors.New("managed secret not found")

// managedSecretTypeToLabel converts ManagedSecretType enum to the corresponding label value
func managedSecretTypeToLabel(secretType argocdv1.ManagedSecretType) (string, error) {
	switch secretType {
	case argocdv1.ManagedSecretType_MANAGED_SECRET_TYPE_REPO_CREDS:
		return "repo-creds", nil
	case argocdv1.ManagedSecretType_MANAGED_SECRET_TYPE_REPOSITORY:
		return "repository", nil
	case argocdv1.ManagedSecretType_MANAGED_SECRET_TYPE_UNSPECIFIED:
		return "repo-creds", nil // Default to repo-creds for unspecified type
	default:
		return "", fmt.Errorf("unsupported managed secret type: %v", secretType)
	}
}

func prepManagedSecretLabels(
	labels map[string]string,
	secretTypeLabel string,
) map[string]string {
	if labels == nil {
		labels = make(map[string]string)
	}
	labels[argocdManagedSecretTypeLabel] = secretTypeLabel
	labels[client.ManagedSecretLabel] = "true"
	return labels
}

func prepManagedSecretAnnotations(allowedClusters []string, clusterSelector string) map[string]string {
	annotations := make(map[string]string)
	if len(allowedClusters) > 0 {
		annotations[client.ManagedSecretSyncAllowedClustersAnnotation] = strings.Join(allowedClusters, ",")
	}
	if len(clusterSelector) > 0 {
		annotations[client.ManagedSecretClusterSelectorAnnotation] = clusterSelector
	}
	return annotations
}

// ensureSecretIsManaged checks if the secret is a managed secret and returns it if so.
func ensureSecretIsManaged(ctx context.Context, kubeClient kubernetes.Interface, name string) (*corev1.Secret, error) {
	secret, err := kubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get managed secret %q: %w", name, err)
	}

	// If it doesn't have any labels, it isn't a managed secret
	if secret.Labels == nil {
		return nil, errorNotManagedSecret
	}
	// Check if the secret is a managed secret by looking for the managed secret label or the cluster ownership label
	if secret.Labels[client.ManagedSecretLabel] != "true" && secret.Labels[client.LabelKeySecretOwnedByCluster] == "" {
		return nil, errorNotManagedSecret
	}
	return secret, nil
}

func toK8sLabelSelector(selector *argocdv1.ObjectSelector) (string, error) {
	if selector == nil {
		return "", nil
	}
	if selector.MatchLabels == nil && selector.MatchExpressions == nil {
		return "", nil
	}

	matchExpressions := make([]metav1.LabelSelectorRequirement, 0, len(selector.MatchExpressions))
	for _, expr := range selector.MatchExpressions {
		// We just convert over for now and then do a round trip to validate the selector
		matchExpressions = append(matchExpressions, metav1.LabelSelectorRequirement{
			Key:      *expr.Key,
			Operator: metav1.LabelSelectorOperator(*expr.Operator),
			Values:   expr.Values,
		})
	}

	labelSelector := metav1.LabelSelector{
		MatchLabels:      selector.MatchLabels,
		MatchExpressions: matchExpressions,
	}
	// Validate by turning into a selector
	validated, err := metav1.LabelSelectorAsSelector(&labelSelector)
	if err != nil {
		return "", fmt.Errorf("invalid label selector: %w", err)
	}
	return validated.String(), nil
}

func (s *ArgoCDV1Server) CreateManagedSecret(
	ctx context.Context,
	req *argocdv1.CreateManagedSecretRequest,
) (*argocdv1.CreateManagedSecretResponse, error) {
	k3sKubeClient, err := s.getKubeClientForInstance(ctx, req.GetOrganizationId(), req.WorkspaceId, req.GetInstanceId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetInstanceId())
		}
		return nil, err
	}

	// Convert the managed secret type to label value
	secretTypeLabel, err := managedSecretTypeToLabel(req.ManagedSecret.Type)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid secret type: %v", err)
	}

	labels := prepManagedSecretLabels(
		req.ManagedSecret.Labels,
		secretTypeLabel,
	)
	clusterSelector, err := toK8sLabelSelector(req.ManagedSecret.ClusterSelector)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid cluster selector: %v", err)
	}

	annotations := prepManagedSecretAnnotations(
		req.ManagedSecret.AllowedClusters,
		clusterSelector,
	)

	// Create the Kubernetes secret
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:        req.ManagedSecret.Name,
			Labels:      labels,
			Annotations: annotations,
		},
		StringData: req.ManagedSecretData,
	}

	_, err = k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		// NOTE: This will allow a user to check if a non-managed secret exists by trying to create
		// it. That is probably fine because for a good user experience, we'd want to tell them what
		// the reserved names are anyway. We can change this down the line if we're worried
		if k8serrors.IsAlreadyExists(err) {
			return nil, status.Errorf(codes.AlreadyExists, "managed secret %q already exists", req.ManagedSecret.Name)
		}
		return nil, status.Errorf(codes.Internal, "failed to create managed secret %q: %v", req.ManagedSecret.Name, err)
	}

	return &argocdv1.CreateManagedSecretResponse{}, nil
}
