package argocd

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/api/akuity/v1alpha1"
	argocdv1alpha1 "github.com/akuityio/akuity-platform/api/argocd/v1alpha1"
	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

var k3sResourceTypes = map[argocdv1.PruneResourceType]bool{
	argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APP_PROJECTS:            true,
	argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APPLICATIONS:            true,
	argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APPLICATION_SETS:        true,
	argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_REPO_CREDENTIAL_SECRETS: true,
}

// convertedApplyRequest is an apply request converted to a series of other API requests
type convertedApplyRequest struct {
	patchInstanceRequest                        *argocdv1.PatchInstanceRequest
	patchInstanceSecretRequest                  *argocdv1.PatchInstanceSecretRequest
	updateInstanceNotificationConfigRequest     *argocdv1.UpdateInstanceNotificationConfigRequest
	patchInstanceNotificationSecretRequest      *argocdv1.PatchInstanceNotificationSecretRequest
	upsertInstanceAccountRequests               []*argocdv1.UpsertInstanceAccountRequest
	deleteInstanceAccountRequests               []*argocdv1.DeleteInstanceAccountRequest
	updateInstanceImageUpdaterConfigRequest     *argocdv1.UpdateInstanceImageUpdaterConfigRequest
	updateInstanceImageUpdaterSSHConfigRequest  *argocdv1.UpdateInstanceImageUpdaterSSHConfigRequest
	patchInstanceImageUpdaterSecretRequest      *argocdv1.PatchInstanceImageUpdaterSecretRequest
	updateInstanceResourceCustomizationsRequest *argocdv1.UpdateInstanceResourceCustomizationsRequest
	createInstanceClusterRequests               []*argocdv1.CreateInstanceClusterRequest
	updateInstanceClusterRequests               []*argocdv1.UpdateInstanceClusterRequest
	deleteInstanceClusterRequests               []*argocdv1.DeleteInstanceClusterRequest
	updateConfigManagementPluginsRequest        *argocdv1.UpdateInstanceConfigManagementPluginsRequest
	patchInstanceAppSetSecretRequest            *argocdv1.PatchInstanceAppsetSecretRequest
}

func (s *ArgoCDV1Server) ApplyInstance(
	ctx context.Context,
	req *argocdv1.ApplyInstanceRequest,
) (*argocdv1.ApplyInstanceResponse, error) {
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	var instanceID string
	var existing *instances.ArgoCDInstance
	pruneResourceTypes := map[argocdv1.PruneResourceType]bool{}
	pruneK3s := false
	for _, t := range req.PruneResourceTypes {
		pruneResourceTypes[t] = true
		pruneK3s = pruneK3s || k3sResourceTypes[t]
	}

	// 1. create the instance or fetch the existing one
	var err error
	if req.IdType == idv1.Type_NAME {
		existing, err = instanceSvc.GetInstanceByName(ctx, req.Id)
	} else {
		existing, err = instanceSvc.GetInstanceByID(ctx, req.Id)
	}
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			if req.IdType != idv1.Type_NAME {
				return nil, status.Error(codes.InvalidArgument, "id.type must be 'name' when creating instances")
			}
			if req.Argocd == nil {
				return nil, status.Error(codes.InvalidArgument, "ArgoCD must be specified when creating instances")
			}
			workspaceID := ""
			orgID := req.GetOrganizationId()
			if s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
				teamSvc := teams.NewService(s.db)
				workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)
				if req.GetWorkspaceId() == "" {
					// if the workspace id is not provided, for backward compatibility, we will use the default workspace
					defaultWorkspace, err := workspaceSvc.GetDefaultWorkspace(ctx, req.GetOrganizationId())
					if err != nil {
						return nil, errorsutil.NewAPIStatus(http.StatusForbidden, err.Error())
					}
					workspaceID = defaultWorkspace.ID
				} else {
					workspace, err := workspaceSvc.GetWorkspace(ctx, req.GetWorkspaceId())
					if err != nil {
						return nil, err
					}
					workspaceID = workspace.ID
				}
			}
			req.WorkspaceId = workspaceID
			if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
				accesscontrol.NewActionCreateWorkspaceInstances(req.GetWorkspaceId())); err != nil {
				return nil, err
			}
			instanceID, err = s.createInstance(ctx, req)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	} else {
		instanceID = existing.ID
		if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionUpdateWorkspaceInstances(existing.WorkspaceID.String, instanceID)); err != nil {
			return nil, err
		}
	}
	var existingClusters []*models.ArgoCDCluster
	existingClusters, err = instanceSvc.GetInstanceClusters(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	var existingCMPs []*argocdv1.ConfigManagementPlugin
	getCMPsResp, err := s.GetInstanceConfigManagementPlugins(ctx, &argocdv1.GetInstanceConfigManagementPluginsRequest{
		OrganizationId: req.OrganizationId,
		Id:             instanceID,
	})
	if err != nil {
		return nil, err
	}
	existingCMPs = getCMPsResp.Plugins
	reqs, err := convertApplyRequest(req, instanceID, existing, existingClusters, existingCMPs, pruneResourceTypes, s.clusterAutoscalerConfig)
	if err != nil {
		return nil, err
	}

	// 2. update any configuration in the InstanceV1 model (spec, config, rbacConfig)
	if reqs.patchInstanceRequest != nil {
		if _, err := s.PatchInstance(ctx, reqs.patchInstanceRequest); err != nil {
			return nil, err
		}
	}
	// 3. update argocd-secret
	if reqs.patchInstanceSecretRequest != nil {
		if _, err := s.PatchInstanceSecret(ctx, reqs.patchInstanceSecretRequest); err != nil {
			return nil, err
		}
	}
	// 4. update local accounts
	for _, req := range reqs.upsertInstanceAccountRequests {
		if _, err := s.UpsertInstanceAccount(ctx, req); err != nil {
			return nil, err
		}
	}
	for _, req := range reqs.deleteInstanceAccountRequests {
		if _, err := s.DeleteInstanceAccount(ctx, req); err != nil {
			return nil, err
		}
	}

	// 5. update notifications config and secret
	if reqs.updateInstanceNotificationConfigRequest != nil {
		if _, err := s.UpdateInstanceNotificationConfig(ctx, reqs.updateInstanceNotificationConfigRequest); err != nil {
			return nil, err
		}
	}
	if reqs.patchInstanceNotificationSecretRequest != nil {
		if _, err := s.PatchInstanceNotificationSecret(ctx, reqs.patchInstanceNotificationSecretRequest); err != nil {
			return nil, err
		}
	}

	// 6. update image updater config, ssh-config, and secret
	if reqs.updateInstanceImageUpdaterConfigRequest != nil {
		if _, err := s.UpdateInstanceImageUpdaterConfig(ctx, reqs.updateInstanceImageUpdaterConfigRequest); err != nil {
			return nil, err
		}
	}
	if reqs.updateInstanceImageUpdaterSSHConfigRequest != nil {
		if _, err := s.UpdateInstanceImageUpdaterSSHConfig(ctx, reqs.updateInstanceImageUpdaterSSHConfigRequest); err != nil {
			return nil, err
		}
	}
	if reqs.patchInstanceImageUpdaterSecretRequest != nil {
		if _, err := s.PatchInstanceImageUpdaterSecret(ctx, reqs.patchInstanceImageUpdaterSecretRequest); err != nil {
			return nil, err
		}
	}
	if reqs.updateInstanceResourceCustomizationsRequest != nil {
		if _, err := s.UpdateInstanceResourceCustomizations(ctx, reqs.updateInstanceResourceCustomizationsRequest); err != nil {
			return nil, err
		}
	}

	// 7. update application set secret
	if reqs.patchInstanceAppSetSecretRequest != nil {
		if _, err := s.PatchInstanceAppsetSecret(ctx, reqs.patchInstanceAppSetSecretRequest); err != nil {
			return nil, err
		}
	}

	// 8. create, update, or delete clusters
	for _, req := range reqs.createInstanceClusterRequests {
		if _, err := s.CreateInstanceCluster(ctx, req); err != nil {
			return nil, err
		}
	}
	for _, req := range reqs.updateInstanceClusterRequests {
		if _, err := s.UpdateInstanceCluster(ctx, req); err != nil {
			return nil, err
		}
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_CLUSTERS] {
		for _, req := range reqs.deleteInstanceClusterRequests {
			if _, err := s.DeleteInstanceCluster(ctx, req); err != nil {
				return nil, err
			}
		}
	}

	// 9. update config management plugins
	if reqs.updateConfigManagementPluginsRequest != nil {
		if _, err := s.UpdateInstanceConfigManagementPlugins(ctx, reqs.updateConfigManagementPluginsRequest); err != nil {
			return nil, err
		}
	}

	// 10. create, update or delete extra argocd configurations, e.g., argocd-ssh-known-hosts-cm, argocd-tls-certs-cm, repo credentials and repo template credentials.
	// As well as Applications, ApplicationSets, and AppProjects.
	objs, err := convertApplyK3sResources(req)
	if err != nil {
		return nil, err
	}
	// only attempt to apply k3s resources apply if input has any k3s resources or requires to prune k3s resources
	if len(objs) > 0 || pruneK3s {
		if err := s.applyK3sResources(ctx, instanceSvc, instanceID, existing, objs, pruneResourceTypes); err != nil {
			if k8serrors.IsInvalid(err) {
				return nil, status.Errorf(codes.InvalidArgument, "failed to apply manifests: %s", err.Error())
			}
			return nil, err
		}
	}

	return &argocdv1.ApplyInstanceResponse{}, nil
}

func (s *ArgoCDV1Server) applyK3sResources(ctx context.Context, instanceSvc *instances.Service, instanceID string, existing *instances.ArgoCDInstance, objs []*unstructured.Unstructured, pruneResourceTypes map[argocdv1.PruneResourceType]bool) error {
	return wait.PollUntilContextTimeout(ctx, 2*time.Second, 120*time.Second, true, func(ctx context.Context) (bool, error) {
		b, err := s.applyK3sResourcesInternal(ctx, instanceSvc, instanceID, existing, objs, pruneResourceTypes)
		if err != nil {
			if errorsutil.IsInstanceReadinessError(err) {
				return false, nil
			}
			return false, err
		}
		return b, nil
	})
}

func (s *ArgoCDV1Server) applyK3sResourcesInternal(ctx context.Context, instanceSvc *instances.Service, instanceID string, existing *instances.ArgoCDInstance, objs []*unstructured.Unstructured, pruneResourceTypes map[argocdv1.PruneResourceType]bool) (bool, error) {
	instance := existing
	if existing == nil {
		var err error
		instance, err = instanceSvc.GetInstanceByID(ctx, instanceID)
		if err != nil {
			return false, err
		}
	}
	tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, instanceID)
	if err != nil {
		return false, err
	}

	var kubectl *kube.Kubectl
	var k3sKubeClient kubernetes.Interface
	var k3sDynamicClient dynamic.Interface

	if instance.Shard == "" {
		kubectl, err = tnt.ControlPlaneKubectl(ctx)
		if err != nil {
			// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
			if k8serrors.IsNotFound(err) {
				return false, nil
			}
			// We don't return the error directly since it may contain AKP implementation details.
			return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
		}
		k3sKubeClient, err = tnt.ControlPlaneKubeClientset(ctx)
		if err != nil {
			// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
			if k8serrors.IsNotFound(err) {
				return false, nil
			}
			// We don't return the error directly since it may contain AKP implementation details.
			return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
		}
		k3sDynamicClient, err = tnt.ControlPlaneDynamicClientset(ctx)
		if err != nil {
			// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
			if k8serrors.IsNotFound(err) {
				return false, nil
			}
			// We don't return the error directly since it may contain AKP implementation details.
			return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
		}
	} else {
		privateSpec, err := instance.GetPrivateSpec()
		if err != nil {
			return false, err
		}
		if privateSpec.Kubeconfig == nil {
			// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
			return false, nil
		}

		restConfig, err := privateSpec.GetRestConfig()
		if err != nil {
			return false, err
		}
		k3sKubeClient, err = kubernetes.NewForConfig(restConfig)
		if err != nil {
			return false, err
		}
		kubectl, err = kube.NewKubectl(restConfig)
		if err != nil {
			return false, err
		}
		k3sDynamicClient, err = dynamic.NewForConfig(restConfig)
		if err != nil {
			return false, err
		}
	}

	// Get the argocd-ssh-known-host-cm and argocd-tls-certs-cm Configmaps to make sure these 2 configmaps with default values are created from agent.
	hostsCM, err := k3sKubeClient.CoreV1().ConfigMaps(argoproj.K3sArgoCDNamespace).Get(ctx, argoproj.ArgoCDKnownHostsConfigMapName, metav1.GetOptions{})
	if err != nil {
		return false, nil
	}
	certsCM, err := k3sKubeClient.CoreV1().ConfigMaps(argoproj.K3sArgoCDNamespace).Get(ctx, argoproj.ArgoCDTLSCertsConfigMapName, metav1.GetOptions{})
	if err != nil {
		return false, nil
	}
	for _, o := range objs {
		if o.GetName() == argoproj.ArgoCDKnownHostsConfigMapName {
			var cm corev1.ConfigMap
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(o.UnstructuredContent(), &cm); err != nil {
				return false, err
			}
			// Upserts argocd-ssh-known-hosts-cm ConfigMap with the values present in upstream manifest.
			if err := tnt.UpsertKnownHostsConfigMapWithUpstream(&cm); err != nil {
				// If the upstream argocd-ssh-known-hosts-cm ConfigMap doesn't exist, we skip the next steps and wait until it is created.
				if k8serrors.IsNotFound(err) {
					return false, nil
				}
				return false, err
			}
			obj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&cm)
			if err != nil {
				return false, err
			}
			o.SetUnstructuredContent(obj)
		}
		o.SetNamespace(argoproj.K3sArgoCDNamespace)
		if o.GetKind() == "Secret" || o.GetKind() == "ConfigMap" {
			labels := make(map[string]string)
			labels["app.kubernetes.io/name"] = o.GetName()
			labels["app.kubernetes.io/part-of"] = "argocd"
			// preserve the `generation` label for argocd-ssh-known-hosts-cm and argocd-tls-certs-cm
			if o.GetName() == argoproj.ArgoCDKnownHostsConfigMapName {
				labels[client.LabelKeyGeneration] = hostsCM.Labels[client.LabelKeyGeneration]
			}
			if o.GetName() == argoproj.ArgoCDTLSCertsConfigMapName {
				labels[client.LabelKeyGeneration] = certsCM.Labels[client.LabelKeyGeneration]
			}
			original := o.GetLabels()
			if v, ok := original[argoproj.LabelKeySecretType]; ok {
				labels[argoproj.LabelKeySecretType] = v
				labels[client.LabelKeyInstanceID] = instanceID
			}
			o.SetLabels(labels)
		}
		if _, err := kubectl.ApplyResource(ctx, o, kube.ApplyOpts{Validate: true}); err != nil {
			return false, err
		}
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_REPO_CREDENTIAL_SECRETS] {
		deletedCredentialSecrets, err := s.getDeletedRepoCredentialSecretNames(ctx, k3sKubeClient, objs)
		if err != nil {
			return false, err
		}
		for _, name := range deletedCredentialSecrets {
			if err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
				return false, err
			}
		}
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APPLICATIONS] {
		deletedAppNames, err := s.getDeletedArgoCDResourceNames(ctx, k3sDynamicClient, argocdutil.ApplicationGVR, argoproj.ApplicationKind, objs)
		if err != nil {
			return false, err
		}
		for _, name := range deletedAppNames {
			if err := k3sDynamicClient.Resource(argocdutil.ApplicationGVR).Namespace(argoproj.K3sArgoCDNamespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
				return false, err
			}
		}
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APPLICATION_SETS] {
		deletedAppSetNames, err := s.getDeletedArgoCDResourceNames(ctx, k3sDynamicClient, argocdutil.ApplicationsetGVR, argoproj.ApplicationSetKind, objs)
		if err != nil {
			return false, err
		}
		for _, name := range deletedAppSetNames {
			if err := k3sDynamicClient.Resource(argocdutil.ApplicationsetGVR).Namespace(argoproj.K3sArgoCDNamespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
				return false, err
			}
		}
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_APP_PROJECTS] {
		deletedAppProjectNames, err := s.getDeletedArgoCDResourceNames(ctx, k3sDynamicClient, argocdutil.AppprojectsGVR, argoproj.ProjectKind, objs)
		if err != nil {
			return false, err
		}
		for _, name := range deletedAppProjectNames {
			if err := k3sDynamicClient.Resource(argocdutil.AppprojectsGVR).Namespace(argoproj.K3sArgoCDNamespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
				return false, err
			}
		}
	}
	return true, nil
}

func (s *ArgoCDV1Server) getDeletedArgoCDResourceNames(ctx context.Context, k3sDynamicClient dynamic.Interface, gvr schema.GroupVersionResource, kind string, applyObjs []*unstructured.Unstructured) ([]string, error) {
	var existingObjs []unstructured.Unstructured
	objs, err := k3sDynamicClient.Resource(gvr).Namespace(argoproj.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	existingObjs = append(existingObjs, objs.Items...)
	keepMap := make(map[string]bool)
	for _, obj := range applyObjs {
		if obj.GetKind() == kind {
			keepMap[obj.GetName()] = true
		}
	}
	var deletedNames []string
	for _, obj := range existingObjs {
		// skip the objects that are managed by other Applications or ApplicationSets
		if !isManagedObject(obj) && !keepMap[obj.GetName()] {
			deletedNames = append(deletedNames, obj.GetName())
		}
	}
	return deletedNames, nil
}

func (s *ArgoCDV1Server) getDeletedRepoCredentialSecretNames(ctx context.Context, k3sKubeClient kubernetes.Interface, applyObjs []*unstructured.Unstructured) ([]string, error) {
	var existingSecrets []corev1.Secret
	repoSecrets, err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", argoproj.LabelKeySecretType, argoproj.LabelValueSecretTypeRepository),
	})
	if err != nil {
		return nil, err
	}
	existingSecrets = append(existingSecrets, repoSecrets.Items...)
	repoTemplateSecrets, err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", argoproj.LabelKeySecretType, argoproj.LabelValueSecretTypeRepoCreds),
	})
	if err != nil {
		return nil, err
	}
	existingSecrets = append(existingSecrets, repoTemplateSecrets.Items...)
	keepSecretsMap := make(map[string]bool)
	for _, obj := range applyObjs {
		if obj.GetKind() == "Secret" {
			keepSecretsMap[obj.GetName()] = true
		}
	}
	var deletedSecretNames []string
	for _, secret := range existingSecrets {
		if !keepSecretsMap[secret.GetName()] {
			deletedSecretNames = append(deletedSecretNames, secret.GetName())
		}
	}
	return deletedSecretNames, nil
}

// newInvalidApplySpec is a helper to return an invalid argument error due to a bad spec
func newInvalidApplySpec(specName, message string) error {
	return status.Error(codes.InvalidArgument, fmt.Sprintf("invalid %s spec: %s", specName, message))
}

// Creates an instance and returns the resulting ID
func (s *ArgoCDV1Server) createInstance(
	ctx context.Context,
	applyReq *argocdv1.ApplyInstanceRequest,
) (string, error) {
	argocd, err := kubeArgoCDSpec(applyReq.Argocd)
	if err != nil {
		return "", err
	}
	if argocd == nil {
		return "", status.Error(codes.InvalidArgument, "ArgoCD spec must be specified when creating instances")
	}
	createInstanceRequest := &argocdv1.CreateInstanceRequest{
		OrganizationId: applyReq.OrganizationId,
		Name:           applyReq.Id,
		Description:    &argocd.Spec.Description,
		Version:        argocd.Spec.Version,
		Shard:          &argocd.Spec.Shard,
		WorkspaceId:    applyReq.WorkspaceId,
	}
	createRes, err := s.CreateInstance(ctx, createInstanceRequest)
	if err != nil {
		return "", err
	}
	return createRes.Instance.Id, nil
}

func kubeArgoCDSpec(spec *structpb.Struct) (*v1alpha1.ArgoCD, error) {
	if spec == nil {
		return nil, nil
	}
	argoCDMap := spec.AsMap()
	if argoCDMap["spec"] == nil {
		return nil, nil
	}
	var argocd v1alpha1.ArgoCD
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(argoCDMap, &argocd); err != nil {
		return nil, newInvalidApplySpec("ArgoCD", err.Error())
	}
	return &argocd, nil
}

func kubeClusterSpec(spec *structpb.Struct) (*v1alpha1.Cluster, error) {
	var cluster v1alpha1.Cluster
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(spec.AsMap(), &cluster); err != nil {
		return nil, newInvalidApplySpec("Cluster", err.Error())
	}
	return &cluster, nil
}

func kubeCMPSpec(spec *structpb.Struct) (*argocdv1alpha1.ConfigManagementPlugin, error) {
	var cmp argocdv1alpha1.ConfigManagementPlugin
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(spec.AsMap(), &cmp); err != nil {
		return nil, newInvalidApplySpec("ConfigManagementPlugin", err.Error())
	}
	if cmp.GetAnnotations()[argocdv1alpha1.AnnotationCMPImage] == "" {
		return nil, newInvalidApplySpec("ConfigManagementPlugin", fmt.Sprintf("ConfigManagementPlugin image annotation %q is required", argocdv1alpha1.AnnotationCMPImage))
	}
	return &cmp, nil
}

func kubeArgoCDResource(obj *structpb.Struct, kind string) (*unstructured.Unstructured, error) {
	var o unstructured.Unstructured
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.AsMap(), &o); err != nil {
		return nil, newInvalidApplySpec(kind, err.Error())
	}
	return &o, nil
}

// convertApplyRequest converts an apply request to other API requests which we will make as part of the overall apply
func convertApplyRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance, existingClusters []*models.ArgoCDCluster, existingCMPs []*argocdv1.ConfigManagementPlugin, pruneResourceTypes map[argocdv1.PruneResourceType]bool, autoscalerConfig common.AutoScalerConfig) (*convertedApplyRequest, error) {
	var reqs convertedApplyRequest
	var err error
	reqs.patchInstanceRequest, err = toPatchInstanceRequest(applyReq, instanceID)
	if err != nil {
		return nil, err
	}
	reqs.patchInstanceSecretRequest, err = toPatchInstanceSecretRequest(applyReq, instanceID, existing)
	if err != nil {
		return nil, err
	}
	reqs.updateInstanceNotificationConfigRequest, err = toUpdateInstanceNotificationConfigRequest(applyReq, instanceID)
	if err != nil {
		return nil, err
	}
	reqs.patchInstanceNotificationSecretRequest, err = toPatchInstanceNotificationSecretRequest(applyReq, instanceID, existing)
	if err != nil {
		return nil, err
	}
	reqs.upsertInstanceAccountRequests, reqs.deleteInstanceAccountRequests, err = toInstanceAccountRequests(applyReq, instanceID, existing)
	if err != nil {
		return nil, err
	}
	reqs.updateInstanceImageUpdaterConfigRequest, err = toUpdateInstanceImageUpdaterConfigRequest(applyReq, instanceID)
	if err != nil {
		return nil, err
	}
	reqs.updateInstanceImageUpdaterSSHConfigRequest, err = toUpdateInstanceImageUpdaterSSHConfigRequest(applyReq, instanceID)
	if err != nil {
		return nil, err
	}
	reqs.patchInstanceImageUpdaterSecretRequest, err = toPatchInstanceImageUpdaterSecretRequest(applyReq, instanceID, existing)
	if err != nil {
		return nil, err
	}
	reqs.updateInstanceResourceCustomizationsRequest, err = toUpdateInstanceResourceCustomizationsRequest(applyReq, instanceID)
	if err != nil {
		return nil, err
	}
	reqs.patchInstanceAppSetSecretRequest, err = toPatchInstanceAppSetSecretRequest(applyReq, instanceID, existing)
	if err != nil {
		return nil, err
	}
	reqs.createInstanceClusterRequests, reqs.updateInstanceClusterRequests, reqs.deleteInstanceClusterRequests, err = toInstanceClusterRequests(applyReq, instanceID, existingClusters, autoscalerConfig)
	if err != nil {
		return nil, err
	}
	reqs.updateConfigManagementPluginsRequest, err = toUpdateInstanceConfigManagementPluginsRequest(applyReq, instanceID, existingCMPs, pruneResourceTypes)
	if err != nil {
		return nil, err
	}
	return &reqs, nil
}

func toPatchInstanceRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string) (*argocdv1.PatchInstanceRequest, error) {
	argocd, err := kubeArgoCDSpec(applyReq.Argocd)
	if err != nil {
		return nil, newInvalidApplySpec("ArgoCD", err.Error())
	}

	// patchMap Contains a map of fields which we will be sending to the patch API. The fields names
	// should map to the json field in the `protobuf:"...,json=XXXXX"` tags of argocdv1.Instance in
	// argocd.pb.go: name, version, description, spec, config, rbacConfig
	patchMap := make(map[string]interface{})
	if argocd != nil {
		if argocd.Name == "" {
			return nil, newInvalidApplySpec("ArgoCD", "metadata.name required")
		}
		if argocd.Spec.Version == "" {
			return nil, newInvalidApplySpec("ArgoCD", "spec.version required")
		}

		patchMap["name"] = argocd.Name
		patchMap["version"] = argocd.Spec.Version
		if argocd.Spec.Description != "" {
			patchMap["description"] = argocd.Spec.Description
		}

		instanceSpec, ok, err := unstructured.NestedMap(applyReq.Argocd.AsMap(), "spec", "instanceSpec")
		if err != nil {
			return nil, newInvalidApplySpec("ArgoCD", err.Error())
		} else if ok {
			patchMap["spec"] = instanceSpec
		}
	}

	if applyReq.ArgocdConfigmap != nil {
		obj := &models.ArgoCDConfigMap{}
		if err = validateArgoCDConfigMap(applyReq.ArgocdConfigmap, "argocd-cm", obj); err != nil {
			return nil, err
		}
		cm, err := kubeArgoCDConfigMapToProtoConfigMap(applyReq.ArgocdConfigmap)
		if err != nil {
			return nil, err
		}
		patchMap["config"] = cm
	}

	if applyReq.ArgocdRbacConfigmap != nil {
		obj := &models.ArgoCDRbacConfigMap{}
		if err = validateArgoCDConfigMap(applyReq.ArgocdRbacConfigmap, "argocd-rbac-cm", obj); err != nil {
			return nil, err
		}
		cm, err := kubeArgoCDRBACConfigMapToProtoRBACConfigMap(applyReq.ArgocdRbacConfigmap)
		if err != nil {
			return nil, err
		}
		patchMap["rbacConfig"] = cm
	}
	patchMap, err = shared.ProtoToMap(patchMap)
	if err != nil {
		return nil, err
	}
	structPatch, err := structpb.NewStruct(patchMap)
	if err != nil {
		return nil, err
	}
	return &argocdv1.PatchInstanceRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Patch:          structPatch,
	}, nil
}

// parseAccountString parses an account entry in the argocd-cm to return the account name and capabilities:
//
//	accounts.alice: apiKey, login
//	accounts.alice.enabled: "true"
func parseAccountString(key, value string) (*string, *argocdv1.InstanceAccountCapabilities, *bool) {
	parts := strings.Split(key, ".")
	partsLen := len(parts)
	if parts[0] != "accounts" || partsLen > 3 || partsLen < 2 {
		return nil, nil, nil
	}
	accountName := parts[1]
	var capabilities *argocdv1.InstanceAccountCapabilities
	var disabled *bool

	if partsLen == 2 {
		capabilities = &argocdv1.InstanceAccountCapabilities{}
		for _, cap := range strings.Split(value, ",") {
			if strings.TrimSpace(cap) == models.AccountCapabilityAPIKey {
				capabilities.ApiKey = true
			} else if strings.TrimSpace(cap) == models.AccountCapabilityLogin {
				capabilities.Login = true
			}
		}
	} else if partsLen == 3 && parts[2] == "enabled" {
		if value == "true" {
			v := false
			disabled = &v
		} else {
			v := true
			disabled = &v
		}
	}

	return &accountName, capabilities, disabled
}

// toInstanceAccountRequests converts entries in the argocd-cm to a list of UpsertInstanceAccountRequest and DeleteInstanceAccountRequest
func toInstanceAccountRequests(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance) ([]*argocdv1.UpsertInstanceAccountRequest, []*argocdv1.DeleteInstanceAccountRequest, error) {
	if applyReq.ArgocdConfigmap == nil {
		return nil, nil, nil
	}
	var cm corev1.ConfigMap
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(applyReq.ArgocdConfigmap.AsMap(), &cm); err != nil {
		return nil, nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
	}
	upsertMap := make(map[string]*argocdv1.UpsertInstanceAccountRequest)
	for k, v := range cm.Data {
		accountName, capabilities, disabled := parseAccountString(k, v)
		if accountName == nil {
			continue
		}
		accReq, ok := upsertMap[*accountName]
		if !ok {
			accReq = &argocdv1.UpsertInstanceAccountRequest{
				OrganizationId: applyReq.OrganizationId,
				InstanceId:     instanceID,
				Name:           *accountName,
			}
		}
		if capabilities != nil {
			accReq.Capabilities = capabilities
		}
		if disabled != nil {
			accReq.Disabled = disabled
		}
		upsertMap[*accountName] = accReq
	}
	var upsertList []*argocdv1.UpsertInstanceAccountRequest
	for k := range upsertMap {
		r := upsertMap[k]
		upsertList = append(upsertList, r)
	}

	// Now delete any entries that no longer appear in the configmap
	deleteMap := make(map[string]bool)
	var deleteList []*argocdv1.DeleteInstanceAccountRequest
	if existing != nil && len(existing.ArgocdCM.JSON) > 0 {
		var existingCM models.ArgoCDConfigMap
		if err := json.Unmarshal(existing.ArgocdCM.JSON, &existingCM); err != nil {
			return nil, nil, err
		}
		for _, account := range existingCM.Accounts {
			if _, ok := upsertMap[account.Name]; !ok && !deleteMap[account.Name] {
				// if it doesn't exists in the upsert map, it means we should delete it
				deleteList = append(deleteList, &argocdv1.DeleteInstanceAccountRequest{
					OrganizationId: applyReq.OrganizationId,
					InstanceId:     instanceID,
					Name:           account.Name,
				})
				deleteMap[account.Name] = true
			}
		}
	}

	return upsertList, deleteList, nil
}

func toUpdateInstanceConfigManagementPluginsRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existingCMPs []*argocdv1.ConfigManagementPlugin, pruneResourceTypes map[argocdv1.PruneResourceType]bool) (*argocdv1.UpdateInstanceConfigManagementPluginsRequest, error) {
	existingCMPMap := make(map[string]*argocdv1.ConfigManagementPlugin)
	deleteCMPMap := make(map[string]bool)
	for _, cmp := range existingCMPs {
		existingCMPMap[cmp.Name] = cmp
		deleteCMPMap[cmp.Name] = true
	}
	var cmps []*argocdv1.ConfigManagementPlugin
	for _, c := range applyReq.ConfigManagementPlugins {
		cmp, err := kubeCMPSpec(c)
		if err != nil {
			return nil, err
		}
		deleteCMPMap[cmp.Name] = false
		spec, err := toArgoCDCMPSpec(&cmp.Spec)
		if err != nil {
			return nil, err
		}
		cmps = append(cmps, &argocdv1.ConfigManagementPlugin{
			Name:    cmp.Name,
			Enabled: cmp.GetAnnotations()[argocdv1alpha1.AnnotationCMPEnabled] == "true",
			Image:   cmp.GetAnnotations()[argocdv1alpha1.AnnotationCMPImage],
			Spec:    spec,
		})
	}
	if pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] || pruneResourceTypes[argocdv1.PruneResourceType_PRUNE_RESOURCE_TYPE_CONFIG_MANAGEMENT_PLUGINS] {
		return &argocdv1.UpdateInstanceConfigManagementPluginsRequest{
			OrganizationId: applyReq.OrganizationId,
			Id:             instanceID,
			Plugins:        cmps,
		}, nil
	}
	for name, deleted := range deleteCMPMap {
		if deleted {
			cmps = append(cmps, existingCMPMap[name])
		}
	}
	return &argocdv1.UpdateInstanceConfigManagementPluginsRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Plugins:        cmps,
	}, nil
}

func toInstanceClusterRequests(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existingClusters []*models.ArgoCDCluster, autoscalerConfig common.AutoScalerConfig) ([]*argocdv1.CreateInstanceClusterRequest, []*argocdv1.UpdateInstanceClusterRequest, []*argocdv1.DeleteInstanceClusterRequest, error) {
	var createReqs []*argocdv1.CreateInstanceClusterRequest
	var updateReqs []*argocdv1.UpdateInstanceClusterRequest
	var deleteReqs []*argocdv1.DeleteInstanceClusterRequest
	existingClustersMap := make(map[string]*models.ArgoCDCluster)
	deleteClusterMap := make(map[string]bool)
	for _, c := range existingClusters {
		existingClustersMap[c.Name] = c
		deleteClusterMap[c.ID] = true
	}
	for _, c := range applyReq.Clusters {
		cluster, err := kubeClusterSpec(c)
		if err != nil {
			return nil, nil, nil, err
		}
		data, err := toArgoCDClusterData(cluster, autoscalerConfig)
		if err != nil {
			return nil, nil, nil, err
		}
		if existingCluster, ok := existingClustersMap[cluster.Name]; ok {
			updateReqs = append(updateReqs, &argocdv1.UpdateInstanceClusterRequest{
				OrganizationId: applyReq.OrganizationId,
				InstanceId:     instanceID,
				Id:             existingCluster.ID,
				Description:    cluster.Spec.Description,
				Data:           data,
			})
			deleteClusterMap[existingCluster.ID] = false
		} else {
			createReqs = append(createReqs, &argocdv1.CreateInstanceClusterRequest{
				OrganizationId: applyReq.OrganizationId,
				InstanceId:     instanceID,
				Name:           cluster.Name,
				Description:    cluster.Spec.Description,
				Data:           data,
				Upsert:         false,
			})
		}
	}
	for clusterID, deleted := range deleteClusterMap {
		if deleted {
			deleteReqs = append(deleteReqs, &argocdv1.DeleteInstanceClusterRequest{
				OrganizationId: applyReq.OrganizationId,
				InstanceId:     instanceID,
				Id:             clusterID,
			})
		}
	}
	return createReqs, updateReqs, deleteReqs, nil
}

func toArgoCDClusterData(cluster *v1alpha1.Cluster, autoscalerConfig common.AutoScalerConfig) (*argocdv1.ClusterData, error) {
	if cluster == nil {
		return nil, nil
	}
	var kustomization *structpb.Struct
	if cluster.Spec.Data.Kustomization.Raw != nil {
		var kustomizationMap map[string]interface{}
		if err := json.Unmarshal(cluster.Spec.Data.Kustomization.Raw, &kustomizationMap); err != nil {
			return nil, err
		}
		var err error
		if kustomization, err = structpb.NewStruct(kustomizationMap); err != nil {
			return nil, err
		}
	}
	var size argocdv1.ClusterSize
	switch option.ArgoCDClusterAgentSize(cluster.Spec.Data.Size) {
	case option.ArgoCDClusterAgentSizeSmall:
		size = argocdv1.ClusterSize_CLUSTER_SIZE_SMALL
	case option.ArgoCDClusterAgentSizeMedium:
		size = argocdv1.ClusterSize_CLUSTER_SIZE_MEDIUM
	case option.ArgoCDClusterAgentSizeLarge:
		size = argocdv1.ClusterSize_CLUSTER_SIZE_LARGE
	case option.ArgoCDClusterAgentSizeAuto:
		size = argocdv1.ClusterSize_CLUSTER_SIZE_AUTO
	default:
		return nil, fmt.Errorf("invalid agent size: %v", size)
	}
	var managedClusterConfig *argocdv1.ManagedClusterConfig
	if cluster.Spec.Data.ManagedClusterConfig != nil {
		managedClusterConfig = &argocdv1.ManagedClusterConfig{
			SecretKey:  cluster.Spec.Data.ManagedClusterConfig.SecretKey,
			SecretName: cluster.Spec.Data.ManagedClusterConfig.SecretName,
		}
	}
	asc := &argocdv1.AutoScalerConfig{}
	if autoscalerConfig.RepoServer != nil {
		asc.RepoServer = &argocdv1.RepoServerAutoScalingConfig{
			ResourceMinimum: &argocdv1.Resources{
				Cpu: autoscalerConfig.RepoServer.ResourceMinimum.CPU.String(),
				Mem: autoscalerConfig.RepoServer.ResourceMinimum.Memory.String(),
			},
			ResourceMaximum: &argocdv1.Resources{
				Cpu: autoscalerConfig.RepoServer.ResourceMaximum.CPU.String(),
				Mem: autoscalerConfig.RepoServer.ResourceMaximum.Memory.String(),
			},
			ReplicaMaximum: autoscalerConfig.RepoServer.ReplicaMaximum,
			ReplicaMinimum: autoscalerConfig.RepoServer.ReplicaMinimum,
		}
	}
	if autoscalerConfig.ApplicationController != nil {
		asc.ApplicationController = &argocdv1.AppControllerAutoScalingConfig{
			ResourceMinimum: &argocdv1.Resources{
				Cpu: autoscalerConfig.ApplicationController.ResourceMinimum.CPU.String(),
				Mem: autoscalerConfig.ApplicationController.ResourceMinimum.Memory.String(),
			},
			ResourceMaximum: &argocdv1.Resources{
				Cpu: autoscalerConfig.ApplicationController.ResourceMaximum.CPU.String(),
				Mem: autoscalerConfig.ApplicationController.ResourceMaximum.Memory.String(),
			},
		}
	}
	if cluster.Spec.Data.AutoscalerConfig != nil {
		if cluster.Spec.Data.AutoscalerConfig.RepoServer != nil {
			asc.RepoServer = &argocdv1.RepoServerAutoScalingConfig{
				ResourceMinimum: &argocdv1.Resources{
					Cpu: cluster.Spec.Data.AutoscalerConfig.RepoServer.ResourceMinimum.Cpu,
					Mem: cluster.Spec.Data.AutoscalerConfig.RepoServer.ResourceMinimum.Mem,
				},
				ResourceMaximum: &argocdv1.Resources{
					Cpu: cluster.Spec.Data.AutoscalerConfig.RepoServer.ResourceMaximum.Cpu,
					Mem: cluster.Spec.Data.AutoscalerConfig.RepoServer.ResourceMaximum.Mem,
				},
				ReplicaMaximum: cluster.Spec.Data.AutoscalerConfig.RepoServer.ReplicaMaximum,
				ReplicaMinimum: cluster.Spec.Data.AutoscalerConfig.RepoServer.ReplicaMinimum,
			}
		}
		if cluster.Spec.Data.AutoscalerConfig.ApplicationController != nil {
			asc.ApplicationController = &argocdv1.AppControllerAutoScalingConfig{
				ResourceMinimum: &argocdv1.Resources{
					Cpu: cluster.Spec.Data.AutoscalerConfig.ApplicationController.ResourceMinimum.Cpu,
					Mem: cluster.Spec.Data.AutoscalerConfig.ApplicationController.ResourceMinimum.Mem,
				},
				ResourceMaximum: &argocdv1.Resources{
					Cpu: cluster.Spec.Data.AutoscalerConfig.ApplicationController.ResourceMaximum.Cpu,
					Mem: cluster.Spec.Data.AutoscalerConfig.ApplicationController.ResourceMaximum.Mem,
				},
			}
		}
	}
	compatibility := &argocdv1.ClusterCompatibility{}
	if cluster.Spec.Data.Compatibility != nil {
		compatibility = &argocdv1.ClusterCompatibility{
			Ipv6Only: cluster.Spec.Data.Compatibility.Ipv6Only,
		}
	}
	var argocdNotificationsSettings *argocdv1.ClusterArgoCDNotificationsSettings
	if cluster.Spec.Data.ArgocdNotificationsSettings != nil {
		argocdNotificationsSettings = &argocdv1.ClusterArgoCDNotificationsSettings{
			InClusterSettings: cluster.Spec.Data.ArgocdNotificationsSettings.InClusterSettings,
		}
	}
	var directClusterSpec *argocdv1.DirectClusterSpec
	if cluster.Spec.Data.DirectClusterSpec != nil {
		if cluster.Spec.Data.DirectClusterSpec.ClusterType == "kargo" {
			directClusterSpec = &argocdv1.DirectClusterSpec{
				KargoInstanceId: cluster.Spec.Data.DirectClusterSpec.KargoInstanceId,
				ClusterType:     argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_KARGO,
			}
		} else {
			return nil, status.Errorf(codes.InvalidArgument, `declarative api does not support "%v" clusterType`, cluster.Spec.Data.DirectClusterSpec.ClusterType)
		}
	}
	return &argocdv1.ClusterData{
		Size:                            size,
		Labels:                          cluster.Labels,
		Annotations:                     cluster.Annotations,
		AutoUpgradeDisabled:             cluster.Spec.Data.AutoUpgradeDisabled,
		Kustomization:                   kustomization,
		AppReplication:                  cluster.Spec.Data.AppReplication,
		RedisTunneling:                  cluster.Spec.Data.RedisTunneling,
		TargetVersion:                   cluster.Spec.Data.TargetVersion,
		Namespace:                       cluster.Namespace,
		NamespaceScoped:                 cluster.Spec.NamespaceScoped,
		DatadogAnnotationsEnabled:       cluster.Spec.Data.DatadogAnnotationsEnabled,
		EksAddonEnabled:                 cluster.Spec.Data.EksAddonEnabled,
		ManagedClusterConfig:            managedClusterConfig,
		MultiClusterK8SDashboardEnabled: cluster.Spec.Data.MultiClusterK8SDashboardEnabled,
		AutoscalerConfig:                asc,
		Project:                         cluster.Spec.Data.Project,
		Compatibility:                   compatibility,
		ArgocdNotificationsSettings:     argocdNotificationsSettings,
		DirectClusterSpec:               directClusterSpec,
	}, nil
}

func toArgoCDCMPSpec(cmpSpec *argocdv1alpha1.PluginSpec) (*argocdv1.PluginSpec, error) {
	if cmpSpec == nil {
		return nil, nil
	}
	specData, err := json.Marshal(cmpSpec)
	if err != nil {
		return nil, err
	}
	var spec *argocdv1.PluginSpec
	if err := json.Unmarshal(specData, &spec); err != nil {
		return nil, err
	}
	return spec, nil
}

func toPatchInstanceSecretRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance) (*argocdv1.PatchInstanceSecretRequest, error) {
	if applyReq.ArgocdSecret == nil {
		return nil, nil
	}
	secretData, err := toSecretMap(argoproj.ArgoCDSecretName, applyReq.ArgocdSecret)
	if err != nil {
		return nil, err
	}
	if len(secretData) == 0 {
		return nil, nil
	}
	req := argocdv1.PatchInstanceSecretRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Secret:         make(map[string]*argocdv1.PatchInstanceSecretRequest_ValueField, len(secretData)),
	}
	for k, v := range secretData {
		req.Secret[k] = &argocdv1.PatchInstanceSecretRequest_ValueField{
			Value: ptr.To(v),
		}
	}
	if existing != nil {
		existingSecrets, err := existing.GetArgocdSecret()
		if err != nil {
			return nil, err
		}
		for k := range existingSecrets {
			_, isDesired := secretData[k]
			if instances.IsManagedArgoCDSecretKey(k) || isDesired {
				continue
			}
			req.Secret[k] = &argocdv1.PatchInstanceSecretRequest_ValueField{
				Value: nil,
			}
		}
	}
	return &req, nil
}

func toUpdateInstanceNotificationConfigRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string) (*argocdv1.UpdateInstanceNotificationConfigRequest, error) {
	if applyReq.NotificationsConfigmap == nil {
		return nil, nil
	}
	var cm corev1.ConfigMap
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(applyReq.NotificationsConfigmap.AsMap(), &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDNotificationsConfigMapName, err.Error())
	}
	return &argocdv1.UpdateInstanceNotificationConfigRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Config:         cm.Data,
	}, nil
}

func toPatchInstanceNotificationSecretRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance) (*argocdv1.PatchInstanceNotificationSecretRequest, error) {
	if applyReq.NotificationsSecret == nil {
		return nil, nil
	}
	secretData, err := toSecretMap(argoproj.ArgoCDNotificationsSecretName, applyReq.NotificationsSecret)
	if err != nil {
		return nil, err
	}
	if len(secretData) == 0 {
		return nil, nil
	}
	req := argocdv1.PatchInstanceNotificationSecretRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Secret:         make(map[string]*argocdv1.PatchInstanceNotificationSecretRequest_ValueField, len(secretData)),
	}
	for k, v := range secretData {
		req.Secret[k] = &argocdv1.PatchInstanceNotificationSecretRequest_ValueField{
			Value: ptr.To(v),
		}
	}
	if existing != nil {
		existingSecrets, err := existing.GetArgoCDNotificationsSecret()
		if err != nil {
			return nil, err
		}
		for k := range existingSecrets {
			_, isDesired := secretData[k]
			if instances.IsManagedArgoCDSecretKey(k) || isDesired {
				continue
			}
			req.Secret[k] = &argocdv1.PatchInstanceNotificationSecretRequest_ValueField{
				Value: nil,
			}
		}
	}
	return &req, nil
}

func toUpdateInstanceImageUpdaterConfigRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string) (*argocdv1.UpdateInstanceImageUpdaterConfigRequest, error) {
	if applyReq.ImageUpdaterConfigmap == nil {
		return nil, nil
	}
	var cm corev1.ConfigMap
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(applyReq.ImageUpdaterConfigmap.AsMap(), &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDImageUpdaterConfigMapName, err.Error())
	}
	argocd, err := kubeArgoCDSpec(applyReq.Argocd)
	if err != nil {
		return nil, newInvalidApplySpec("ArgoCD", err.Error())
	}
	updateImageUpdaterConfigReq := argocdv1.UpdateInstanceImageUpdaterConfigRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Config:         cm.Data,
		Version:        argocd.Spec.InstanceSpec.ImageUpdaterVersion,
	}
	return &updateImageUpdaterConfigReq, nil
}

func toUpdateInstanceImageUpdaterSSHConfigRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string) (*argocdv1.UpdateInstanceImageUpdaterSSHConfigRequest, error) {
	if applyReq.ImageUpdaterSshConfigmap == nil {
		return nil, nil
	}
	var cm corev1.ConfigMap
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(applyReq.ImageUpdaterSshConfigmap.AsMap(), &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDImageUpdaterSSHConfigMapName, err.Error())
	}
	updateImageUpdaterSSHConfigReq := argocdv1.UpdateInstanceImageUpdaterSSHConfigRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Config:         cm.Data,
	}
	return &updateImageUpdaterSSHConfigReq, nil
}

func toPatchInstanceImageUpdaterSecretRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance) (*argocdv1.PatchInstanceImageUpdaterSecretRequest, error) {
	if applyReq.ImageUpdaterSecret == nil {
		return nil, nil
	}
	secretData, err := toSecretMap(argoproj.ArgoCDImageUpdaterSecretName, applyReq.ImageUpdaterSecret)
	if err != nil {
		return nil, err
	}
	if len(secretData) == 0 {
		return nil, nil
	}
	req := argocdv1.PatchInstanceImageUpdaterSecretRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Secret:         make(map[string]*argocdv1.PatchInstanceImageUpdaterSecretRequest_ValueField, len(secretData)),
	}
	for k, v := range secretData {
		req.Secret[k] = &argocdv1.PatchInstanceImageUpdaterSecretRequest_ValueField{
			Value: ptr.To(v),
		}
	}
	if existing != nil {
		existingSecrets, err := existing.GetArgoCDImageUpdaterSecret()
		if err != nil {
			return nil, err
		}
		for k := range existingSecrets {
			_, isDesired := secretData[k]
			if instances.IsManagedArgoCDSecretKey(k) || isDesired {
				continue
			}
			req.Secret[k] = &argocdv1.PatchInstanceImageUpdaterSecretRequest_ValueField{
				Value: nil,
			}
		}
	}
	return &req, nil
}

func toPatchInstanceAppSetSecretRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string, existing *instances.ArgoCDInstance) (*argocdv1.PatchInstanceAppsetSecretRequest, error) {
	if applyReq.ApplicationSetSecret == nil {
		return nil, nil
	}
	secretData, err := toSecretMap(argoproj.ArgoCDApplicationSetSecretName, applyReq.ApplicationSetSecret)
	if err != nil {
		return nil, err
	}
	if len(secretData) == 0 {
		return nil, nil
	}
	req := argocdv1.PatchInstanceAppsetSecretRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Secret:         make(map[string]*argocdv1.PatchInstanceAppsetSecretRequest_ValueField, len(secretData)),
	}
	for k, v := range secretData {
		req.Secret[k] = &argocdv1.PatchInstanceAppsetSecretRequest_ValueField{
			Value: ptr.To(v),
		}
	}
	if existing != nil {
		existingSecrets, err := existing.GetArgoCDAppsetSecret()
		if err != nil {
			return nil, err
		}
		for k := range existingSecrets {
			_, isDesired := secretData[k]
			if instances.IsManagedArgoCDSecretKey(k) || isDesired {
				continue
			}
			req.Secret[k] = &argocdv1.PatchInstanceAppsetSecretRequest_ValueField{
				Value: nil,
			}
		}
	}
	return &req, nil
}

func toUpdateInstanceResourceCustomizationsRequest(applyReq *argocdv1.ApplyInstanceRequest, instanceID string) (*argocdv1.UpdateInstanceResourceCustomizationsRequest, error) {
	if applyReq.ArgocdConfigmap == nil || applyReq.ArgocdConfigmap.Fields["data"] == nil {
		return nil, nil
	}
	data, err := applyReq.ArgocdConfigmap.Fields["data"].MarshalJSON()
	if err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
	}
	var cm models.ArgoCDConfigMap
	if err := json.Unmarshal(data, &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
	}
	if len(cm.ResourceCustomizations) == 0 {
		return nil, nil
	}
	var resourceCustomizations []*argocdv1.ResourceCustomizationConfig
	for _, resourceCustomization := range cm.ResourceCustomizations {
		config := &argocdv1.ResourceCustomizationConfig{
			Group:             resourceCustomization.Group,
			Kind:              resourceCustomization.Kind,
			Health:            resourceCustomization.Health,
			Actions:           resourceCustomization.Actions,
			IgnoreDifferences: resourceCustomization.IgnoreDifferences,
			KnownTypeFields:   resourceCustomization.KnownTypeFields,
		}
		if resourceCustomization.UseOpenLibs != "" {
			val, err := strconv.ParseBool(resourceCustomization.UseOpenLibs)
			if err != nil {
				return nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
			}
			config.UseOpenLibs = ptr.To(val)
		}
		resourceCustomizations = append(resourceCustomizations, config)
	}
	updateResourceCustomizationsReq := argocdv1.UpdateInstanceResourceCustomizationsRequest{
		OrganizationId: applyReq.OrganizationId,
		Id:             instanceID,
		Resources:      resourceCustomizations,
	}
	return &updateResourceCustomizationsReq, nil
}

func toSecretMap(objName string, obj *structpb.Struct) (map[string]string, error) {
	var sec corev1.Secret
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.AsMap(), &sec); err != nil {
		return nil, newInvalidApplySpec(objName, err.Error())
	}
	if len(sec.Data) == 0 && len(sec.StringData) == 0 {
		return nil, nil
	}
	secrets := make(map[string]string)
	for k, v := range sec.Data {
		secrets[k] = string(v)
	}
	for k, v := range sec.StringData {
		secrets[k] = v
	}
	return secrets, nil
}

// Converts a kubernetes argocd-cm ConfigMap to an argocdv1.ArgoCDConfigMap
func kubeArgoCDConfigMapToProtoConfigMap(kubeCM *structpb.Struct) (*argocdv1.ArgoCDConfigMap, error) {
	cmData, err := json.Marshal(kubeCM.Fields["data"])
	if err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
	}
	var cm models.ArgoCDConfigMap
	if err := json.Unmarshal(cmData, &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDConfigMapName, err.Error())
	}
	protoCM := argocdutil.NewArgoCDConfigMapV1(&cm)
	return protoCM, nil
}

// Converts a kubernetes argocd-rbac-cm ConfigMap to an argocdv1.ArgoCDRBACConfigMap
func kubeArgoCDRBACConfigMapToProtoRBACConfigMap(kubeCM *structpb.Struct) (*argocdv1.ArgoCDRBACConfigMap, error) {
	cmData, err := json.Marshal(kubeCM.Fields["data"])
	if err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDRBACConfigMapName, err.Error())
	}
	var cm models.ArgoCDRbacConfigMap
	if err := json.Unmarshal(cmData, &cm); err != nil {
		return nil, newInvalidApplySpec(argoproj.ArgoCDRBACConfigMapName, err.Error())
	}
	protoCM := argocdutil.NewArgoCDRBACConfigMapV1(&cm)
	return protoCM, nil
}

func convertApplyK3sResources(applyReq *argocdv1.ApplyInstanceRequest) ([]*unstructured.Unstructured, error) {
	var objs []*unstructured.Unstructured
	if applyReq.ArgocdKnownHostsConfigmap != nil {
		obj, err := validateApplyConfigMap(applyReq.ArgocdKnownHostsConfigmap.AsMap(), argoproj.ArgoCDKnownHostsConfigMapName)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	if applyReq.ArgocdTlsCertsConfigmap != nil {
		obj, err := validateApplyConfigMap(applyReq.ArgocdTlsCertsConfigmap.AsMap(), argoproj.ArgoCDTLSCertsConfigMapName)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	for _, o := range applyReq.RepoCredentialSecrets {
		obj, err := validateApplyCredentialSecret(o.AsMap(), argoproj.LabelValueSecretTypeRepository)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	for _, o := range applyReq.RepoTemplateCredentialSecrets {
		obj, err := validateApplyCredentialSecret(o.AsMap(), argoproj.LabelValueSecretTypeRepoCreds)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	for _, o := range applyReq.Applications {
		obj, err := validateArgoCDResource(o, argoproj.ApplicationKind)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	for _, o := range applyReq.ApplicationSets {
		obj, err := validateArgoCDResource(o, argoproj.ApplicationSetKind)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	for _, o := range applyReq.AppProjects {
		obj, err := validateArgoCDResource(o, argoproj.ProjectKind)
		if err != nil {
			return nil, err
		}
		objs = append(objs, obj)
	}
	return objs, nil
}

func validateApplyConfigMap(configMap map[string]interface{}, name string) (*unstructured.Unstructured, error) {
	var cm corev1.ConfigMap
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(configMap, &cm); err != nil {
		return nil, newInvalidApplySpec(name, err.Error())
	}
	obj := &unstructured.Unstructured{Object: configMap}
	if obj.GetName() != name {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid name, expected name: %s, found name: %s", name, obj.GetName()))
	}
	return obj, nil
}

func validateApplyCredentialSecret(secret map[string]interface{}, credentialType string) (*unstructured.Unstructured, error) {
	var s corev1.Secret
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(secret, &s); err != nil {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid credential secret: %s", err.Error()))
	}
	obj := &unstructured.Unstructured{Object: secret}
	if obj.GetLabels()[argoproj.LabelKeySecretType] != credentialType {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid secret type, it can only be %s or %s, found: %s", argoproj.LabelValueSecretTypeRepoCreds, argoproj.LabelValueSecretTypeRepository, obj.GetLabels()[argoproj.LabelKeySecretType]))
	}
	if !strings.HasPrefix(obj.GetName(), argoproj.RepoCredentialNamePrefix) {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid secret name, repo credential secret name should start with %q", argoproj.RepoCredentialNamePrefix))
	}
	return obj, nil
}

func validateArgoCDResource(obj *structpb.Struct, kind string) (*unstructured.Unstructured, error) {
	o, err := kubeArgoCDResource(obj, kind)
	if err != nil {
		return nil, err
	}
	if o.GetAPIVersion() != argoproj.APIVersion {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid apiVersion, expected apiVersion: argoproj.io/v1alpha1, found apiVersion: %s", o.GetAPIVersion()))
	}
	if o.GetKind() != kind {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid kind, expected kind: %s, found kind: %s", kind, o.GetKind()))
	}
	return o, nil
}

type extObject interface {
	MarshalJSON() ([]byte, error)
	UnmarshalJSON(config []byte) error
}

var falsePositiveFields = map[string]bool{
	// this will be flattened to separate resources like resource.customizations.knownTypeFields.kubevirt.io_VirtualMachine
	// if unmarshal passed, this will be considered as valid since it's hard to be converted back
	"resource.customizations": true,
}

// validateConfigMap is a generic function that validates if configmap data keys are supported by the target object.
func validateArgoCDConfigMap(configMapStruct *structpb.Struct, configMapName string, obj extObject) error {
	cmData, err := json.Marshal(configMapStruct.Fields["data"])
	if err != nil {
		return newInvalidApplySpec(configMapName, err.Error())
	}

	if err := obj.UnmarshalJSON(cmData); err != nil {
		return newInvalidApplySpec(configMapName, err.Error())
	}

	validData, err := obj.MarshalJSON()
	if err != nil {
		return newInvalidApplySpec(configMapName, err.Error())
	}

	var validMap map[string]any
	if err := json.Unmarshal(validData, &validMap); err != nil {
		return newInvalidApplySpec(configMapName, err.Error())
	}

	var sourceMap map[string]any
	if err := json.Unmarshal(cmData, &sourceMap); err != nil {
		return newInvalidApplySpec(configMapName, err.Error())
	}

	invalids := make([]string, 0)
	for k := range sourceMap {
		if _, exists := validMap[k]; !exists {
			invalids = append(invalids, k)
		}
	}

	filtered := make([]string, 0)
	for _, k := range invalids {
		if falsePositiveFields[k] {
			continue
		}
		filtered = append(filtered, k)
	}
	if len(filtered) > 0 {
		return status.Errorf(codes.InvalidArgument, "unsupported field in %s: %s", configMapName, strings.Join(invalids, ", "))
	}
	return nil
}

// isManagedObject checks if the given object has tracking id annotation or owner reference, if yes, it is managed by other Applications or ApplicationSets
func isManagedObject(obj unstructured.Unstructured) bool {
	if obj.GetAnnotations()[argoproj.ArgoCDTrackingIDAnnotationKey] != "" {
		return true
	}
	return len(obj.GetOwnerReferences()) > 0
}
