package declarative

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/cli/display"
)

var renderer = display.NewBufferedRenderer(display.OutputFormatJSON)

func Test_ParseCombinedFile(t *testing.T) {
	applyReq, err := utils.ParseFiles(renderer, []string{"./testdata/combined-file/akp-instance.yaml"}, ConvertToApplyInstanceRequest)
	require.NoError(t, err)
	assert.NotNil(t, applyReq.Argocd)
	assert.NotNil(t, applyReq.ArgocdConfigmap)
	assert.NotNil(t, applyReq.ArgocdRbacConfigmap)
	assert.NotNil(t, applyReq.ArgocdSecret)
	assert.NotNil(t, applyReq.NotificationsConfigmap)
	assert.NotNil(t, applyReq.NotificationsSecret)
	assert.NotNil(t, applyReq.Clusters)
	assert.Len(t, applyReq.Clusters, 3)
	assert.NotNil(t, applyReq.ArgocdKnownHostsConfigmap)
	assert.NotNil(t, applyReq.ArgocdTlsCertsConfigmap)
	assert.NotNil(t, applyReq.RepoTemplateCredentialSecrets)
	assert.Len(t, applyReq.RepoTemplateCredentialSecrets, 1)
	assert.NotNil(t, applyReq.RepoCredentialSecrets)
	assert.Len(t, applyReq.RepoCredentialSecrets, 2)
	assert.Equal(t, "test-inst", applyReq.Id)
	assert.NotNil(t, applyReq.ConfigManagementPlugins)
	assert.Len(t, applyReq.ConfigManagementPlugins, 2)
	assert.NotNil(t, applyReq.ApplicationSetSecret)
	assert.NotNil(t, applyReq.Applications)
	assert.Len(t, applyReq.Applications, 1)
	assert.NotNil(t, applyReq.ApplicationSets)
	assert.Len(t, applyReq.ApplicationSets, 1)
	assert.NotNil(t, applyReq.AppProjects)
	assert.Len(t, applyReq.AppProjects, 1)
}

func Test_ParseMultipleFiles(t *testing.T) {
	applyReq, err := utils.ParseFiles(renderer, []string{"./testdata/multiple-files"}, ConvertToApplyInstanceRequest)
	require.NoError(t, err)
	assert.NotNil(t, applyReq.Argocd)
	assert.NotNil(t, applyReq.ArgocdConfigmap)
	assert.NotNil(t, applyReq.ArgocdRbacConfigmap)
	assert.NotNil(t, applyReq.Clusters)
	assert.Len(t, applyReq.Clusters, 3)
	assert.Nil(t, applyReq.ArgocdSecret)
	assert.Nil(t, applyReq.NotificationsConfigmap)
	assert.Nil(t, applyReq.NotificationsSecret)
	assert.NotNil(t, applyReq.ArgocdKnownHostsConfigmap)
	assert.NotNil(t, applyReq.ArgocdTlsCertsConfigmap)
	assert.NotNil(t, applyReq.RepoTemplateCredentialSecrets)
	assert.Len(t, applyReq.RepoTemplateCredentialSecrets, 1)
	assert.NotNil(t, applyReq.RepoCredentialSecrets)
	assert.Len(t, applyReq.RepoCredentialSecrets, 2)
	assert.Equal(t, "test-inst", applyReq.Id)
	assert.NotNil(t, applyReq.ConfigManagementPlugins)
	assert.Len(t, applyReq.ConfigManagementPlugins, 2)
	assert.NotNil(t, applyReq.ApplicationSetSecret)
	assert.NotNil(t, applyReq.Applications)
	assert.Len(t, applyReq.Applications, 1)
	assert.NotNil(t, applyReq.ApplicationSets)
	assert.Len(t, applyReq.ApplicationSets, 1)
	assert.NotNil(t, applyReq.AppProjects)
	assert.Len(t, applyReq.AppProjects, 1)
}

// Ensures we error if user applies a directory with multiple instances defined.
// We cannot support multiple instances because we don't know which configmap belongs to which
func Test_DisallowMultipleInstances(t *testing.T) {
	applyReq, err := utils.ParseFiles(renderer, []string{"./testdata/multiple-instances"}, ConvertToApplyInstanceRequest)
	assert.EqualError(t, err, "cannot apply multiple instances")
	assert.Nil(t, applyReq)
}
