apiVersion: argocd.akuity.io/v1alpha1
kind: ArgoCD
metadata:
  name: test-inst
spec:
  version: v2.6.0
  description: test-inst
  instanceSpec:
    ipAllowList:
      - ip: "*******"
        description: dummy entry
    declarativeManagementEnabled: true
    imageUpdaterEnabled: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
data:
  users.anonymous.enabled: "true"
  kustomize.buildOptions: --load_restrictor none
  admin.enabled: "false"
  accounts.alice: apiKey, login
  accounts.bob: login
  accounts.bob.enabled: "false"
  dex.config: |
    connectors:
      # GitHub example
      - type: github
        id: github
        name: GitHub
        config:
          clientID: aabbccddeeff00112233
          clientSecret: $dex.github.clientSecret
          orgs:
          - name: your-github-org
  resource.customizations.health.certmanager.k8s.io_Certificate: |
    hs = {}
    hs.status = "Progressing"
    hs.message = "Waiting for certificate"
    return hs
  resource.customizations.knownTypeFields.kubevirt.io_VirtualMachine: |
    - field: spec.template.spec.domain.resources
      type: core/v1/ResourceRequirements
  resource.customizations: |
    sunk.coreweave.com/NodeSet:
      knownTypeFields: |
        - field: spec.template.spec
          type: core/v1/PodSpec
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
data:
  policy.default: role:readonly
  policy.csv: |
    p, role:org-admin, applications, *, */*, allow
    p, role:org-admin, clusters, get, *, allow
    g, your-github-org:your-team, role:org-admin

---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-secret
type: Opaque
stringData:
  dex.github.clientSecret: my-github-oidc-secret
  webhook.github.secret: shhhh! it's a github secret

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
data:
  trigger.on-sync-status-unknown: |
    - when: app.status.sync.status == 'Unknown'
      send: [my-custom-template]
  template.my-custom-template: |
    message: |
      Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
  defaultTriggers: |
    - on-sync-status-unknown

---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-notifications-secret
type: Opaque
stringData:
  email-username: <EMAIL>
  email-password: password

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-image-updater-config
data:
  registries.conf: |-
    registries:
      - prefix: docker.io
        name: Docker
        api_url: https://registry-1.docker.io
        credentials: secret:argocd/argocd-image-updater-secret#my-docker-credentials
  git.email: <EMAIL>
  git.user: akuitybot

---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-image-updater-secret
type: Opaque
stringData:
  my-docker-credentials: abcd1234

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-image-updater-ssh-config
data:
  config: |-
    Host *
          PubkeyAcceptedAlgorithms +ssh-rsa
          HostkeyAlgorithms +ssh-rsa

---
apiVersion: v1
kind: Secret
metadata:
  name: application-set-secret
type: Opaque
stringData:
  my-appset-secret: xyz456
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: test-cluster-create
  namespace: test
  labels:
    test-label: "true"
  annotations:
    test-annotation: "false"
spec:
  namespaceScoped: true
  description: test-inst
  data:
    size: small
    autoUpgradeDisabled: true
    appReplication: true
    targetVersion: 0.3.30
    kustomization:
      apiVersion: kustomize.config.k8s.io/v1beta1
      kind: Kustomization
      resources:
        - test.yaml
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: kargo-test-integration
  namespace: akuity
spec:
  data:
    size: small
    directClusterSpec:
      clusterType: kargo
      kargoInstanceId: 'xyz'
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: test-cluster-update
  namespace: test
  labels:
    test-label: "true"
  annotations:
    test-annotation: "false"
spec:
  namespaceScoped: true
  description: test-inst
  data:
    size: small
    autoUpgradeDisabled: true
    appReplication: true
    targetVersion: 0.3.30
    kustomization:
      apiVersion: kustomize.config.k8s.io/v1beta1
      kind: Kustomization
      resources:
        - test.yaml
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-ssh-known-hosts-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-ssh-known-hosts-cm
data:
  ssh_known_hosts: |
    bitbucket.org ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEAubiN81eDcafrgMeLzaFPsw2kNvEcqTKl/VqLat/MaB33pZy0y3rJZtnqwR2qOOvbwKZYKiEO1O6VqNEBxKvJJelCq0dTXWT5pbO2gDXC6h6QDXCaHo6pOHGPUy+YBaGQRGuSusMEASYiWunYN0vCAI8QaXnWMXNMdFP3jHAJH0eDsoiGnLPBlBp4TNm6rYI74nMzgz3B9IikW4WVK+dc8KZJZWYjAuORU3jc1c/NPskD2ASinf8v3xnfXeukU0sJ5N6m5E8VLjObPEO+mN2t/FZTMZLiFqPWc/ALSqnMnnhwrNi2rbfg/rd/IpL8Le3pSBne8+seeFVBoGqzHM9yXw==
    github.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCj7ndNxQowgcQnjshcLrqPEiiphnt+VTTvDP6mHBL9j1aNUkY4Ue1gvwnGLVlOhGeYrnZaMgRK6+PKCUXaDbC7qtbW8gIkhL7aGCsOr/C56SJMy/BCZfxd1nWzAOxSDPgVsmerOBYfNqltV9/hWCqBywINIR+5dIg6JTJ72pcEpEjcYgXkE2YEFXV1JHnsKgbLWNlhScqb2UmyRkQyytRLtL+38TGxkxCflmO+5Z8CSSNY7GidjMIZ7Q4zMjA2n1nGrlTDkzwDCsw+wqFPGQA179cnfGWOWRVruj16z6XyvxvjJwbz0wQZ75XK5tKSb7FNyeIEs4TT4jk+S4dhPeAUC5y+bDYirYgM4GC7uEnztnZyaVWQ7B381AK4Qdrwt51ZqExKbQpTUNn+EjqoTwvqNj4kqx5QUCI0ThS/YkOxJCXmPUWZbhjpCg56i+2aB6CmK2JGhn57K5mj0MNdBXA4/WnwH6XoPWJzK5Nyu2zB3nAZp+S5hpQs+p1vN1/wsjk=
    gitlab.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBFSMqzJeV9rUzU4kWitGjeR4PWSa29SPqJ1fVkhtj3Hw9xjLVXVYrU9QlYWrOLXBpQ6KWjbjTDTdDkoohFzgbEY=
    gitlab.com ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAfuCHKVTjquxvt6CM6tdG4SLp1Btn/nOeHHE5UOzRdf
    gitlab.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCsj2bNKTBSpIYDEGk9KxsGh3mySTRgMtXL583qmBpzeQ+jqCMRgBqB98u3z++J1sKlXHWfM9dyhSevkMwSbhoR8XIq/U0tCNyokEi/ueaBMCvbcTHhO7FcwzY92WK4Yt0aGROY5qX2UKSeOvuP4D6TPqKF1onrSzH9bx9XUf2lEdWT/ia1NEKjunUqu1xOB/StKDHMoX4/OKyIzuS0q/T1zOATthvasJFoPrAjkohTyaDUz2LN5JoH839hViyEG82yB+MjcFV5MU3N1l1QL3cVUCh93xSaua1N85qivl+siMkPGbO5xR/En4iEY6K2XPASUEMaieWVNTRCtJ4S8H+9
    ssh.dev.azure.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC7Hr1oTWqNqOlzGJOfGJ4NakVyIzf1rXYd4d7wo6jBlkLvCA4odBlL0mDUyZ0/QUfTTqeu+tm22gOsv+VrVTMk6vwRU75gY/y9ut5Mb3bR5BV58dKXyq9A9UeB5Cakehn5Zgm6x1mKoVyf+FFn26iYqXJRgzIZZcZ5V6hrE0Qg39kZm4az48o0AUbf6Sp4SLdvnuMa2sVNwHBboS7EJkm57XQPVU3/QpyNLHbWDdzwtrlS+ez30S3AdYhLKEOxAG8weOnyrtLJAUen9mTkol8oII1edf7mWWbWVf0nBmly21+nZcmCTISQBtdcyPaEno7fFQMDD26/s0lfKob4Kw8H
    vs-ssh.visualstudio.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC7Hr1oTWqNqOlzGJOfGJ4NakVyIzf1rXYd4d7wo6jBlkLvCA4odBlL0mDUyZ0/QUfTTqeu+tm22gOsv+VrVTMk6vwRU75gY/y9ut5Mb3bR5BV58dKXyq9A9UeB5Cakehn5Zgm6x1mKoVyf+FFn26iYqXJRgzIZZcZ5V6hrE0Qg39kZm4az48o0AUbf6Sp4SLdvnuMa2sVNwHBboS7EJkm57XQPVU3/QpyNLHbWDdzwtrlS+ez30S3AdYhLKEOxAG8weOnyrtLJAUen9mTkol8oII1edf7mWWbWVf0nBmly21+nZcmCTISQBtdcyPaEno7fFQMDD26/s0lfKob4Kw8H
    github.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBEmKSENjQEezOmxkZMy7opKgwFB9nkt5YRrYMjNuG5N87uRgg6CLrbo5wAdT/y6v0mKV0U2w0WZ2YB/++Tpockg=
    github.com ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOMqqnkVzrm0SdG6UOoqKLsabgH5C9okWi0dh2l9GKJl
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-tls-certs-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-tls-certs-cm
    app.kubernetes.io/part-of: argocd
data:
  server.example.com: |
    -----BEGIN CERTIFICATE-----
    ...
    -----END CERTIFICATE-----
---
apiVersion: v1
kind: Secret
metadata:
  name: repo-argoproj-https-creds
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repo-creds
stringData:
  url: https://github.com/argoproj
  type: helm
  password: my-password
  username: my-username
---
apiVersion: v1
kind: Secret
metadata:
  name: repo-my-private-https-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
stringData:
  url: https://github.com/argoproj/argocd-example-apps
  password: my-password
  username: my-username
  insecure: "true"
  forceHttpBasicAuth: "true"
  enableLfs: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: repo-my-private-ssh-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
stringData:
  url: ssh://**************/argoproj/argocd-example-apps
  sshPrivateKey: |
    -----BEGIN OPENSSH PRIVATE KEY-----
    ...
    -----END OPENSSH PRIVATE KEY-----
  insecure: "true"
  enableLfs: "true"
---
apiVersion: argoproj.io/v1alpha1
kind: ConfigManagementPlugin
metadata:
  name: kasane
  annotations:
    akuity.io/image: "gcr.io/kasaneapp/kasane"
spec:
  version: v1.0
  init:
    command:
      - kasane
      - update
  generate:
    command:
      - kasane
      - show
---
apiVersion: argoproj.io/v1alpha1
kind: ConfigManagementPlugin
metadata:
  name: helm-kustomize
  annotations:
    akuity.io/enabled: "true"
    akuity.io/image: "gatsinski/kubectl-kustomize-helm"
spec:
  init:
    command:
      - sh
      - "-c"
    args:
      - helm dependency build
  generate:
    command:
      - sh
      - "-c"
    args:
      - helm template --release-name release-name . > all.yaml && kustomize build
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: helm-guestbook
spec:
  destination:
    namespace: helm-guestbook
    server: http://cluster-test:8001
  project: default
  source:
    path: helm-guestbook
    repoURL: https://github.com/argoproj/argocd-example-apps
    targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: guestbook
spec:
  generators:
    - list:
        elements:
          - cluster: test
            url: http://cluster-test:8001
          - cluster: stage
            url: http://cluster-stage:8001
  template:
    metadata:
      name: '{{cluster}}-guestbook'
    spec:
      project: default
      source:
        path: helm-guestbook
        repoURL: https://github.com/argoproj/argocd-example-apps
        targetRevision: HEAD
      destination:
        server: '{{url}}'
        namespace: guestbook
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: my-project
spec:
  description: Example Project
