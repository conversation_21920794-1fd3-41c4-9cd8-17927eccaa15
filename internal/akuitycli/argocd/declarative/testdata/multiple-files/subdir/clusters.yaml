apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: test-cluster-create
  namespace: test
  labels:
    test-label: "true"
  annotations:
    test-annotation: "false"
spec:
  namespaceScoped: true
  description: test-inst
  data:
    size: small
    autoUpgradeDisabled: true
    appReplication: true
    targetVersion: 0.3.30
    kustomization:
      apiVersion: kustomize.config.k8s.io/v1beta1
      kind: Kustomization
      resources:
        - test.yaml
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: test-cluster-update
  namespace: test
  labels:
    test-label: "true"
  annotations:
    test-annotation: "false"
spec:
  namespaceScoped: true
  description: test-inst
  data:
    size: small
    autoUpgradeDisabled: true
    appReplication: true
    targetVersion: 0.3.30
    kustomization:
      apiVersion: kustomize.config.k8s.io/v1beta1
      kind: Kustomization
      resources:
        - test.yaml
---
apiVersion: argocd.akuity.io/v1alpha1
kind: Cluster
metadata:
  name: kargo-test-integration
  namespace: akuity
spec:
  data:
    size: small
    directClusterSpec:
      clusterType: kargo
      kargoInstanceId: 'xyz'