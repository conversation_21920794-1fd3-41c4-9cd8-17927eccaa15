package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	runtimeutil "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/utils/ptr"

	agentclient "github.com/akuityio/agent/pkg/client"
	k8sErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/models/util/dal"
)

type ctrlContext struct{}

var ctrlContextKey = ctrlContext{}

func WithControllerContext(ctx context.Context, controller TableController) context.Context {
	return context.WithValue(ctx, ctrl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, controller)
}

func GetControllerFromContext(ctx context.Context) TableController {
	if controller, ok := ctx.Value(ctrlContextKey).(TableController); ok {
		return controller
	}
	return nil
}

type TableControllerOpts[T dal.ActiveRecord] func(t *tableController[T])

func WithReconciliationTimeout[T dal.ActiveRecord](reconciliationTimeout time.Duration) TableControllerOpts[T] {
	return func(t *tableController[T]) {
		t.reconciliationTimeout = reconciliationTimeout
	}
}

func WithResyncDuration[T dal.ActiveRecord](resyncDuration time.Duration) TableControllerOpts[T] {
	return func(t *tableController[T]) {
		t.resyncDuration = resyncDuration
	}
}

func WithEnqueueAllDelayFunc[T dal.ActiveRecord](enqueueAllDelay func() time.Duration) TableControllerOpts[T] {
	return func(t *tableController[T]) {
		t.enqueueAllDelay = enqueueAllDelay
	}
}

func WithEnqueueDelayFunc[T dal.ActiveRecord](enqueueDelay func(i, count int) time.Duration) TableControllerOpts[T] {
	return func(t *tableController[T]) {
		t.enqueueDelay = enqueueDelay
	}
}

func WithRequeueBackoff[T dal.ActiveRecord](requeueBackoff wait.Backoff) TableControllerOpts[T] {
	return func(t *tableController[T]) {
		t.requeueBackoff = requeueBackoff
	}
}

type Reconciler[T dal.ActiveRecord] interface {
	Reconcile(ctx context.Context, item T) error
	ItemToID(item T) string
	IDColumn() string
	LogValuesFromID(id string) []interface{}
	LogValuesFromItem(item T) []interface{}
}

type TableController interface {
	Start(ctx context.Context, numWorkers int) error
	Enqueue(id string, delay ...time.Duration)
}

// tableController implements the Controller backed by the database table and postgres channel.
type tableController[T dal.ActiveRecord] struct {
	repo                  dal.Repository[T]
	subscribe             func(ctx context.Context) <-chan string
	queue                 workqueue.TypedRateLimitingInterface[string]
	reconciler            Reconciler[T]
	log                   logr.Logger
	requeueBackoff        wait.Backoff
	resyncDuration        time.Duration
	reconciliationTimeout time.Duration
	backoffByKey          sync.Map
	processingByKey       sync.Map
	controllerName        string
	reconcileErrors       *prometheus.CounterVec
	enqueueHeartbeat      *prometheus.HistogramVec
	reconcileHeartbeat    *prometheus.HistogramVec
	enqueueAllDelay       func() time.Duration
	enqueueDelay          func(i, count int) time.Duration
}

func NewTableController[T dal.ActiveRecord](
	repo dal.Repository[T],
	log logr.Logger,
	controllerName string,
	subscribe func(ctx context.Context) <-chan string,
	reconciler Reconciler[T],
	reconcileErrors *prometheus.CounterVec,
	enqueueHeartbeat *prometheus.HistogramVec,
	reconcileHeartbeat *prometheus.HistogramVec,
	options ...TableControllerOpts[T],
) *tableController[T] {
	c := &tableController[T]{
		log:       log,
		repo:      repo,
		subscribe: subscribe,
		queue: workqueue.NewTypedRateLimitingQueueWithConfig[string](
			workqueue.DefaultTypedControllerRateLimiter[string](),
			workqueue.TypedRateLimitingQueueConfig[string]{Name: controllerName}),
		requeueBackoff:     wait.Backoff{Duration: time.Second, Factor: 2, Cap: time.Minute * 5, Steps: 9999},
		resyncDuration:     5 * time.Minute,
		reconciler:         reconciler,
		controllerName:     controllerName,
		reconcileErrors:    reconcileErrors,
		enqueueHeartbeat:   enqueueHeartbeat,
		reconcileHeartbeat: reconcileHeartbeat,
	}
	for _, option := range options {
		option(c)
	}
	return c
}

func (c *tableController[T]) Enqueue(id string, delay ...time.Duration) {
	// reset backoff when id is enqueued explicitly
	c.backoffByKey.Delete(id)
	if len(delay) > 0 {
		c.queue.AddAfter(id, delay[0])
	} else {
		c.queue.Add(id)
	}
}

func (c *tableController[T]) Start(ctx context.Context, numWorkers int) error {
	if err := c.enqueueAll(); err != nil {
		return err
	}

	ids := c.subscribe(ctx)

	go func() {
		for id := range ids {
			if _, hasBackoff := c.backoffByKey.Load(id); hasBackoff {
				continue
			}
			if _, inProgress := c.processingByKey.Load(id); inProgress {
				continue
			}
			c.queue.Add(id)
		}
	}()

	var ticker *time.Ticker
	if c.resyncDuration > 0 {
		ticker = time.NewTicker(c.resyncDuration)
		go func() {
			for range ticker.C {
				if c.enqueueAllDelay != nil {
					time.Sleep(c.enqueueAllDelay())
				}
				if err := c.enqueueAll(); err != nil {
					c.log.Error(err, fmt.Sprintf("enqueueAll completed: failed to resync table for controller '%s'", c.controllerName))
				}
			}
		}()
	}

	c.log.Info(fmt.Sprintf("Starting %d workers\n", numWorkers))
	for i := 0; i < numWorkers; i++ {
		go wait.Until(func() {
			c.runWorker()
		}, time.Second, ctx.Done())
	}
	go func() {
		<-ctx.Done()
		c.queue.ShutDown()
		if ticker != nil {
			ticker.Stop()
		}
	}()
	return nil
}

func (c *tableController[T]) enqueueAll() error {
	startTime := time.Now()
	c.log.Info("enqueueAll started", "controllerName", c.controllerName)
	defer func() {
		timeElapsed := time.Since(startTime)
		if c.enqueueHeartbeat != nil {
			c.enqueueHeartbeat.WithLabelValues(c.controllerName).
				Observe(timeElapsed.Seconds())
		}
		c.log.Info("enqueueAll completed",
			"controllerName", c.controllerName,
			"time_ms", timeElapsed.Milliseconds())
	}()

	ctx := context.Background()
	if c.reconciliationTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, c.reconciliationTimeout)
		defer cancel()
	}

	all, err := c.repo.ListAll(ctx, c.reconciler.IDColumn())
	if err != nil {
		if c.reconcileErrors != nil {
			c.reconcileErrors.WithLabelValues(c.controllerName).Inc()
		}
		return err
	}
	for i := range all {
		id := c.reconciler.ItemToID(all[i])
		if c.enqueueDelay != nil {
			c.backoffByKey.Delete(id)
			c.queue.AddAfter(id, c.enqueueDelay(i, len(all)))
		} else {
			c.Enqueue(id)
		}
	}
	return nil
}

func (c *tableController[T]) runWorker() {
	defer runtimeutil.HandleCrash()

	for c.processNextItem() {
	}
}

func (c *tableController[T]) processNextItem() bool {
	itemId, quit := c.queue.Get()
	defer func() {
		if r := recover(); r != nil {
			c.log.Error(fmt.Errorf("panic: %+v\n%s", r, debug.Stack()), "Recovered from panic")
		}
		c.queue.Done(itemId)
	}()
	log := c.log.WithValues(append(c.reconciler.LogValuesFromID(itemId), "controller", c.controllerName)...)

	startTime := time.Now()
	ctx := context.Background()
	if c.reconciliationTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, c.reconciliationTimeout)
		defer cancel()
	}
	c.processingByKey.Store(itemId, struct{}{})
	defer c.processingByKey.Delete(itemId)
	log.Info("Reconciling started")
	var logValues []interface{}
	var reconcileErr error
	var reconcileMessage string
	timer := agentclient.DisabledTimer

	if c.reconciliationTimeout > 0 {
		// Timer threshold is set to 80% of reconciliationTimeout so we get timer logs for slow operations only
		timer = agentclient.NewTimer(ptr.To(time.Duration(float64(c.reconciliationTimeout) * 0.8)))
		ctx = agentclient.AddTimerToContext(ctx, timer)
		timer.StartStep("Reconciler")
		defer timer.EndStep("Reconciler")
	}

	defer func() {
		elapsed := time.Since(startTime)
		if c.reconcileHeartbeat != nil {
			c.reconcileHeartbeat.WithLabelValues(c.controllerName).Observe(elapsed.Seconds())
		}
		logValues = append(logValues, "time_ms", elapsed.Milliseconds())
		logValues = append(logValues, timer.TimerSteps()...)
		reconcileMessage = fmt.Sprintf("Reconcile completed: %s", reconcileMessage)
		if reconcileErr != nil {
			if c.reconcileErrors != nil && !k8sErrors.IsRetryable(reconcileErr) {
				c.reconcileErrors.WithLabelValues(c.controllerName).Inc()
			}
			log.Error(reconcileErr, reconcileMessage, logValues...)
		} else {
			log.Info(reconcileMessage, logValues...)
		}
	}()

	timer.StartStep("Reconciler.GetByID")
	item, err := c.repo.GetByID(ctx, itemId)
	timer.EndStep("Reconciler.GetByID")

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.Info("Item not found, skipping")
			reconcileMessage = "Item not found in database, skipping reconciliation"
			err = nil
		}
	} else {
		logValues = append(logValues, c.reconciler.LogValuesFromItem(item)...)
		timer.StartStep("Reconciler.Reconcile")
		err = c.reconciler.Reconcile(logging.ContextWithLogger(WithControllerContext(ctx, c), log.WithValues(logValues...)), item)
		timer.EndStep("Reconciler.Reconcile")
	}

	if err != nil {
		if k8sErrors.IsRetryable(err) {
			nextRequeue := c.nextRequeueDuration(itemId)
			reconcileMessage = fmt.Sprintf("Retryable reconciliation error: %v. Retrying in %v...", err, nextRequeue)
			c.queue.AddAfter(itemId, nextRequeue)
		} else {
			reconcileErr = err
			reconcileMessage = "failed"
		}
	} else {
		c.backoffByKey.Delete(itemId)
		reconcileMessage = "success"
	}

	if quit {
		log.Info("Quit processing")
		return false
	}
	return true
}

func (c *tableController[T]) nextRequeueDuration(key interface{}) time.Duration {
	defaultBackoff := c.requeueBackoff
	backoff := &defaultBackoff
	if val, loaded := c.backoffByKey.LoadOrStore(key, backoff); loaded {
		backoff = val.(*wait.Backoff)
	}
	return backoff.Step()
}
