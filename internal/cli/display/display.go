package display

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"

	"github.com/fatih/color"
	"github.com/olekukonko/tablewriter"
	twrenderer "github.com/olekukonko/tablewriter/renderer"
	"github.com/olekukonko/tablewriter/tw"
	"google.golang.org/protobuf/proto"
	"sigs.k8s.io/yaml"

	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
)

type OutputFormat string

const (
	OutputFormatUndefined OutputFormat = ""
	OutputFormatJSON      OutputFormat = "json"
	OutputFormatWide      OutputFormat = "wide"
	OutputFormatYAML      OutputFormat = "yaml"
)

func ParseOutputFormat(v string) (OutputFormat, error) {
	switch OutputFormat(v) {
	case OutputFormatJSON:
		return OutputFormatJSON, nil
	case OutputFormatYAML:
		return OutputFormatYAML, nil
	case OutputFormatWide:
		return OutputFormatWide, nil
	default:
		return OutputFormatUndefined, fmt.Errorf("unknown output format: %q", v)
	}
}

type Result interface {
	TableHeader() []string
	ToTableRow() []string
	Data() interface{}
}

func mapResultData(results ...Result) []interface{} {
	data, _ := types.MapSlice(results, func(in Result) (interface{}, error) {
		return in.Data(), nil
	})
	return data
}

type Renderer interface {
	Newline()
	Infof(format string, a ...interface{})
	Warnf(format string, a ...interface{})
	Errorf(format string, a ...interface{})

	StringOutput(output string)
	Output(result Result)
	ListOutput(results ...Result)
}

type renderer struct {
	resultWriter  io.Writer
	messageWriter io.Writer

	format OutputFormat
}

func NewSystemRenderer(format OutputFormat) Renderer {
	return &renderer{
		resultWriter:  os.Stdout,
		messageWriter: os.Stderr,
		format:        format,
	}
}

func (r *renderer) Newline() {
	_, _ = fmt.Fprintln(r.messageWriter)
}

func (r *renderer) Infof(format string, a ...interface{}) {
	_, _ = color.New(color.FgGreen).Fprint(r.messageWriter, "▸ ")
	_, _ = fmt.Fprintf(r.messageWriter, format, a...)
}

func (r *renderer) Warnf(format string, a ...interface{}) {
	_, _ = color.New(color.FgYellow).Fprint(r.messageWriter, "▸ ")
	_, _ = fmt.Fprintf(r.messageWriter, format, a...)
}

func (r *renderer) Errorf(format string, a ...interface{}) {
	_, _ = color.New(color.FgRed).Fprint(r.messageWriter, "▸ ")
	_, _ = fmt.Fprintf(r.messageWriter, format, a...)
}

func (r *renderer) StringOutput(result string) {
	_, _ = fmt.Fprint(r.resultWriter, result)
}

func (r *renderer) Output(result Result) {
	switch r.format {
	case OutputFormatWide:
		r.wideOutput(result)
	case OutputFormatJSON:
		r.jsonOutput(result.Data())
	case OutputFormatYAML:
		r.yamlOutput(result.Data())
	default:
		r.Errorf("Unknown output format: %q", r.format)
	}
}

func (r *renderer) ListOutput(results ...Result) {
	switch r.format {
	case OutputFormatWide:
		r.wideOutput(results...)
	case OutputFormatJSON:
		r.jsonOutput(mapResultData(results...))
	case OutputFormatYAML:
		r.yamlOutput(mapResultData(results...))
	default:
		r.Errorf("Unknown output format: %q", r.format)
	}
}

func (r *renderer) wideOutput(results ...Result) {
	if len(results) == 0 {
		return
	}

	twrender := twrenderer.NewBlueprint(tw.Rendition{
		Borders: tw.Border{
			Left:   tw.Off,
			Right:  tw.Off,
			Top:    tw.Off,
			Bottom: tw.Off,
		},
		Settings: tw.Settings{
			Separators: tw.Separators{
				ShowHeader:     tw.Off,
				ShowFooter:     tw.Off,
				BetweenColumns: tw.Off,
				BetweenRows:    tw.Off,
			},
			Lines: tw.Lines{
				ShowHeaderLine: tw.Off,
				ShowFooterLine: tw.Off,
			},
		},
	})

	table := tablewriter.NewTable(r.resultWriter, tablewriter.WithRowAutoWrap(tw.WrapNormal), tablewriter.WithHeaderAutoFormat(tw.On), tablewriter.WithHeaderAlignment(tw.AlignLeft), tablewriter.WithRenderer(twrender))
	table.Header(results[0].TableHeader())

	for _, result := range results {
		if err := table.Append(result.ToTableRow()); err != nil {
			r.Errorf("Failed to append table row: %v", err)
		}
	}
	if err := table.Render(); err != nil {
		r.Errorf("Failed to render table: %v", err)
	}
}

func (r *renderer) jsonify(data interface{}) ([]byte, error) {
	if msg, ok := data.(proto.Message); ok {
		return gwoption.ClientMarshalOptions.Marshal(msg)
	}
	if list, ok := data.([]interface{}); ok {
		var results []interface{}
		for _, raw := range list {
			if msg, ok := raw.(proto.Message); ok {
				res, err := gwoption.ClientMarshalOptions.Marshal(msg)
				if err != nil {
					return nil, err
				}
				var unstructured map[string]interface{}
				if err := json.Unmarshal(res, &unstructured); err != nil {
					return nil, err
				}
				results = append(results, unstructured)
				continue
			}
			results = append(results, raw)
		}
		return json.MarshalIndent(results, "", "  ")
	}
	return json.MarshalIndent(data, "", "  ")
}

func (r *renderer) jsonOutput(data interface{}) {
	output, err := r.jsonify(data)
	if err != nil {
		r.failedToMarshalOutput(err)
		return
	}
	r.StringOutput(string(output))
}

func (r *renderer) yamlOutput(data interface{}) {
	jsonOutput, err := r.jsonify(data)
	if err != nil {
		r.failedToMarshalOutput(err)
		return
	}
	yamlOutput, err := yaml.JSONToYAML(jsonOutput)
	if err != nil {
		r.failedToMarshalOutput(err)
		return
	}
	r.StringOutput(string(yamlOutput))
}

func (r *renderer) failedToMarshalOutput(err error) {
	r.Errorf("Failed to marshal output: %v", err)
}

func NewBufferedRenderer(format OutputFormat) Renderer {
	return &renderer{
		resultWriter:  bytes.NewBufferString(""),
		messageWriter: bytes.NewBufferString(""),
		format:        format,
	}
}
