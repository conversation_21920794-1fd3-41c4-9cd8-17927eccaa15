package integration

import (
	"context"
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/client/apis/clusterupgrader"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	tenanttesting "github.com/akuityio/akuity-platform/controllers/shared/tenant/testing"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/models/client"
	testinutil "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
	modelsstatus "github.com/akuityio/akuity-platform/models/util/status"
	"github.com/akuityio/akuity-platform/test/utils"
)

const (
	connectionString = "user=postgres dbname=postgres host=localhost port=5432 sslmode=disable password=dbpassword"
	testInstanceId   = "4c7zr0dk4hvydra5"
)

func init() {
	utils.InitializeTestDataKey()
}

type fakeTenant struct {
	ReturnStatus agentclient.AggregatedHealthResponse

	ReceivedCplaneValues  *controlplane.DataValues
	ReceivedApplyOpts     *kube.ApplyOpts
	ReceivedClusterConfig *agentclient.ClusterConfig
}

func (f *fakeTenant) CertificateStatus(ctx context.Context) (*agentclient.CertificateStatus, error) {
	return &agentclient.CertificateStatus{}, nil
}

func (f *fakeTenant) ApplyCluster(ctx context.Context, clusterConfig agentclient.ClusterConfig) error {
	f.ReceivedClusterConfig = &clusterConfig
	return nil
}

func (f *fakeTenant) InitializeTenant(ctx context.Context, cplaneValues *controlplane.DataValues, applyOpts kube.ApplyOpts, enableK3sCertCNReset bool) error {
	return nil
}

func (f *fakeTenant) PruneTenant(ctx context.Context, generation uint64, dryRun bool) error {
	return nil
}

func (f *fakeTenant) PruneCluster(ctx context.Context, clusterName string, generation uint64, dryRun bool) error {
	return nil
}

func (f *fakeTenant) DeleteDirectCluster(ctx context.Context, name string) []error {
	return nil
}

func (f *fakeTenant) ApplyDirectCluster(ctx context.Context, config agentclient.DirectClusterConfig) error {
	return nil
}

func (f *fakeTenant) ControlPlaneExternalKubeConfig(ctx context.Context, fqdn string, inSecure bool) ([]byte, error) {
	return nil, nil
}

func (f *fakeTenant) Apply(_ context.Context, cplaneValues *controlplane.DataValues, applyOpts kube.ApplyOpts) error {
	f.ReceivedCplaneValues = cplaneValues
	f.ReceivedApplyOpts = &applyOpts
	return nil
}

func (f *fakeTenant) Status(_ context.Context, _, _ bool) (*agentclient.AggregatedHealthResponse, error) {
	return &f.ReturnStatus, nil
}

func (f *fakeTenant) NewTenant(string) (Tenant, error) {
	return f, nil
}

func (f *fakeTenant) Delete(ctx context.Context) error {
	return nil
}

func (f *fakeTenant) InitializeClusterData(ctx context.Context, config agentclient.ClusterConfig, agentValues *clusteragent.DataValues, upgraderValues *clusterupgrader.DataValues) error {
	return nil
}

func (f *fakeTenant) DeleteCluster(ctx context.Context, name, suffix string) []error {
	return nil
}

func (f *fakeTenant) GetClusterAgentDataValues(context.Context, string, string, bool, bool) (*clusteragent.DataValues, error) {
	return &clusteragent.DataValues{ArgoCd: &clusteragent.DataValuesArgoCd{}, ImageUpdater: &clusteragent.DataValuesImageUpdater{}}, nil
}

func (f *fakeTenant) DeleteClusterCredentials(ctx context.Context, name, oldSuffix, currentSuffix string, privileged bool) (bool, []error) {
	return false, nil
}

func fakeSQLOpen(db *sql.DB) func(_, _ string) (*sql.DB, error) {
	return func(_, _ string) (*sql.DB, error) {
		return db, nil
	}
}

const initTriggersStatementsCount = 5

func getImagePullSecret() (map[string][]byte, error) {
	return nil, nil
}

func newTestReconciler(t *testing.T, tenantPhase common.ArgoCDTenantPhase, sharedK3sDBConnectionAuth bool) (*argocdInstanceReconciler, client.RepoSet, *fakeTenant) {
	portalDB, portalDBMock, err := sqlmock.New()
	require.NoError(t, err)

	k3sDB, k3sDBMock, err := sqlmock.New()
	require.NoError(t, err)

	portalDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"first_instance_id", "clusters_count", "instances_count", "applications_count"}).AddRow(testInstanceId, 0, 1, 0))

	portalDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"applications_count"}).AddRow(0))

	k3sDBMock.ExpectExec(`CREATE SCHEMA IF NOT EXISTS instance_` + testInstanceId).WillReturnResult(sqlmock.NewResult(0, 0))
	k3sDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"id"}))

	k3sDBMock.ExpectExec(fmt.Sprintf(`
CREATE USER instance_%[1]s WITH PASSWORD .*;
GRANT USAGE ON SCHEMA instance_%[1]s to instance_%[1]s;
GRANT CREATE ON SCHEMA instance_%[1]s to instance_%[1]s;
`, testInstanceId)).WillReturnResult(sqlmock.NewResult(0, 0))

	if sharedK3sDBConnectionAuth {
		k3sDBMock.ExpectExec(fmt.Sprintf(`ALTER TABLE IF EXISTS instance_%[1]s.kine OWNER TO instance_%[1]s;`,
			testInstanceId)).
			WillReturnResult(sqlmock.NewResult(0, 0))
	}

	k3sDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"name"}))
	for i := 0; i < initTriggersStatementsCount; i++ {
		k3sDBMock.ExpectExec(`.*`).WillReturnResult(sqlmock.NewResult(0, 0))
	}

	repoSet := testinutil.NewInMemoryRepoSet()
	factory := fakeTenant{ReturnStatus: agentclient.AggregatedHealthResponse{PriorityStatus: tenantPhase}}

	repoSet.ArgoCDInstancesRepo.Items[testInstanceId] = &models.ArgoCDInstance{ID: testInstanceId, OrganizationOwner: testInstanceId}
	instanceConfig := &models.ArgoCDInstanceConfig{InstanceID: testInstanceId, Version: null.StringFrom("latest")}
	webhookKey, webhookCert, err := GetWebhookCert(testInstanceId, false)
	require.NoError(t, err)
	err = instanceConfig.SetPrivateSpec(models.InstanceConfigPrivateSpec{
		K3sUsername:   "instance_" + testInstanceId,
		K3sPassword:   "my-password",
		RedisPassword: "my-password",
		FqdnVersion:   "1",
		WebhookKey:    webhookKey,
		WebhookCert:   webhookCert,
	})
	require.NoError(t, err)
	repoSet.ArgoCDInstanceConfigsRepo.Items[testInstanceId] = instanceConfig
	repoSet.OrganizationsRepo.Items[testInstanceId] = &models.Organization{ID: testInstanceId}

	logger, err := logging.NewConsoleLogger()
	require.NoError(t, err)

	reconciler := NewArgoCDInstanceReconciler(&logger, &factory, ControllerSettings{
		InstanceSubDomains:        true,
		PortalDBRawClient:         portalDB,
		K3sDBRawClient:            k3sDB,
		RepoSet:                   repoSet,
		K3sDBConnection:           connectionString,
		SharedK3sDBConnectionAuth: sharedK3sDBConnectionAuth,
		DomainSuffix:              "akuity.cloud",
		InstanceConfig:            config.InstanceConfig{},
		IngressConfig:             config.IngressConfig{},
		Shard:                     "",
		PortalIPs:                 []string{},
	}, tenanttesting.FakeTenantStateClient{}, getImagePullSecret, 1*time.Hour, nil, nil, features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{}), []agentclient.ComponentVersion{{Version: "latest"}})
	reconciler.sqlOpen = fakeSQLOpen(k3sDB)
	return reconciler, repoSet, &factory
}

func TestReconcileSuccessful(t *testing.T) {
	reconciler, repoSet, factory := newTestReconciler(t, common.TenantPhaseHealthy, false)
	defer io.Close(reconciler)

	require.NoError(t, reconciler.Reconcile(context.Background(), &models.ArgoCDInstance{ID: testInstanceId, OrganizationOwner: testInstanceId}))

	instance, err := repoSet.ArgoCDInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)

	status, err := instance.GetStatus()
	require.NoError(t, err)
	assert.Equal(t, modelsstatus.HealthStatusCodeHealthy, status.Health.Code)

	config, err := repoSet.ArgoCDInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	privateSpec, err := config.GetPrivateSpec()
	require.NoError(t, err)
	assert.Equal(t, "instance_"+testInstanceId, privateSpec.K3sUsername)
	assert.Equal(t, "my-password", privateSpec.K3sPassword)
	assert.Equal(t, testInstanceId+"-agentsvr.cdsvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.AgentServer.Fqdn)
	assert.Equal(t, testInstanceId+"-cache.cdsvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.Redis.Fqdn)
	assert.Equal(t, testInstanceId+".cd.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.ArgoCd.Fqdn)
}

func TestReconcileProgressing(t *testing.T) {
	reconciler, repoSet, factory := newTestReconciler(t, common.TenantPhaseProgressing, false)
	defer io.Close(reconciler)

	require.NoError(t, reconciler.Reconcile(context.Background(), &models.ArgoCDInstance{ID: testInstanceId, OrganizationOwner: testInstanceId}))

	instance, err := repoSet.ArgoCDInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	status, err := instance.GetStatus()
	require.NoError(t, err)
	assert.False(t, status.Conditions.IsEstablished(models.InstanceConditionTypePruned), "not healthy tenant should not be pruned")

	assert.Equal(t, modelsstatus.HealthStatusCodeProgressing, status.Health.Code)

	config, err := repoSet.ArgoCDInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	privateSpec, err := config.GetPrivateSpec()
	require.NoError(t, err)
	assert.Equal(t, "instance_"+testInstanceId, privateSpec.K3sUsername)
	assert.NotEmpty(t, privateSpec.K3sPassword)
	assert.Equal(t, testInstanceId+"-agentsvr.cdsvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.AgentServer.Fqdn)
	assert.Equal(t, testInstanceId+"-cache.cdsvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.Redis.Fqdn)
	assert.Equal(t, testInstanceId+".cd.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.ArgoCd.Fqdn)
}

func TestArgoCDInstanceItemToID(t *testing.T) {
	reconciler, _, _ := newTestReconciler(t, common.TenantPhaseProgressing, false)
	defer io.Close(reconciler)

	id := reconciler.ItemToID(&models.ArgoCDInstance{ID: testInstanceId})
	assert.Equal(t, testInstanceId, id)
}
