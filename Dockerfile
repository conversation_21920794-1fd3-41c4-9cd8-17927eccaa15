####################################################################################################
# Global Args
####################################################################################################
ARG BASE_IMAGE=debian:12.11-slim

####################################################################################################
# Build UI
####################################################################################################
FROM --platform=$BUILDPLATFORM node:22.3.0-bullseye AS ui-builder

RUN npm install --global pnpm@9.3.0
WORKDIR /portal/ui
COPY ["portal/ui/package.json", "portal/ui/pnpm-lock.yaml", "./"]

RUN pnpm install

COPY ["portal/ui/", "."]

ARG VERSION
ENV VERSION=${VERSION}

ARG HUBSPOT_HUB_ID
ENV HUBSPOT_HUB_ID=${HUBSPOT_HUB_ID}

ARG SENTRY_TOKEN
ENV SENTRY_TOKEN=${SENTRY_TOKEN}

RUN NODE_ENV='production' SENTRY_TOKEN=${SENTRY_TOKEN} pnpm run build

####################################################################################################
# Build AIMS UI
####################################################################################################

WORKDIR /aims/ui
COPY ["aims/ui/package.json", "aims/ui/pnpm-lock.yaml", "./"]

RUN pnpm install --ignore-scripts

COPY ["aims/ui/", "."]

RUN VERSION=${VERSION} HUBSPOT_HUB_ID=${HUBSPOT_HUB_ID} NODE_ENV='production' pnpm run build

####################################################################################################
# Build Server and Platform Controller binaries
####################################################################################################
FROM --platform=$BUILDPLATFORM golang:1.24.5 AS builder

ARG GH_TOKEN
ENV GH_TOKEN=$GH_TOKEN

RUN printf "machine github.com\n\
    login ${GH_TOKEN}\n\
    password x-oauth-basic\n\
    \n\
    machine api.github.com\n\
    login ${GH_TOKEN}\n\
    password x-oauth-basic\n"\
    >> /root/.netrc && \
    chmod 600 /root/.netrc

WORKDIR /go/src/github.com/akuityio/akuity-platform

COPY go.mod go.sum ./
RUN go mod download

COPY . .

# Automatic platform ARGs in the global scope
# https://docs.docker.com/engine/reference/builder/#automatic-platform-args-in-the-global-scope
ARG TARGETOS
ENV GOOS=$TARGETOS
ARG TARGETARCH
ENV GOARCH=$TARGETARCH

ARG SELF_HOSTED=false
ENV SELF_HOSTED=$SELF_HOSTED

ARG VERSION
ENV VERSION=${VERSION}

RUN make binaries && \
    mkdir /ytt && K14SIO_INSTALL_BIN_DIR=/ytt ./hack/download-ytt-ci.sh 0.50.0 && \
    mkdir /kustomize && ./hack/download-kustomize.sh 5.4.3 /kustomize

####################################################################################################
# Final image
####################################################################################################
FROM ${BASE_IMAGE} AS akuity-platform

# Copy UI files for build
COPY --from=ui-builder /portal/ui/build /portal/ui/build
COPY --from=ui-builder /portal/ui/extensions-build /portal/ui/extensions-build
COPY --from=ui-builder /portal/ui/kargo-extensions-build /portal/ui/kargo-extensions-build

# Copy AIMS UI files for build
COPY --from=ui-builder /aims/ui/build /aims/ui/build

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /ytt/ytt /bin/
COPY --from=builder /kustomize/kustomize /bin/
COPY --from=builder /go/src/github.com/akuityio/akuity-platform/models/liquibase/changelogs /changelogs
COPY --from=builder /go/src/github.com/akuityio/akuity-platform/dist/akuity-platform /bin/
COPY --from=builder /go/src/github.com/akuityio/akuity-platform/dist/akputil /bin/

# https://akuityio.slack.com/archives/C02JBT9LL3Z/p1698175161912009
# "global-bundle.pem" is kept up-to-date with "generate-global-bundle" make target
# It is necessary if "sslmode=verify-ca" or "sslmode=verify-full" (with "sslrootcert") arguments are used when
# establishing a Postgres connection - https://www.postgresql.org/docs/15/libpq-ssl.html#LIBPQ-SSL-PROTECTION
# Akuity Platform is running in and connecting to Amazon Aurora in private AWS VPC and doesn't need to verify Postgres
# identity or protect itself against MITM attack. Therefore, it uses "sslmode=require" and doesn't require this file.
# However, Self-Hosted customers might run in a less secure environment and still prefer a Postgres connection string with
# stricter "sslmode" and "sslrootcert".
COPY global-bundle.pem /etc/ssl/certs/amazon-aurora-global-bundle.pem

USER 1000:0
