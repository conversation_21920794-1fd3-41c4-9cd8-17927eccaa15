package models

import (
	"encoding/json"

	"github.com/volatiletech/null/v8"
)

func (o *OrganizationKubevisionUsage) GetID() string {
	return o.ID
}

func (o *OrganizationKubevisionUsage) SetID(id string) {
	o.ID = id
}

func (o *OrganizationKubevisionUsage) GetAIUsage() (*AIUsage, error) {
	usage := &AIUsage{}
	if o == nil || o.AiUsage.IsZero() {
		return usage, nil
	}
	err := json.Unmarshal(o.AiUsage.JSON, usage)
	return usage, err
}

func (o *OrganizationKubevisionUsage) SetAIUsage(usage AIUsage) error {
	data, err := json.Marshal(usage)
	if err != nil {
		return err
	}
	o.AiUsage = null.JSONFrom(data)
	return nil
}
