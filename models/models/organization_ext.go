package models

import (
	"encoding/json"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/volatiletech/null/v8"

	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

type UserOrganization struct {
	Organization `boil:",bind"`
	Role         string `boil:"role"`
}

type MFASettings struct {
	Enabled bool `json:"enabled"`
}

type BillingState string

const (
	Trial        BillingState = "trial"
	PaidCustomer BillingState = "paid_customer"
	// DefaultOrgAICostMonthlyLimit is ~100$ per org per month for now
	DefaultOrgAICostMonthlyLimit = 100.0
)

func (o *Organization) GetID() string {
	return o.ID
}

func (o *Organization) SetID(id string) {
	o.ID = id
}

type OrgStatus struct {
	Trial                bool         `json:"trial"`
	ExpiryTime           int64        `json:"expiry_time"`
	State                BillingState `json:"state"`
	BillingUpdating      bool         `json:"billing_updating"`
	LastEventProcessedAt int64        `json:"last_event_processed_at"`
}

func (o *Organization) GetOrgStatus() (*OrgStatus, error) {
	status := &OrgStatus{}
	if o.OrgStatus.IsZero() {
		return status, nil
	}
	err := json.Unmarshal(o.OrgStatus.JSON, status)
	return status, err
}

func (o *Organization) SetOrgStatus(status OrgStatus) error {
	data, err := json.Marshal(status)
	if err != nil {
		return err
	}
	o.OrgStatus = null.JSONFrom(data)
	return nil
}

func (o *Organization) GetOrgFeatureGates() (*featuresv1.OrganizationFeatureGates, error) {
	var gates featuresv1.OrganizationFeatureGates
	if !o.FeatureGates.Valid {
		return &gates, nil
	}
	if err := json.Unmarshal(o.FeatureGates.JSON, &gates); err != nil {
		return nil, err
	}
	return &gates, nil
}

func (o *Organization) SetOrgFeatureGates(features *featuresv1.OrganizationFeatureGates) error {
	data, err := json.Marshal(features)
	if err != nil {
		return err
	}
	o.FeatureGates = null.JSONFrom(data)
	return nil
}

type OrgQuota struct {
	MaxInstances             int     `json:"max_instances"`
	MaxClusters              int     `json:"max_clusters"`
	MaxApplications          int     `json:"max_applications"`
	MaxKargoInstances        int     `json:"max_kargo_instances"`
	MaxKargoStages           int     `json:"max_kargo_stages"`
	MaxKargoAgents           int     `json:"max_kargo_agents"`
	AuditRecordMonths        int     `json:"audit_record_months"`
	AuditRecordArchiveMonths int     `json:"audit_record_archive_months"`
	MaxOrgMembers            int     `json:"max_org_members"`
	MaxWorkspaces            int     `json:"max_workspaces"`
	MaxAiCostPerMonth        float64 `json:"max_ai_cost_per_month"`

	// Deprecated: we no longer will limit projects rather, stages will be limited
	MaxKargoProjects int `json:"max_kargo_projects"`
}

func (o *Organization) GetOrgQuota() (*OrgQuota, error) {
	spec, err := o.GetSpec()
	if err != nil {
		return nil, err
	}
	res := &OrgQuota{
		MaxInstances:      o.MaxInstances,
		MaxClusters:       o.MaxClusters,
		MaxApplications:   o.MaxApplications,
		MaxKargoInstances: o.MaxKargoInstances,
		MaxKargoProjects:  o.MaxKargoProjects,
		MaxKargoAgents:    o.MaxKargoAgents,
		MaxWorkspaces:     o.MaxWorkspaces,
		MaxKargoStages:    o.MaxKargoStages,
	}
	if spec.AI.MaxCostPerMonth != nil {
		res.MaxAiCostPerMonth = *spec.AI.MaxCostPerMonth
	} else {
		res.MaxAiCostPerMonth = DefaultOrgAICostMonthlyLimit
	}
	return res, nil
}

func (o *Organization) GetOrgOIDCMap() (map[string]string, error) {
	data, err := json.Marshal(o.OidcMap)
	if err != nil {
		return nil, err
	}
	var oidcMap map[string]string
	if err := json.Unmarshal(data, &oidcMap); err != nil {
		return nil, err
	}
	return oidcMap, nil
}

func (o *Organization) SetOrgOIDCMap(oidcMap map[string]string) error {
	data, err := json.Marshal(oidcMap)
	if err != nil {
		return err
	}
	o.OidcMap = null.JSONFrom(data)
	return nil
}

func (o *Organization) GetOIDCTeamMap() (map[string]string, error) {
	data, err := json.Marshal(o.OidcTeamMap)
	if err != nil {
		return nil, err
	}
	var oidcMap map[string]string
	if err := json.Unmarshal(data, &oidcMap); err != nil {
		return nil, err
	}
	return oidcMap, nil
}

func (o *Organization) SetOIDCTeamMap(oidcMap map[string]string) error {
	data, err := json.Marshal(oidcMap)
	if err != nil {
		return err
	}
	o.OidcTeamMap = null.JSONFrom(data)
	return nil
}

func (o *Organization) GetPatch(updated *Organization) (map[string]interface{}, error) {
	originJSON, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	updatedJSON, err := json.Marshal(updated)
	if err != nil {
		return nil, err
	}
	patch, err := jsonpatch.CreateMergePatch(originJSON, updatedJSON)
	if err != nil {
		return nil, err
	}
	rv := map[string]interface{}{}
	if err := json.Unmarshal(patch, &rv); err != nil {
		return nil, err
	}
	return rv, nil
}

type VerifiedDomain struct {
	Verified bool   `json:"verified"`
	Domain   string `json:"domain"`
}

type VerifiedDomains []VerifiedDomain

func (vd VerifiedDomains) IsDomainVerified(domain string) bool {
	for _, d := range vd {
		if d.Domain == domain {
			return d.Verified
		}
	}

	return false
}

func (o *Organization) GetVerifiedDomains() (VerifiedDomains, error) {
	df := VerifiedDomains{}
	if o.VerifiedDomains.IsZero() {
		return df, nil
	}
	err := json.Unmarshal(o.VerifiedDomains.JSON, &df)
	return df, err
}

func (o *Organization) SetVerifiedDomains(df VerifiedDomains) error {
	data, err := json.Marshal(df)
	if err != nil {
		return err
	}
	o.VerifiedDomains = null.JSONFrom(data)
	return nil
}

func (o *Organization) GetMFASettings() (*MFASettings, error) {
	settings := &MFASettings{}

	if !o.MfaSettings.IsZero() {
		err := json.Unmarshal(o.MfaSettings.JSON, &settings)
		return settings, err
	}
	return settings, nil
}

func (o *Organization) SetMFASettings(settings MFASettings) error {
	data, err := json.Marshal(settings)
	if err != nil {
		return err
	}
	o.MfaSettings = null.JSONFrom(data)
	return nil
}

type OrganizationAISettings struct {
	Provider        string   `json:"provider"`
	MaxCostPerMonth *float64 `json:"max_cost_per_month"`
}

type InstanceQuota map[string]int32

type OrganizationSpec struct {
	AI                  OrganizationAISettings `json:"ai,omitempty"`
	ArgoCDInstanceQuota map[string]int32       `json:"argocd_instance_quota,omitempty"`
	KargoInstanceQuota  map[string]int32       `json:"kargo_instance_quota,omitempty"`
}

func (o *Organization) GetSpec() (*OrganizationSpec, error) {
	spec := &OrganizationSpec{}
	if o.Spec.IsZero() {
		return spec, nil
	}
	err := json.Unmarshal(o.Spec.JSON, spec)
	return spec, err
}

func (o *Organization) SetSpec(spec OrganizationSpec) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.Spec = null.JSONFrom(data)
	return nil
}

type AIUsage struct {
	InputTokens       int     `json:"input_tokens,omitempty"`
	CachedInputTokens int     `json:"cached_input_tokens,omitempty"`
	OutputTokens      int     `json:"output_tokens,omitempty"`
	Cost              float64 `json:"cost,omitempty"`
}

func (u AIUsage) Plus(u2 AIUsage) AIUsage {
	return AIUsage{
		InputTokens:       u.InputTokens + u2.InputTokens,
		CachedInputTokens: u.CachedInputTokens + u2.CachedInputTokens,
		OutputTokens:      u.OutputTokens + u2.OutputTokens,
		Cost:              u.Cost + u2.Cost,
	}
}

func (o *Organization) GetAIUsage() (*AIUsage, error) {
	usage := &AIUsage{}
	if o.AiUsage.IsZero() {
		return usage, nil
	}
	err := json.Unmarshal(o.AiUsage.JSON, usage)
	return usage, err
}

func (o *Organization) SetAIUsage(usage AIUsage) error {
	data, err := json.Marshal(usage)
	if err != nil {
		return err
	}
	o.AiUsage = null.JSONFrom(data)
	return nil
}
