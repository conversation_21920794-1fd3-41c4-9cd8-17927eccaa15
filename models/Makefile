# https://hub.docker.com/r/liquibase/liquibase/tags
# https://quay.io/repository/akuity/liquibase?tab=tags
# https://github.com/akuityio/agent/actions/workflows/retag-images.yaml => "Upstream Liquibase image"
LIQUIBASE_IMAGE=quay.io/akuity/liquibase:4.29

# https://hub.docker.com/_/postgres/tags
POSTGRES_IMAGE=postgres:17.2

# https://hub.docker.com/_/golang/tags
GO_IMAGE=golang:1.24.5

# https://github.com/volatiletech/sqlboiler/releases
SQLBOILER_VERSION=v4.16.2

install-toolchain:
	SQLBOILER_VERSION="${SQLBOILER_VERSION}" ./hack/install-toolchain.sh

.PHONY: setup-db
setup-db: tear-down-db
	docker network create portalDB
	docker run --rm -d --net=portalDB --name portalDB -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=dbpassword -e POSTGRES_DB=postgres "${POSTGRES_IMAGE}"
	docker run --rm -i --net=portalDB -e PGPASSWORD=dbpassword "${POSTGRES_IMAGE}" bash -c "until pg_isready --host portalDB --user postgres; do sleep 1; done"

.PHONY: tear-down-db
tear-down-db:
	(docker inspect portalDB > /dev/null 2>&1 && docker stop portalDB ) || echo "portalDB container not running"
	(docker network inspect portalDB > /dev/null 2>&1 && docker network rm portalDB) || echo "portalDB network does not exist"

SKIP_SETUP?=false
SKIP_TEAR_DOWN?=false
SKIP_LIQUIBASE?=false
DBSCHEMA?=public

.PHONY: generate-sql-local
generate-sql-local:
	liquibase --outputFile=sql/schema.sql --log-format=TEXT updateSQL

.PHONY: generate-sql
generate-sql:
	./hack/generate-sql.sh

.PHONY: generate-models-local
generate-models-local:
	PSQL_HOST=localhost $(GOPATH)/bin/sqlboiler $(GOPATH)/bin/sqlboiler-psql

.PHONY: generate-models
generate-models:
	./hack/generate-models.sh

.PHONY: generate-local
generate-local:
	make generate-sql-local
	liquibase update
	make generate-models-local

.PHONY: generate
generate:
	make setup-db
	LIQUIBASE_IMAGE="${LIQUIBASE_IMAGE}" SKIP_SETUP=true SKIP_TEAR_DOWN=true DBSCHEMA="${DBSCHEMA}" make generate-sql
	LIQUIBASE_IMAGE="${LIQUIBASE_IMAGE}" POSTGRES_IMAGE="${POSTGRES_IMAGE}" GO_IMAGE="${GO_IMAGE}" \
		SKIP_SETUP=true SKIP_TEAR_DOWN=true SKIP_LIQUIBASE=true DBSCHEMA="${DBSCHEMA}" make generate-models
	make tear-down-db
