// Code generated by protoc-gen-grpc-gateway-client. DO NOT EDIT.
// source: argocd/v1/argocd.proto

package argocdv1

import (
	context "context"
	fmt "fmt"
	gateway "github.com/akuity/grpc-gateway-client/pkg/grpc/gateway"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	url "net/url"
)

// ArgoCDServiceGatewayClient is the interface for ArgoCDService service client.
type ArgoCDServiceGatewayClient interface {
	ListInstanceVersions(context.Context, *ListInstanceVersionsRequest) (*ListInstanceVersionsResponse, error)
	ListInstances(context.Context, *ListInstancesRequest) (*ListInstancesResponse, error)
	WatchInstances(context.Context, *WatchInstancesRequest) (<-chan *WatchInstancesResponse, <-chan error, error)
	CreateInstance(context.Context, *CreateInstanceRequest) (*CreateInstanceResponse, error)
	GetInstance(context.Context, *GetInstanceRequest) (*GetInstanceResponse, error)
	GetInstanceCSS(context.Context, *GetInstanceCSSRequest) (*GetInstanceCSSResponse, error)
	GetInstanceNotificationSettings(context.Context, *GetInstanceNotificationSettingsRequest) (*GetInstanceNotificationSettingsResponse, error)
	GetInstanceNotificationCatalog(context.Context, *GetInstanceNotificationCatalogRequest) (*GetInstanceNotificationCatalogResponse, error)
	GetInstanceImageUpdaterSettings(context.Context, *GetInstanceImageUpdaterSettingsRequest) (*GetInstanceImageUpdaterSettingsResponse, error)
	GetInstanceResourceCustomizations(context.Context, *GetInstanceResourceCustomizationsRequest) (*GetInstanceResourceCustomizationsResponse, error)
	GetInstanceConfigManagementPlugins(context.Context, *GetInstanceConfigManagementPluginsRequest) (*GetInstanceConfigManagementPluginsResponse, error)
	PatchInstance(context.Context, *PatchInstanceRequest) (*PatchInstanceResponse, error)
	PatchInstanceSecret(context.Context, *PatchInstanceSecretRequest) (*PatchInstanceSecretResponse, error)
	PatchInstanceNotificationSecret(context.Context, *PatchInstanceNotificationSecretRequest) (*PatchInstanceNotificationSecretResponse, error)
	PatchInstanceImageUpdaterSecret(context.Context, *PatchInstanceImageUpdaterSecretRequest) (*PatchInstanceImageUpdaterSecretResponse, error)
	GetInstanceAppsetSecret(context.Context, *GetInstanceAppsetSecretRequest) (*GetInstanceAppsetSecretResponse, error)
	PatchInstanceAppsetSecret(context.Context, *PatchInstanceAppsetSecretRequest) (*PatchInstanceAppsetSecretResponse, error)
	UpdateInstance(context.Context, *UpdateInstanceRequest) (*UpdateInstanceResponse, error)
	UpdateInstanceWorkspace(context.Context, *UpdateInstanceWorkspaceRequest) (*UpdateInstanceWorkspaceResponse, error)
	UpdateInstanceCSS(context.Context, *UpdateInstanceCSSRequest) (*UpdateInstanceCSSResponse, error)
	UpdateInstanceNotificationConfig(context.Context, *UpdateInstanceNotificationConfigRequest) (*UpdateInstanceNotificationConfigResponse, error)
	UpdateInstanceImageUpdaterConfig(context.Context, *UpdateInstanceImageUpdaterConfigRequest) (*UpdateInstanceImageUpdaterConfigResponse, error)
	UpdateInstanceImageUpdaterSSHConfig(context.Context, *UpdateInstanceImageUpdaterSSHConfigRequest) (*UpdateInstanceImageUpdaterSSHConfigResponse, error)
	UpdateInstanceResourceCustomizations(context.Context, *UpdateInstanceResourceCustomizationsRequest) (*UpdateInstanceResourceCustomizationsResponse, error)
	UpdateInstanceConfigManagementPlugins(context.Context, *UpdateInstanceConfigManagementPluginsRequest) (*UpdateInstanceConfigManagementPluginsResponse, error)
	DeleteInstance(context.Context, *DeleteInstanceRequest) (*DeleteInstanceResponse, error)
	ListInstanceAccounts(context.Context, *ListInstanceAccountsRequest) (*ListInstanceAccountsResponse, error)
	UpsertInstanceAccount(context.Context, *UpsertInstanceAccountRequest) (*UpsertInstanceAccountResponse, error)
	UpdateInstanceAccountPassword(context.Context, *UpdateInstanceAccountPasswordRequest) (*UpdateInstanceAccountPasswordResponse, error)
	RegenerateInstanceAccountPassword(context.Context, *RegenerateInstanceAccountPasswordRequest) (*RegenerateInstanceAccountPasswordResponse, error)
	DeleteInstanceAccount(context.Context, *DeleteInstanceAccountRequest) (*DeleteInstanceAccountResponse, error)
	ListInstanceClusters(context.Context, *ListInstanceClustersRequest) (*ListInstanceClustersResponse, error)
	WatchInstanceClusters(context.Context, *WatchInstanceClustersRequest) (<-chan *WatchInstanceClustersResponse, <-chan error, error)
	CreateInstanceCluster(context.Context, *CreateInstanceClusterRequest) (*CreateInstanceClusterResponse, error)
	GetClusterAPIServerCAData(context.Context, *GetClusterAPIServerCADataRequest) (*GetClusterAPIServerCADataResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceCluster(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterResponse, error)
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceClusterInfo(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterInfoResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetInstanceClusterManifests(context.Context, *GetInstanceClusterManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	UpdateInstanceCluster(context.Context, *UpdateInstanceClusterRequest) (*UpdateInstanceClusterResponse, error)
	UpdateInstanceClusters(context.Context, *UpdateInstanceClustersRequest) (*UpdateInstanceClustersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	UpdateInstanceClustersAgentVersion(context.Context, *UpdateInstanceClustersAgentVersionRequest) (*emptypb.Empty, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	RotateInstanceClusterCredentials(context.Context, *RotateInstanceClusterCredentialsRequest) (*RotateInstanceClusterCredentialsResponse, error)
	DeleteInstanceCluster(context.Context, *DeleteInstanceClusterRequest) (*DeleteInstanceClusterResponse, error)
	GetInstanceClusterCommand(context.Context, *GetInstanceClusterCommandRequest) (*GetInstanceClusterCommandResponse, error)
	GetAIAssistantUsageStats(context.Context, *GetAIAssistantUsageStatsRequest) (*GetAIAssistantUsageStatsResponse, error)
	GetSyncOperationsStats(context.Context, *GetSyncOperationsStatsRequest) (*GetSyncOperationsStatsResponse, error)
	GetSyncOperationsEvents(context.Context, *GetSyncOperationsEventsRequest) (*GetSyncOperationsEventsResponse, error)
	ApplyInstance(context.Context, *ApplyInstanceRequest) (*ApplyInstanceResponse, error)
	ExportInstance(context.Context, *ExportInstanceRequest) (*ExportInstanceResponse, error)
	ListInstanceAddonRepos(context.Context, *ListInstanceAddonReposRequest) (*ListInstanceAddonReposResponse, error)
	GetInstanceAddonRepo(context.Context, *GetInstanceAddonRepoRequest) (*GetInstanceAddonRepoResponse, error)
	CreateInstanceAddonRepo(context.Context, *CreateInstanceAddonRepoRequest) (*CreateInstanceAddonRepoResponse, error)
	RefreshInstanceAddonRepo(context.Context, *RefreshInstanceAddonRepoRequest) (*RefreshInstanceAddonRepoResponse, error)
	DeleteInstanceAddonRepo(context.Context, *DeleteInstanceAddonRepoRequest) (*DeleteInstanceAddonRepoResponse, error)
	ListInstanceAddons(context.Context, *ListInstanceAddonsRequest) (*ListInstanceAddonsResponse, error)
	ListInstanceAddonErrors(context.Context, *ListInstanceAddonErrorsRequest) (*ListInstanceAddonErrorsResponse, error)
	GetInstanceAddon(context.Context, *GetInstanceAddonRequest) (*GetInstanceAddonResponse, error)
	DeleteInstanceAddon(context.Context, *DeleteInstanceAddonRequest) (*DeleteInstanceAddonResponse, error)
	RefreshInstanceAddon(context.Context, *RefreshInstanceAddonRequest) (*RefreshInstanceAddonResponse, error)
	UpdateInstanceAddon(context.Context, *UpdateInstanceAddonRequest) (*UpdateInstanceAddonResponse, error)
	PatchInstanceAddon(context.Context, *PatchInstanceAddonRequest) (*PatchInstanceAddonResponse, error)
	ClearAddonStatusSourceHistory(context.Context, *ClearAddonStatusSourceHistoryRequest) (*ClearAddonStatusSourceHistoryResponse, error)
	WatchInstanceAddons(context.Context, *WatchInstanceAddonsRequest) (<-chan *WatchInstanceAddonsResponse, <-chan error, error)
	WatchInstanceAddonRepos(context.Context, *WatchInstanceAddonReposRequest) (<-chan *WatchInstanceAddonReposResponse, <-chan error, error)
	AddonMarketplaceInstall(context.Context, *AddonMarketplaceInstallRequest) (*AddonMarketplaceInstallResponse, error)
	ListAddonMarketplaceInstalls(context.Context, *ListAddonMarketplaceInstallsRequest) (*ListAddonMarketplaceInstallsResponse, error)
	WatchAddonMarketplaceInstalls(context.Context, *WatchAddonMarketplaceInstallsRequest) (<-chan *WatchAddonMarketplaceInstallsResponse, <-chan error, error)
	UpdateAddonMarketplaceInstall(context.Context, *UpdateAddonMarketplaceInstallRequest) (*UpdateAddonMarketplaceInstallResponse, error)
	ListInstanceRepos(context.Context, *ListInstanceReposRequest) (*ListInstanceReposResponse, error)
	CreateInstanceRepo(context.Context, *CreateInstanceRepoRequest) (*CreateInstanceRepoResponse, error)
	DeleteAddonMarketplaceInstall(context.Context, *DeleteAddonMarketplaceInstallRequest) (*DeleteAddonMarketplaceInstallResponse, error)
	ListInstanceManagedSecrets(context.Context, *ListInstanceManagedSecretsRequest) (*ListInstanceManagedSecretsResponse, error)
	CreateManagedSecret(context.Context, *CreateManagedSecretRequest) (*CreateManagedSecretResponse, error)
	DeleteManagedSecret(context.Context, *DeleteManagedSecretRequest) (*DeleteManagedSecretResponse, error)
	UpdateManagedSecret(context.Context, *UpdateManagedSecretRequest) (*UpdateManagedSecretResponse, error)
	// PatchManagedSecret updates the current metadata information (i.e. access permissions and
	// labels) for the secret without modifying the actual secret data. If this is purely managed on
	// the control plane, all labels will overwrite the current labels. However, if this secret is
	// managed by another cluster, an error will be returned if any labels are set
	PatchManagedSecret(context.Context, *PatchManagedSecretRequest) (*PatchManagedSecretResponse, error)
}

func NewArgoCDServiceGatewayClient(c gateway.Client) ArgoCDServiceGatewayClient {
	return &argoCDServiceGatewayClient{
		gwc: c,
	}
}

type argoCDServiceGatewayClient struct {
	gwc gateway.Client
}

func (c *argoCDServiceGatewayClient) ListInstanceVersions(ctx context.Context, req *ListInstanceVersionsRequest) (*ListInstanceVersionsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/argocd/versions")
	return gateway.DoRequest[ListInstanceVersionsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstances(ctx context.Context, req *ListInstancesRequest) (*ListInstancesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstancesResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) WatchInstances(ctx context.Context, req *WatchInstancesRequest) (<-chan *WatchInstancesResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/argocd/instances")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[WatchInstancesResponse](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) CreateInstance(ctx context.Context, req *CreateInstanceRequest) (*CreateInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstance(ctx context.Context, req *GetInstanceRequest) (*GetInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("idType", req.IdType.String())
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceCSS(ctx context.Context, req *GetInstanceCSSRequest) (*GetInstanceCSSResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/css")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceCSSResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceNotificationSettings(ctx context.Context, req *GetInstanceNotificationSettingsRequest) (*GetInstanceNotificationSettingsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/notifications")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceNotificationSettingsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceNotificationCatalog(ctx context.Context, req *GetInstanceNotificationCatalogRequest) (*GetInstanceNotificationCatalogResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/notifications/catalog")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceNotificationCatalogResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceImageUpdaterSettings(ctx context.Context, req *GetInstanceImageUpdaterSettingsRequest) (*GetInstanceImageUpdaterSettingsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/image-updater")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceImageUpdaterSettingsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceResourceCustomizations(ctx context.Context, req *GetInstanceResourceCustomizationsRequest) (*GetInstanceResourceCustomizationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/resource-customizations")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceResourceCustomizationsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceConfigManagementPlugins(ctx context.Context, req *GetInstanceConfigManagementPluginsRequest) (*GetInstanceConfigManagementPluginsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/config-management-plugins")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceConfigManagementPluginsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstance(ctx context.Context, req *PatchInstanceRequest) (*PatchInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/argocd/instances/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req.Patch)
	return gateway.DoRequest[PatchInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstanceSecret(ctx context.Context, req *PatchInstanceSecretRequest) (*PatchInstanceSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/secret")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchInstanceSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstanceNotificationSecret(ctx context.Context, req *PatchInstanceNotificationSecretRequest) (*PatchInstanceNotificationSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/notifications/secret")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchInstanceNotificationSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstanceImageUpdaterSecret(ctx context.Context, req *PatchInstanceImageUpdaterSecretRequest) (*PatchInstanceImageUpdaterSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/image-updater/secret")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchInstanceImageUpdaterSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceAppsetSecret(ctx context.Context, req *GetInstanceAppsetSecretRequest) (*GetInstanceAppsetSecretResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/appset/secret")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceAppsetSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstanceAppsetSecret(ctx context.Context, req *PatchInstanceAppsetSecretRequest) (*PatchInstanceAppsetSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/appset/secret")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchInstanceAppsetSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstance(ctx context.Context, req *UpdateInstanceRequest) (*UpdateInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceWorkspace(ctx context.Context, req *UpdateInstanceWorkspaceRequest) (*UpdateInstanceWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/transfer")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceWorkspaceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceCSS(ctx context.Context, req *UpdateInstanceCSSRequest) (*UpdateInstanceCSSResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/css")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceCSSResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceNotificationConfig(ctx context.Context, req *UpdateInstanceNotificationConfigRequest) (*UpdateInstanceNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/notifications")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceNotificationConfigResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceImageUpdaterConfig(ctx context.Context, req *UpdateInstanceImageUpdaterConfigRequest) (*UpdateInstanceImageUpdaterConfigResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/image-updater/config")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceImageUpdaterConfigResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceImageUpdaterSSHConfig(ctx context.Context, req *UpdateInstanceImageUpdaterSSHConfigRequest) (*UpdateInstanceImageUpdaterSSHConfigResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/image-updater/ssh-config")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceImageUpdaterSSHConfigResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceResourceCustomizations(ctx context.Context, req *UpdateInstanceResourceCustomizationsRequest) (*UpdateInstanceResourceCustomizationsResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/resource-customizations")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceResourceCustomizationsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceConfigManagementPlugins(ctx context.Context, req *UpdateInstanceConfigManagementPluginsRequest) (*UpdateInstanceConfigManagementPluginsResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/config-management-plugins")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceConfigManagementPluginsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteInstance(ctx context.Context, req *DeleteInstanceRequest) (*DeleteInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/argocd/instances/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceAccounts(ctx context.Context, req *ListInstanceAccountsRequest) (*ListInstanceAccountsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/accounts")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstanceAccountsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpsertInstanceAccount(ctx context.Context, req *UpsertInstanceAccountRequest) (*UpsertInstanceAccountResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/accounts/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpsertInstanceAccountResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceAccountPassword(ctx context.Context, req *UpdateInstanceAccountPasswordRequest) (*UpdateInstanceAccountPasswordResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/accounts/{name}/password")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceAccountPasswordResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) RegenerateInstanceAccountPassword(ctx context.Context, req *RegenerateInstanceAccountPasswordRequest) (*RegenerateInstanceAccountPasswordResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/accounts/{name}/regenerate-password")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[RegenerateInstanceAccountPasswordResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteInstanceAccount(ctx context.Context, req *DeleteInstanceAccountRequest) (*DeleteInstanceAccountResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/accounts/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteInstanceAccountResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceClusters(ctx context.Context, req *ListInstanceClustersRequest) (*ListInstanceClustersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	if req.Filter != nil {
		if req.Filter.NameLike != nil {
			q.Add("filter.nameLike", fmt.Sprintf("%v", *req.Filter.NameLike))
		}
		for _, v := range req.Filter.AgentStatus {
			q.Add("filter.agentStatus", v.String())
		}
		for _, v := range req.Filter.AgentVersion {
			q.Add("filter.agentVersion", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filter.ArgocdVersion {
			q.Add("filter.argocdVersion", fmt.Sprintf("%v", v))
		}
		if req.Filter.Limit != nil {
			q.Add("filter.limit", fmt.Sprintf("%v", *req.Filter.Limit))
		}
		if req.Filter.Offset != nil {
			q.Add("filter.offset", fmt.Sprintf("%v", *req.Filter.Offset))
		}
		if req.Filter.ExcludeAgentVersion != nil {
			q.Add("filter.excludeAgentVersion", fmt.Sprintf("%v", *req.Filter.ExcludeAgentVersion))
		}
		if req.Filter.OutdatedManifest != nil {
			q.Add("filter.outdatedManifest", fmt.Sprintf("%v", *req.Filter.OutdatedManifest))
		}
		for _, v := range req.Filter.Namespace {
			q.Add("filter.namespace", fmt.Sprintf("%v", v))
		}
		if req.Filter.NamespaceScoped != nil {
			q.Add("filter.namespaceScoped", fmt.Sprintf("%v", *req.Filter.NamespaceScoped))
		}
		for k, v := range req.Filter.Labels {
			key := fmt.Sprintf("filter.labels[%v]", k)
			q.Add(key, fmt.Sprintf("%v", v))
		}
		if req.Filter.NeedReapply != nil {
			q.Add("filter.needReapply", fmt.Sprintf("%v", *req.Filter.NeedReapply))
		}
		if req.Filter.ExcludeDirectCluster != nil {
			q.Add("filter.excludeDirectCluster", fmt.Sprintf("%v", *req.Filter.ExcludeDirectCluster))
		}
	}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstanceClustersResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) WatchInstanceClusters(ctx context.Context, req *WatchInstanceClustersRequest) (<-chan *WatchInstanceClustersResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/argocd/instances/{instance_id}/clusters")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	if req.ClusterId != nil {
		q.Add("clusterId", fmt.Sprintf("%v", *req.ClusterId))
	}
	if req.MinClusterName != nil {
		q.Add("minClusterName", fmt.Sprintf("%v", *req.MinClusterName))
	}
	if req.MaxClusterName != nil {
		q.Add("maxClusterName", fmt.Sprintf("%v", *req.MaxClusterName))
	}
	if req.Filter != nil {
		if req.Filter.NameLike != nil {
			q.Add("filter.nameLike", fmt.Sprintf("%v", *req.Filter.NameLike))
		}
		for _, v := range req.Filter.AgentStatus {
			q.Add("filter.agentStatus", v.String())
		}
		for _, v := range req.Filter.AgentVersion {
			q.Add("filter.agentVersion", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filter.ArgocdVersion {
			q.Add("filter.argocdVersion", fmt.Sprintf("%v", v))
		}
		if req.Filter.Limit != nil {
			q.Add("filter.limit", fmt.Sprintf("%v", *req.Filter.Limit))
		}
		if req.Filter.Offset != nil {
			q.Add("filter.offset", fmt.Sprintf("%v", *req.Filter.Offset))
		}
		if req.Filter.ExcludeAgentVersion != nil {
			q.Add("filter.excludeAgentVersion", fmt.Sprintf("%v", *req.Filter.ExcludeAgentVersion))
		}
		if req.Filter.OutdatedManifest != nil {
			q.Add("filter.outdatedManifest", fmt.Sprintf("%v", *req.Filter.OutdatedManifest))
		}
		for _, v := range req.Filter.Namespace {
			q.Add("filter.namespace", fmt.Sprintf("%v", v))
		}
		if req.Filter.NamespaceScoped != nil {
			q.Add("filter.namespaceScoped", fmt.Sprintf("%v", *req.Filter.NamespaceScoped))
		}
		for k, v := range req.Filter.Labels {
			key := fmt.Sprintf("filter.labels[%v]", k)
			q.Add(key, fmt.Sprintf("%v", v))
		}
		if req.Filter.NeedReapply != nil {
			q.Add("filter.needReapply", fmt.Sprintf("%v", *req.Filter.NeedReapply))
		}
		if req.Filter.ExcludeDirectCluster != nil {
			q.Add("filter.excludeDirectCluster", fmt.Sprintf("%v", *req.Filter.ExcludeDirectCluster))
		}
	}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[WatchInstanceClustersResponse](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) CreateInstanceCluster(ctx context.Context, req *CreateInstanceClusterRequest) (*CreateInstanceClusterResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateInstanceClusterResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetClusterAPIServerCAData(ctx context.Context, req *GetClusterAPIServerCADataRequest) (*GetClusterAPIServerCADataResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/utils/cluster-ca-data")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterName", fmt.Sprintf("%v", req.ClusterName))
	q.Add("server", fmt.Sprintf("%v", req.Server))
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetClusterAPIServerCADataResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceCluster(ctx context.Context, req *GetInstanceClusterRequest) (*GetInstanceClusterResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("idType", req.IdType.String())
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceClusterResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceClusterInfo(ctx context.Context, req *GetInstanceClusterRequest) (*GetInstanceClusterInfoResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}/info")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("idType", req.IdType.String())
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceClusterInfoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceClusterManifests(ctx context.Context, req *GetInstanceClusterManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}/manifests")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("offlineInstallation", fmt.Sprintf("%v", req.OfflineInstallation))
	if req.SkipNamespace != nil {
		q.Add("skipNamespace", fmt.Sprintf("%v", *req.SkipNamespace))
	}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceCluster(ctx context.Context, req *UpdateInstanceClusterRequest) (*UpdateInstanceClusterResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceClusterResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceClusters(ctx context.Context, req *UpdateInstanceClustersRequest) (*UpdateInstanceClustersResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/clusters")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceClustersResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceClustersAgentVersion(ctx context.Context, req *UpdateInstanceClustersAgentVersionRequest) (*emptypb.Empty, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/agent-version")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[emptypb.Empty](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) RotateInstanceClusterCredentials(ctx context.Context, req *RotateInstanceClusterCredentialsRequest) (*RotateInstanceClusterCredentialsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/rotate-credentials")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[RotateInstanceClusterCredentialsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteInstanceCluster(ctx context.Context, req *DeleteInstanceClusterRequest) (*DeleteInstanceClusterResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteInstanceClusterResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceClusterCommand(ctx context.Context, req *GetInstanceClusterCommandRequest) (*GetInstanceClusterCommandResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{instance_id}/clusters/{id}/command")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("locationOrigin", fmt.Sprintf("%v", req.LocationOrigin))
	q.Add("offline", fmt.Sprintf("%v", req.Offline))
	q.Add("type", fmt.Sprintf("%v", req.Type))
	if req.SkipNamespace != nil {
		q.Add("skipNamespace", fmt.Sprintf("%v", *req.SkipNamespace))
	}
	if req.CommandFor != nil {
		q.Add("commandFor", req.CommandFor.String())
	}
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceClusterCommandResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetAIAssistantUsageStats(ctx context.Context, req *GetAIAssistantUsageStatsRequest) (*GetAIAssistantUsageStatsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/ai-assistant-usage-stats")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[GetAIAssistantUsageStatsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetSyncOperationsStats(ctx context.Context, req *GetSyncOperationsStatsRequest) (*GetSyncOperationsStatsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/sync-operations-stats")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[GetSyncOperationsStatsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetSyncOperationsEvents(ctx context.Context, req *GetSyncOperationsEventsRequest) (*GetSyncOperationsEventsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/sync-operations-events")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[GetSyncOperationsEventsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ApplyInstance(ctx context.Context, req *ApplyInstanceRequest) (*ApplyInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/apply")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[ApplyInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ExportInstance(ctx context.Context, req *ExportInstanceRequest) (*ExportInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/argocd/instances/{id}/export")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("idType", req.IdType.String())
	q.Add("workspaceId", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ExportInstanceResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceAddonRepos(ctx context.Context, req *ListInstanceAddonReposRequest) (*ListInstanceAddonReposResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstanceAddonReposResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceAddonRepo(ctx context.Context, req *GetInstanceAddonRepoRequest) (*GetInstanceAddonRepoResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetInstanceAddonRepoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) CreateInstanceAddonRepo(ctx context.Context, req *CreateInstanceAddonRepoRequest) (*CreateInstanceAddonRepoResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateInstanceAddonRepoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) RefreshInstanceAddonRepo(ctx context.Context, req *RefreshInstanceAddonRepoRequest) (*RefreshInstanceAddonRepoResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos/{id}/refresh")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[RefreshInstanceAddonRepoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteInstanceAddonRepo(ctx context.Context, req *DeleteInstanceAddonRepoRequest) (*DeleteInstanceAddonRepoResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteInstanceAddonRepoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceAddons(ctx context.Context, req *ListInstanceAddonsRequest) (*ListInstanceAddonsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.Filter != nil {
		if req.Filter.SortBy != nil {
			q.Add("filter.sortBy", req.Filter.SortBy.String())
		}
		if req.Filter.Enabled != nil {
			q.Add("filter.enabled", fmt.Sprintf("%v", *req.Filter.Enabled))
		}
		if req.Filter.Name != nil {
			q.Add("filter.name", fmt.Sprintf("%v", *req.Filter.Name))
		}
		if req.Filter.AddonType != nil {
			q.Add("filter.addonType", req.Filter.AddonType.String())
		}
		if req.Filter.ClusterNameLike != nil {
			q.Add("filter.clusterNameLike", fmt.Sprintf("%v", *req.Filter.ClusterNameLike))
		}
		for k, v := range req.Filter.ClusterLabels {
			key := fmt.Sprintf("filter.clusterLabels[%v]", k)
			q.Add(key, fmt.Sprintf("%v", v))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstanceAddonsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceAddonErrors(ctx context.Context, req *ListInstanceAddonErrorsRequest) (*ListInstanceAddonErrorsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-errors/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListInstanceAddonErrorsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) GetInstanceAddon(ctx context.Context, req *GetInstanceAddonRequest) (*GetInstanceAddonResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("instanceName", fmt.Sprintf("%v", req.InstanceName))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInstanceAddonResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteInstanceAddon(ctx context.Context, req *DeleteInstanceAddonRequest) (*DeleteInstanceAddonResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteInstanceAddonResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) RefreshInstanceAddon(ctx context.Context, req *RefreshInstanceAddonRequest) (*RefreshInstanceAddonResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}/refresh")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[RefreshInstanceAddonResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateInstanceAddon(ctx context.Context, req *UpdateInstanceAddonRequest) (*UpdateInstanceAddonResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateInstanceAddonResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchInstanceAddon(ctx context.Context, req *PatchInstanceAddonRequest) (*PatchInstanceAddonResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req.Patch)
	return gateway.DoRequest[PatchInstanceAddonResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ClearAddonStatusSourceHistory(ctx context.Context, req *ClearAddonStatusSourceHistoryRequest) (*ClearAddonStatusSourceHistoryResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons/{id}/clear-operation-history")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[ClearAddonStatusSourceHistoryResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) WatchInstanceAddons(ctx context.Context, req *WatchInstanceAddonsRequest) (<-chan *WatchInstanceAddonsResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addons")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.AddonId != nil {
		q.Add("addonId", fmt.Sprintf("%v", *req.AddonId))
	}
	if req.Filter != nil {
		if req.Filter.SortBy != nil {
			q.Add("filter.sortBy", req.Filter.SortBy.String())
		}
		if req.Filter.Enabled != nil {
			q.Add("filter.enabled", fmt.Sprintf("%v", *req.Filter.Enabled))
		}
		if req.Filter.Name != nil {
			q.Add("filter.name", fmt.Sprintf("%v", *req.Filter.Name))
		}
		if req.Filter.AddonType != nil {
			q.Add("filter.addonType", req.Filter.AddonType.String())
		}
		if req.Filter.ClusterNameLike != nil {
			q.Add("filter.clusterNameLike", fmt.Sprintf("%v", *req.Filter.ClusterNameLike))
		}
		for k, v := range req.Filter.ClusterLabels {
			key := fmt.Sprintf("filter.clusterLabels[%v]", k)
			q.Add(key, fmt.Sprintf("%v", v))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[WatchInstanceAddonsResponse](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) WatchInstanceAddonRepos(ctx context.Context, req *WatchInstanceAddonReposRequest) (<-chan *WatchInstanceAddonReposResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-repos")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.AddonRepoId != nil {
		q.Add("addonRepoId", fmt.Sprintf("%v", *req.AddonRepoId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[WatchInstanceAddonReposResponse](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) AddonMarketplaceInstall(ctx context.Context, req *AddonMarketplaceInstallRequest) (*AddonMarketplaceInstallResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-marketplace-installs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[AddonMarketplaceInstallResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListAddonMarketplaceInstalls(ctx context.Context, req *ListAddonMarketplaceInstallsRequest) (*ListAddonMarketplaceInstallsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/list-addon-marketplace-installs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[ListAddonMarketplaceInstallsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) WatchAddonMarketplaceInstalls(ctx context.Context, req *WatchAddonMarketplaceInstallsRequest) (<-chan *WatchAddonMarketplaceInstallsResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-marketplace-installs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	return gateway.DoStreamingRequest[WatchAddonMarketplaceInstallsResponse](ctx, c.gwc, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateAddonMarketplaceInstall(ctx context.Context, req *UpdateAddonMarketplaceInstallRequest) (*UpdateAddonMarketplaceInstallResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-marketplace-installs/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateAddonMarketplaceInstallResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceRepos(ctx context.Context, req *ListInstanceReposRequest) (*ListInstanceReposResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/repos")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	return gateway.DoRequest[ListInstanceReposResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) CreateInstanceRepo(ctx context.Context, req *CreateInstanceRepoRequest) (*CreateInstanceRepoResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/repos")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateInstanceRepoResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteAddonMarketplaceInstall(ctx context.Context, req *DeleteAddonMarketplaceInstallRequest) (*DeleteAddonMarketplaceInstallResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/addon-marketplace-installs/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteAddonMarketplaceInstallResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) ListInstanceManagedSecrets(ctx context.Context, req *ListInstanceManagedSecretsRequest) (*ListInstanceManagedSecretsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/managed-secrets")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	return gateway.DoRequest[ListInstanceManagedSecretsResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) CreateManagedSecret(ctx context.Context, req *CreateManagedSecretRequest) (*CreateManagedSecretResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/managed-secrets")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateManagedSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) DeleteManagedSecret(ctx context.Context, req *DeleteManagedSecretRequest) (*DeleteManagedSecretResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/managed-secrets/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteManagedSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) UpdateManagedSecret(ctx context.Context, req *UpdateManagedSecretRequest) (*UpdateManagedSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/managed-secrets/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateManagedSecretResponse](ctx, gwReq)
}

func (c *argoCDServiceGatewayClient) PatchManagedSecret(ctx context.Context, req *PatchManagedSecretRequest) (*PatchManagedSecretResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/argocd/instances/{instance_id}/managed-secrets/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchManagedSecretResponse](ctx, gwReq)
}
