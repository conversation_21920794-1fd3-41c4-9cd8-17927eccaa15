// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: argocd/v1/argocd.proto

package argocdv1

import (
	context "context"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ArgoCDService_ListInstanceVersions_FullMethodName                  = "/akuity.argocd.v1.ArgoCDService/ListInstanceVersions"
	ArgoCDService_ListInstances_FullMethodName                         = "/akuity.argocd.v1.ArgoCDService/ListInstances"
	ArgoCDService_WatchInstances_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/WatchInstances"
	ArgoCDService_CreateInstance_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/CreateInstance"
	ArgoCDService_GetInstance_FullMethodName                           = "/akuity.argocd.v1.ArgoCDService/GetInstance"
	ArgoCDService_GetInstanceCSS_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/GetInstanceCSS"
	ArgoCDService_GetInstanceNotificationSettings_FullMethodName       = "/akuity.argocd.v1.ArgoCDService/GetInstanceNotificationSettings"
	ArgoCDService_GetInstanceNotificationCatalog_FullMethodName        = "/akuity.argocd.v1.ArgoCDService/GetInstanceNotificationCatalog"
	ArgoCDService_GetInstanceImageUpdaterSettings_FullMethodName       = "/akuity.argocd.v1.ArgoCDService/GetInstanceImageUpdaterSettings"
	ArgoCDService_GetInstanceResourceCustomizations_FullMethodName     = "/akuity.argocd.v1.ArgoCDService/GetInstanceResourceCustomizations"
	ArgoCDService_GetInstanceConfigManagementPlugins_FullMethodName    = "/akuity.argocd.v1.ArgoCDService/GetInstanceConfigManagementPlugins"
	ArgoCDService_PatchInstance_FullMethodName                         = "/akuity.argocd.v1.ArgoCDService/PatchInstance"
	ArgoCDService_PatchInstanceSecret_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/PatchInstanceSecret"
	ArgoCDService_PatchInstanceNotificationSecret_FullMethodName       = "/akuity.argocd.v1.ArgoCDService/PatchInstanceNotificationSecret"
	ArgoCDService_PatchInstanceImageUpdaterSecret_FullMethodName       = "/akuity.argocd.v1.ArgoCDService/PatchInstanceImageUpdaterSecret"
	ArgoCDService_GetInstanceAppsetSecret_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/GetInstanceAppsetSecret"
	ArgoCDService_PatchInstanceAppsetSecret_FullMethodName             = "/akuity.argocd.v1.ArgoCDService/PatchInstanceAppsetSecret"
	ArgoCDService_UpdateInstance_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/UpdateInstance"
	ArgoCDService_UpdateInstanceWorkspace_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceWorkspace"
	ArgoCDService_UpdateInstanceCSS_FullMethodName                     = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceCSS"
	ArgoCDService_UpdateInstanceNotificationConfig_FullMethodName      = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceNotificationConfig"
	ArgoCDService_UpdateInstanceImageUpdaterConfig_FullMethodName      = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceImageUpdaterConfig"
	ArgoCDService_UpdateInstanceImageUpdaterSSHConfig_FullMethodName   = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceImageUpdaterSSHConfig"
	ArgoCDService_UpdateInstanceResourceCustomizations_FullMethodName  = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceResourceCustomizations"
	ArgoCDService_UpdateInstanceConfigManagementPlugins_FullMethodName = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceConfigManagementPlugins"
	ArgoCDService_DeleteInstance_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/DeleteInstance"
	ArgoCDService_ListInstanceAccounts_FullMethodName                  = "/akuity.argocd.v1.ArgoCDService/ListInstanceAccounts"
	ArgoCDService_UpsertInstanceAccount_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/UpsertInstanceAccount"
	ArgoCDService_UpdateInstanceAccountPassword_FullMethodName         = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceAccountPassword"
	ArgoCDService_RegenerateInstanceAccountPassword_FullMethodName     = "/akuity.argocd.v1.ArgoCDService/RegenerateInstanceAccountPassword"
	ArgoCDService_DeleteInstanceAccount_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/DeleteInstanceAccount"
	ArgoCDService_ListInstanceClusters_FullMethodName                  = "/akuity.argocd.v1.ArgoCDService/ListInstanceClusters"
	ArgoCDService_WatchInstanceClusters_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/WatchInstanceClusters"
	ArgoCDService_CreateInstanceCluster_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/CreateInstanceCluster"
	ArgoCDService_GetClusterAPIServerCAData_FullMethodName             = "/akuity.argocd.v1.ArgoCDService/GetClusterAPIServerCAData"
	ArgoCDService_GetInstanceCluster_FullMethodName                    = "/akuity.argocd.v1.ArgoCDService/GetInstanceCluster"
	ArgoCDService_GetInstanceClusterInfo_FullMethodName                = "/akuity.argocd.v1.ArgoCDService/GetInstanceClusterInfo"
	ArgoCDService_GetInstanceClusterManifests_FullMethodName           = "/akuity.argocd.v1.ArgoCDService/GetInstanceClusterManifests"
	ArgoCDService_UpdateInstanceCluster_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceCluster"
	ArgoCDService_UpdateInstanceClusters_FullMethodName                = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceClusters"
	ArgoCDService_UpdateInstanceClustersAgentVersion_FullMethodName    = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceClustersAgentVersion"
	ArgoCDService_RotateInstanceClusterCredentials_FullMethodName      = "/akuity.argocd.v1.ArgoCDService/RotateInstanceClusterCredentials"
	ArgoCDService_DeleteInstanceCluster_FullMethodName                 = "/akuity.argocd.v1.ArgoCDService/DeleteInstanceCluster"
	ArgoCDService_GetInstanceClusterCommand_FullMethodName             = "/akuity.argocd.v1.ArgoCDService/GetInstanceClusterCommand"
	ArgoCDService_GetAIAssistantUsageStats_FullMethodName              = "/akuity.argocd.v1.ArgoCDService/GetAIAssistantUsageStats"
	ArgoCDService_GetSyncOperationsStats_FullMethodName                = "/akuity.argocd.v1.ArgoCDService/GetSyncOperationsStats"
	ArgoCDService_GetSyncOperationsEvents_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/GetSyncOperationsEvents"
	ArgoCDService_ApplyInstance_FullMethodName                         = "/akuity.argocd.v1.ArgoCDService/ApplyInstance"
	ArgoCDService_ExportInstance_FullMethodName                        = "/akuity.argocd.v1.ArgoCDService/ExportInstance"
	ArgoCDService_ListInstanceAddonRepos_FullMethodName                = "/akuity.argocd.v1.ArgoCDService/ListInstanceAddonRepos"
	ArgoCDService_GetInstanceAddonRepo_FullMethodName                  = "/akuity.argocd.v1.ArgoCDService/GetInstanceAddonRepo"
	ArgoCDService_CreateInstanceAddonRepo_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/CreateInstanceAddonRepo"
	ArgoCDService_RefreshInstanceAddonRepo_FullMethodName              = "/akuity.argocd.v1.ArgoCDService/RefreshInstanceAddonRepo"
	ArgoCDService_DeleteInstanceAddonRepo_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/DeleteInstanceAddonRepo"
	ArgoCDService_ListInstanceAddons_FullMethodName                    = "/akuity.argocd.v1.ArgoCDService/ListInstanceAddons"
	ArgoCDService_ListInstanceAddonErrors_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/ListInstanceAddonErrors"
	ArgoCDService_GetInstanceAddon_FullMethodName                      = "/akuity.argocd.v1.ArgoCDService/GetInstanceAddon"
	ArgoCDService_DeleteInstanceAddon_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/DeleteInstanceAddon"
	ArgoCDService_RefreshInstanceAddon_FullMethodName                  = "/akuity.argocd.v1.ArgoCDService/RefreshInstanceAddon"
	ArgoCDService_UpdateInstanceAddon_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/UpdateInstanceAddon"
	ArgoCDService_PatchInstanceAddon_FullMethodName                    = "/akuity.argocd.v1.ArgoCDService/PatchInstanceAddon"
	ArgoCDService_ClearAddonStatusSourceHistory_FullMethodName         = "/akuity.argocd.v1.ArgoCDService/ClearAddonStatusSourceHistory"
	ArgoCDService_WatchInstanceAddons_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/WatchInstanceAddons"
	ArgoCDService_WatchInstanceAddonRepos_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/WatchInstanceAddonRepos"
	ArgoCDService_AddonMarketplaceInstall_FullMethodName               = "/akuity.argocd.v1.ArgoCDService/AddonMarketplaceInstall"
	ArgoCDService_ListAddonMarketplaceInstalls_FullMethodName          = "/akuity.argocd.v1.ArgoCDService/ListAddonMarketplaceInstalls"
	ArgoCDService_WatchAddonMarketplaceInstalls_FullMethodName         = "/akuity.argocd.v1.ArgoCDService/WatchAddonMarketplaceInstalls"
	ArgoCDService_UpdateAddonMarketplaceInstall_FullMethodName         = "/akuity.argocd.v1.ArgoCDService/UpdateAddonMarketplaceInstall"
	ArgoCDService_ListInstanceRepos_FullMethodName                     = "/akuity.argocd.v1.ArgoCDService/ListInstanceRepos"
	ArgoCDService_CreateInstanceRepo_FullMethodName                    = "/akuity.argocd.v1.ArgoCDService/CreateInstanceRepo"
	ArgoCDService_DeleteAddonMarketplaceInstall_FullMethodName         = "/akuity.argocd.v1.ArgoCDService/DeleteAddonMarketplaceInstall"
	ArgoCDService_ListInstanceManagedSecrets_FullMethodName            = "/akuity.argocd.v1.ArgoCDService/ListInstanceManagedSecrets"
	ArgoCDService_CreateManagedSecret_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/CreateManagedSecret"
	ArgoCDService_DeleteManagedSecret_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/DeleteManagedSecret"
	ArgoCDService_UpdateManagedSecret_FullMethodName                   = "/akuity.argocd.v1.ArgoCDService/UpdateManagedSecret"
	ArgoCDService_PatchManagedSecret_FullMethodName                    = "/akuity.argocd.v1.ArgoCDService/PatchManagedSecret"
)

// ArgoCDServiceClient is the client API for ArgoCDService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ArgoCDServiceClient interface {
	ListInstanceVersions(ctx context.Context, in *ListInstanceVersionsRequest, opts ...grpc.CallOption) (*ListInstanceVersionsResponse, error)
	ListInstances(ctx context.Context, in *ListInstancesRequest, opts ...grpc.CallOption) (*ListInstancesResponse, error)
	WatchInstances(ctx context.Context, in *WatchInstancesRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstancesClient, error)
	CreateInstance(ctx context.Context, in *CreateInstanceRequest, opts ...grpc.CallOption) (*CreateInstanceResponse, error)
	GetInstance(ctx context.Context, in *GetInstanceRequest, opts ...grpc.CallOption) (*GetInstanceResponse, error)
	GetInstanceCSS(ctx context.Context, in *GetInstanceCSSRequest, opts ...grpc.CallOption) (*GetInstanceCSSResponse, error)
	GetInstanceNotificationSettings(ctx context.Context, in *GetInstanceNotificationSettingsRequest, opts ...grpc.CallOption) (*GetInstanceNotificationSettingsResponse, error)
	GetInstanceNotificationCatalog(ctx context.Context, in *GetInstanceNotificationCatalogRequest, opts ...grpc.CallOption) (*GetInstanceNotificationCatalogResponse, error)
	GetInstanceImageUpdaterSettings(ctx context.Context, in *GetInstanceImageUpdaterSettingsRequest, opts ...grpc.CallOption) (*GetInstanceImageUpdaterSettingsResponse, error)
	GetInstanceResourceCustomizations(ctx context.Context, in *GetInstanceResourceCustomizationsRequest, opts ...grpc.CallOption) (*GetInstanceResourceCustomizationsResponse, error)
	GetInstanceConfigManagementPlugins(ctx context.Context, in *GetInstanceConfigManagementPluginsRequest, opts ...grpc.CallOption) (*GetInstanceConfigManagementPluginsResponse, error)
	PatchInstance(ctx context.Context, in *PatchInstanceRequest, opts ...grpc.CallOption) (*PatchInstanceResponse, error)
	PatchInstanceSecret(ctx context.Context, in *PatchInstanceSecretRequest, opts ...grpc.CallOption) (*PatchInstanceSecretResponse, error)
	PatchInstanceNotificationSecret(ctx context.Context, in *PatchInstanceNotificationSecretRequest, opts ...grpc.CallOption) (*PatchInstanceNotificationSecretResponse, error)
	PatchInstanceImageUpdaterSecret(ctx context.Context, in *PatchInstanceImageUpdaterSecretRequest, opts ...grpc.CallOption) (*PatchInstanceImageUpdaterSecretResponse, error)
	GetInstanceAppsetSecret(ctx context.Context, in *GetInstanceAppsetSecretRequest, opts ...grpc.CallOption) (*GetInstanceAppsetSecretResponse, error)
	PatchInstanceAppsetSecret(ctx context.Context, in *PatchInstanceAppsetSecretRequest, opts ...grpc.CallOption) (*PatchInstanceAppsetSecretResponse, error)
	UpdateInstance(ctx context.Context, in *UpdateInstanceRequest, opts ...grpc.CallOption) (*UpdateInstanceResponse, error)
	UpdateInstanceWorkspace(ctx context.Context, in *UpdateInstanceWorkspaceRequest, opts ...grpc.CallOption) (*UpdateInstanceWorkspaceResponse, error)
	UpdateInstanceCSS(ctx context.Context, in *UpdateInstanceCSSRequest, opts ...grpc.CallOption) (*UpdateInstanceCSSResponse, error)
	UpdateInstanceNotificationConfig(ctx context.Context, in *UpdateInstanceNotificationConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceNotificationConfigResponse, error)
	UpdateInstanceImageUpdaterConfig(ctx context.Context, in *UpdateInstanceImageUpdaterConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceImageUpdaterConfigResponse, error)
	UpdateInstanceImageUpdaterSSHConfig(ctx context.Context, in *UpdateInstanceImageUpdaterSSHConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceImageUpdaterSSHConfigResponse, error)
	UpdateInstanceResourceCustomizations(ctx context.Context, in *UpdateInstanceResourceCustomizationsRequest, opts ...grpc.CallOption) (*UpdateInstanceResourceCustomizationsResponse, error)
	UpdateInstanceConfigManagementPlugins(ctx context.Context, in *UpdateInstanceConfigManagementPluginsRequest, opts ...grpc.CallOption) (*UpdateInstanceConfigManagementPluginsResponse, error)
	DeleteInstance(ctx context.Context, in *DeleteInstanceRequest, opts ...grpc.CallOption) (*DeleteInstanceResponse, error)
	ListInstanceAccounts(ctx context.Context, in *ListInstanceAccountsRequest, opts ...grpc.CallOption) (*ListInstanceAccountsResponse, error)
	UpsertInstanceAccount(ctx context.Context, in *UpsertInstanceAccountRequest, opts ...grpc.CallOption) (*UpsertInstanceAccountResponse, error)
	UpdateInstanceAccountPassword(ctx context.Context, in *UpdateInstanceAccountPasswordRequest, opts ...grpc.CallOption) (*UpdateInstanceAccountPasswordResponse, error)
	RegenerateInstanceAccountPassword(ctx context.Context, in *RegenerateInstanceAccountPasswordRequest, opts ...grpc.CallOption) (*RegenerateInstanceAccountPasswordResponse, error)
	DeleteInstanceAccount(ctx context.Context, in *DeleteInstanceAccountRequest, opts ...grpc.CallOption) (*DeleteInstanceAccountResponse, error)
	ListInstanceClusters(ctx context.Context, in *ListInstanceClustersRequest, opts ...grpc.CallOption) (*ListInstanceClustersResponse, error)
	WatchInstanceClusters(ctx context.Context, in *WatchInstanceClustersRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceClustersClient, error)
	CreateInstanceCluster(ctx context.Context, in *CreateInstanceClusterRequest, opts ...grpc.CallOption) (*CreateInstanceClusterResponse, error)
	GetClusterAPIServerCAData(ctx context.Context, in *GetClusterAPIServerCADataRequest, opts ...grpc.CallOption) (*GetClusterAPIServerCADataResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceCluster(ctx context.Context, in *GetInstanceClusterRequest, opts ...grpc.CallOption) (*GetInstanceClusterResponse, error)
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceClusterInfo(ctx context.Context, in *GetInstanceClusterRequest, opts ...grpc.CallOption) (*GetInstanceClusterInfoResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetInstanceClusterManifests(ctx context.Context, in *GetInstanceClusterManifestsRequest, opts ...grpc.CallOption) (ArgoCDService_GetInstanceClusterManifestsClient, error)
	UpdateInstanceCluster(ctx context.Context, in *UpdateInstanceClusterRequest, opts ...grpc.CallOption) (*UpdateInstanceClusterResponse, error)
	UpdateInstanceClusters(ctx context.Context, in *UpdateInstanceClustersRequest, opts ...grpc.CallOption) (*UpdateInstanceClustersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	UpdateInstanceClustersAgentVersion(ctx context.Context, in *UpdateInstanceClustersAgentVersionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	RotateInstanceClusterCredentials(ctx context.Context, in *RotateInstanceClusterCredentialsRequest, opts ...grpc.CallOption) (*RotateInstanceClusterCredentialsResponse, error)
	DeleteInstanceCluster(ctx context.Context, in *DeleteInstanceClusterRequest, opts ...grpc.CallOption) (*DeleteInstanceClusterResponse, error)
	GetInstanceClusterCommand(ctx context.Context, in *GetInstanceClusterCommandRequest, opts ...grpc.CallOption) (*GetInstanceClusterCommandResponse, error)
	GetAIAssistantUsageStats(ctx context.Context, in *GetAIAssistantUsageStatsRequest, opts ...grpc.CallOption) (*GetAIAssistantUsageStatsResponse, error)
	GetSyncOperationsStats(ctx context.Context, in *GetSyncOperationsStatsRequest, opts ...grpc.CallOption) (*GetSyncOperationsStatsResponse, error)
	GetSyncOperationsEvents(ctx context.Context, in *GetSyncOperationsEventsRequest, opts ...grpc.CallOption) (*GetSyncOperationsEventsResponse, error)
	ApplyInstance(ctx context.Context, in *ApplyInstanceRequest, opts ...grpc.CallOption) (*ApplyInstanceResponse, error)
	ExportInstance(ctx context.Context, in *ExportInstanceRequest, opts ...grpc.CallOption) (*ExportInstanceResponse, error)
	ListInstanceAddonRepos(ctx context.Context, in *ListInstanceAddonReposRequest, opts ...grpc.CallOption) (*ListInstanceAddonReposResponse, error)
	GetInstanceAddonRepo(ctx context.Context, in *GetInstanceAddonRepoRequest, opts ...grpc.CallOption) (*GetInstanceAddonRepoResponse, error)
	CreateInstanceAddonRepo(ctx context.Context, in *CreateInstanceAddonRepoRequest, opts ...grpc.CallOption) (*CreateInstanceAddonRepoResponse, error)
	RefreshInstanceAddonRepo(ctx context.Context, in *RefreshInstanceAddonRepoRequest, opts ...grpc.CallOption) (*RefreshInstanceAddonRepoResponse, error)
	DeleteInstanceAddonRepo(ctx context.Context, in *DeleteInstanceAddonRepoRequest, opts ...grpc.CallOption) (*DeleteInstanceAddonRepoResponse, error)
	ListInstanceAddons(ctx context.Context, in *ListInstanceAddonsRequest, opts ...grpc.CallOption) (*ListInstanceAddonsResponse, error)
	ListInstanceAddonErrors(ctx context.Context, in *ListInstanceAddonErrorsRequest, opts ...grpc.CallOption) (*ListInstanceAddonErrorsResponse, error)
	GetInstanceAddon(ctx context.Context, in *GetInstanceAddonRequest, opts ...grpc.CallOption) (*GetInstanceAddonResponse, error)
	DeleteInstanceAddon(ctx context.Context, in *DeleteInstanceAddonRequest, opts ...grpc.CallOption) (*DeleteInstanceAddonResponse, error)
	RefreshInstanceAddon(ctx context.Context, in *RefreshInstanceAddonRequest, opts ...grpc.CallOption) (*RefreshInstanceAddonResponse, error)
	UpdateInstanceAddon(ctx context.Context, in *UpdateInstanceAddonRequest, opts ...grpc.CallOption) (*UpdateInstanceAddonResponse, error)
	PatchInstanceAddon(ctx context.Context, in *PatchInstanceAddonRequest, opts ...grpc.CallOption) (*PatchInstanceAddonResponse, error)
	ClearAddonStatusSourceHistory(ctx context.Context, in *ClearAddonStatusSourceHistoryRequest, opts ...grpc.CallOption) (*ClearAddonStatusSourceHistoryResponse, error)
	WatchInstanceAddons(ctx context.Context, in *WatchInstanceAddonsRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceAddonsClient, error)
	WatchInstanceAddonRepos(ctx context.Context, in *WatchInstanceAddonReposRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceAddonReposClient, error)
	AddonMarketplaceInstall(ctx context.Context, in *AddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*AddonMarketplaceInstallResponse, error)
	ListAddonMarketplaceInstalls(ctx context.Context, in *ListAddonMarketplaceInstallsRequest, opts ...grpc.CallOption) (*ListAddonMarketplaceInstallsResponse, error)
	WatchAddonMarketplaceInstalls(ctx context.Context, in *WatchAddonMarketplaceInstallsRequest, opts ...grpc.CallOption) (ArgoCDService_WatchAddonMarketplaceInstallsClient, error)
	UpdateAddonMarketplaceInstall(ctx context.Context, in *UpdateAddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*UpdateAddonMarketplaceInstallResponse, error)
	ListInstanceRepos(ctx context.Context, in *ListInstanceReposRequest, opts ...grpc.CallOption) (*ListInstanceReposResponse, error)
	CreateInstanceRepo(ctx context.Context, in *CreateInstanceRepoRequest, opts ...grpc.CallOption) (*CreateInstanceRepoResponse, error)
	DeleteAddonMarketplaceInstall(ctx context.Context, in *DeleteAddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*DeleteAddonMarketplaceInstallResponse, error)
	ListInstanceManagedSecrets(ctx context.Context, in *ListInstanceManagedSecretsRequest, opts ...grpc.CallOption) (*ListInstanceManagedSecretsResponse, error)
	CreateManagedSecret(ctx context.Context, in *CreateManagedSecretRequest, opts ...grpc.CallOption) (*CreateManagedSecretResponse, error)
	DeleteManagedSecret(ctx context.Context, in *DeleteManagedSecretRequest, opts ...grpc.CallOption) (*DeleteManagedSecretResponse, error)
	UpdateManagedSecret(ctx context.Context, in *UpdateManagedSecretRequest, opts ...grpc.CallOption) (*UpdateManagedSecretResponse, error)
	// PatchManagedSecret updates the current metadata information (i.e. access permissions and
	// labels) for the secret without modifying the actual secret data. If this is purely managed on
	// the control plane, all labels will overwrite the current labels. However, if this secret is
	// managed by another cluster, an error will be returned if any labels are set
	PatchManagedSecret(ctx context.Context, in *PatchManagedSecretRequest, opts ...grpc.CallOption) (*PatchManagedSecretResponse, error)
}

type argoCDServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewArgoCDServiceClient(cc grpc.ClientConnInterface) ArgoCDServiceClient {
	return &argoCDServiceClient{cc}
}

func (c *argoCDServiceClient) ListInstanceVersions(ctx context.Context, in *ListInstanceVersionsRequest, opts ...grpc.CallOption) (*ListInstanceVersionsResponse, error) {
	out := new(ListInstanceVersionsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceVersions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstances(ctx context.Context, in *ListInstancesRequest, opts ...grpc.CallOption) (*ListInstancesResponse, error) {
	out := new(ListInstancesResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstances_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) WatchInstances(ctx context.Context, in *WatchInstancesRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstancesClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[0], ArgoCDService_WatchInstances_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceWatchInstancesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_WatchInstancesClient interface {
	Recv() (*WatchInstancesResponse, error)
	grpc.ClientStream
}

type argoCDServiceWatchInstancesClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceWatchInstancesClient) Recv() (*WatchInstancesResponse, error) {
	m := new(WatchInstancesResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) CreateInstance(ctx context.Context, in *CreateInstanceRequest, opts ...grpc.CallOption) (*CreateInstanceResponse, error) {
	out := new(CreateInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_CreateInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstance(ctx context.Context, in *GetInstanceRequest, opts ...grpc.CallOption) (*GetInstanceResponse, error) {
	out := new(GetInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceCSS(ctx context.Context, in *GetInstanceCSSRequest, opts ...grpc.CallOption) (*GetInstanceCSSResponse, error) {
	out := new(GetInstanceCSSResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceCSS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceNotificationSettings(ctx context.Context, in *GetInstanceNotificationSettingsRequest, opts ...grpc.CallOption) (*GetInstanceNotificationSettingsResponse, error) {
	out := new(GetInstanceNotificationSettingsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceNotificationSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceNotificationCatalog(ctx context.Context, in *GetInstanceNotificationCatalogRequest, opts ...grpc.CallOption) (*GetInstanceNotificationCatalogResponse, error) {
	out := new(GetInstanceNotificationCatalogResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceNotificationCatalog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceImageUpdaterSettings(ctx context.Context, in *GetInstanceImageUpdaterSettingsRequest, opts ...grpc.CallOption) (*GetInstanceImageUpdaterSettingsResponse, error) {
	out := new(GetInstanceImageUpdaterSettingsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceImageUpdaterSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceResourceCustomizations(ctx context.Context, in *GetInstanceResourceCustomizationsRequest, opts ...grpc.CallOption) (*GetInstanceResourceCustomizationsResponse, error) {
	out := new(GetInstanceResourceCustomizationsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceResourceCustomizations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceConfigManagementPlugins(ctx context.Context, in *GetInstanceConfigManagementPluginsRequest, opts ...grpc.CallOption) (*GetInstanceConfigManagementPluginsResponse, error) {
	out := new(GetInstanceConfigManagementPluginsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceConfigManagementPlugins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstance(ctx context.Context, in *PatchInstanceRequest, opts ...grpc.CallOption) (*PatchInstanceResponse, error) {
	out := new(PatchInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstanceSecret(ctx context.Context, in *PatchInstanceSecretRequest, opts ...grpc.CallOption) (*PatchInstanceSecretResponse, error) {
	out := new(PatchInstanceSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstanceSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstanceNotificationSecret(ctx context.Context, in *PatchInstanceNotificationSecretRequest, opts ...grpc.CallOption) (*PatchInstanceNotificationSecretResponse, error) {
	out := new(PatchInstanceNotificationSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstanceNotificationSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstanceImageUpdaterSecret(ctx context.Context, in *PatchInstanceImageUpdaterSecretRequest, opts ...grpc.CallOption) (*PatchInstanceImageUpdaterSecretResponse, error) {
	out := new(PatchInstanceImageUpdaterSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstanceImageUpdaterSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceAppsetSecret(ctx context.Context, in *GetInstanceAppsetSecretRequest, opts ...grpc.CallOption) (*GetInstanceAppsetSecretResponse, error) {
	out := new(GetInstanceAppsetSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceAppsetSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstanceAppsetSecret(ctx context.Context, in *PatchInstanceAppsetSecretRequest, opts ...grpc.CallOption) (*PatchInstanceAppsetSecretResponse, error) {
	out := new(PatchInstanceAppsetSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstanceAppsetSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstance(ctx context.Context, in *UpdateInstanceRequest, opts ...grpc.CallOption) (*UpdateInstanceResponse, error) {
	out := new(UpdateInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceWorkspace(ctx context.Context, in *UpdateInstanceWorkspaceRequest, opts ...grpc.CallOption) (*UpdateInstanceWorkspaceResponse, error) {
	out := new(UpdateInstanceWorkspaceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceCSS(ctx context.Context, in *UpdateInstanceCSSRequest, opts ...grpc.CallOption) (*UpdateInstanceCSSResponse, error) {
	out := new(UpdateInstanceCSSResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceCSS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceNotificationConfig(ctx context.Context, in *UpdateInstanceNotificationConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceNotificationConfigResponse, error) {
	out := new(UpdateInstanceNotificationConfigResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceImageUpdaterConfig(ctx context.Context, in *UpdateInstanceImageUpdaterConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceImageUpdaterConfigResponse, error) {
	out := new(UpdateInstanceImageUpdaterConfigResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceImageUpdaterConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceImageUpdaterSSHConfig(ctx context.Context, in *UpdateInstanceImageUpdaterSSHConfigRequest, opts ...grpc.CallOption) (*UpdateInstanceImageUpdaterSSHConfigResponse, error) {
	out := new(UpdateInstanceImageUpdaterSSHConfigResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceImageUpdaterSSHConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceResourceCustomizations(ctx context.Context, in *UpdateInstanceResourceCustomizationsRequest, opts ...grpc.CallOption) (*UpdateInstanceResourceCustomizationsResponse, error) {
	out := new(UpdateInstanceResourceCustomizationsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceResourceCustomizations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceConfigManagementPlugins(ctx context.Context, in *UpdateInstanceConfigManagementPluginsRequest, opts ...grpc.CallOption) (*UpdateInstanceConfigManagementPluginsResponse, error) {
	out := new(UpdateInstanceConfigManagementPluginsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceConfigManagementPlugins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteInstance(ctx context.Context, in *DeleteInstanceRequest, opts ...grpc.CallOption) (*DeleteInstanceResponse, error) {
	out := new(DeleteInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceAccounts(ctx context.Context, in *ListInstanceAccountsRequest, opts ...grpc.CallOption) (*ListInstanceAccountsResponse, error) {
	out := new(ListInstanceAccountsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpsertInstanceAccount(ctx context.Context, in *UpsertInstanceAccountRequest, opts ...grpc.CallOption) (*UpsertInstanceAccountResponse, error) {
	out := new(UpsertInstanceAccountResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpsertInstanceAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceAccountPassword(ctx context.Context, in *UpdateInstanceAccountPasswordRequest, opts ...grpc.CallOption) (*UpdateInstanceAccountPasswordResponse, error) {
	out := new(UpdateInstanceAccountPasswordResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceAccountPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) RegenerateInstanceAccountPassword(ctx context.Context, in *RegenerateInstanceAccountPasswordRequest, opts ...grpc.CallOption) (*RegenerateInstanceAccountPasswordResponse, error) {
	out := new(RegenerateInstanceAccountPasswordResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_RegenerateInstanceAccountPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteInstanceAccount(ctx context.Context, in *DeleteInstanceAccountRequest, opts ...grpc.CallOption) (*DeleteInstanceAccountResponse, error) {
	out := new(DeleteInstanceAccountResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteInstanceAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceClusters(ctx context.Context, in *ListInstanceClustersRequest, opts ...grpc.CallOption) (*ListInstanceClustersResponse, error) {
	out := new(ListInstanceClustersResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) WatchInstanceClusters(ctx context.Context, in *WatchInstanceClustersRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceClustersClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[1], ArgoCDService_WatchInstanceClusters_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceWatchInstanceClustersClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_WatchInstanceClustersClient interface {
	Recv() (*WatchInstanceClustersResponse, error)
	grpc.ClientStream
}

type argoCDServiceWatchInstanceClustersClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceWatchInstanceClustersClient) Recv() (*WatchInstanceClustersResponse, error) {
	m := new(WatchInstanceClustersResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) CreateInstanceCluster(ctx context.Context, in *CreateInstanceClusterRequest, opts ...grpc.CallOption) (*CreateInstanceClusterResponse, error) {
	out := new(CreateInstanceClusterResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_CreateInstanceCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetClusterAPIServerCAData(ctx context.Context, in *GetClusterAPIServerCADataRequest, opts ...grpc.CallOption) (*GetClusterAPIServerCADataResponse, error) {
	out := new(GetClusterAPIServerCADataResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetClusterAPIServerCAData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceCluster(ctx context.Context, in *GetInstanceClusterRequest, opts ...grpc.CallOption) (*GetInstanceClusterResponse, error) {
	out := new(GetInstanceClusterResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceClusterInfo(ctx context.Context, in *GetInstanceClusterRequest, opts ...grpc.CallOption) (*GetInstanceClusterInfoResponse, error) {
	out := new(GetInstanceClusterInfoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceClusterInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceClusterManifests(ctx context.Context, in *GetInstanceClusterManifestsRequest, opts ...grpc.CallOption) (ArgoCDService_GetInstanceClusterManifestsClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[2], ArgoCDService_GetInstanceClusterManifests_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceGetInstanceClusterManifestsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_GetInstanceClusterManifestsClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type argoCDServiceGetInstanceClusterManifestsClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceGetInstanceClusterManifestsClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) UpdateInstanceCluster(ctx context.Context, in *UpdateInstanceClusterRequest, opts ...grpc.CallOption) (*UpdateInstanceClusterResponse, error) {
	out := new(UpdateInstanceClusterResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceClusters(ctx context.Context, in *UpdateInstanceClustersRequest, opts ...grpc.CallOption) (*UpdateInstanceClustersResponse, error) {
	out := new(UpdateInstanceClustersResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceClustersAgentVersion(ctx context.Context, in *UpdateInstanceClustersAgentVersionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceClustersAgentVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) RotateInstanceClusterCredentials(ctx context.Context, in *RotateInstanceClusterCredentialsRequest, opts ...grpc.CallOption) (*RotateInstanceClusterCredentialsResponse, error) {
	out := new(RotateInstanceClusterCredentialsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_RotateInstanceClusterCredentials_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteInstanceCluster(ctx context.Context, in *DeleteInstanceClusterRequest, opts ...grpc.CallOption) (*DeleteInstanceClusterResponse, error) {
	out := new(DeleteInstanceClusterResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteInstanceCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceClusterCommand(ctx context.Context, in *GetInstanceClusterCommandRequest, opts ...grpc.CallOption) (*GetInstanceClusterCommandResponse, error) {
	out := new(GetInstanceClusterCommandResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceClusterCommand_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetAIAssistantUsageStats(ctx context.Context, in *GetAIAssistantUsageStatsRequest, opts ...grpc.CallOption) (*GetAIAssistantUsageStatsResponse, error) {
	out := new(GetAIAssistantUsageStatsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetAIAssistantUsageStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetSyncOperationsStats(ctx context.Context, in *GetSyncOperationsStatsRequest, opts ...grpc.CallOption) (*GetSyncOperationsStatsResponse, error) {
	out := new(GetSyncOperationsStatsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetSyncOperationsStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetSyncOperationsEvents(ctx context.Context, in *GetSyncOperationsEventsRequest, opts ...grpc.CallOption) (*GetSyncOperationsEventsResponse, error) {
	out := new(GetSyncOperationsEventsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetSyncOperationsEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ApplyInstance(ctx context.Context, in *ApplyInstanceRequest, opts ...grpc.CallOption) (*ApplyInstanceResponse, error) {
	out := new(ApplyInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ApplyInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ExportInstance(ctx context.Context, in *ExportInstanceRequest, opts ...grpc.CallOption) (*ExportInstanceResponse, error) {
	out := new(ExportInstanceResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ExportInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceAddonRepos(ctx context.Context, in *ListInstanceAddonReposRequest, opts ...grpc.CallOption) (*ListInstanceAddonReposResponse, error) {
	out := new(ListInstanceAddonReposResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceAddonRepos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceAddonRepo(ctx context.Context, in *GetInstanceAddonRepoRequest, opts ...grpc.CallOption) (*GetInstanceAddonRepoResponse, error) {
	out := new(GetInstanceAddonRepoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceAddonRepo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) CreateInstanceAddonRepo(ctx context.Context, in *CreateInstanceAddonRepoRequest, opts ...grpc.CallOption) (*CreateInstanceAddonRepoResponse, error) {
	out := new(CreateInstanceAddonRepoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_CreateInstanceAddonRepo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) RefreshInstanceAddonRepo(ctx context.Context, in *RefreshInstanceAddonRepoRequest, opts ...grpc.CallOption) (*RefreshInstanceAddonRepoResponse, error) {
	out := new(RefreshInstanceAddonRepoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_RefreshInstanceAddonRepo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteInstanceAddonRepo(ctx context.Context, in *DeleteInstanceAddonRepoRequest, opts ...grpc.CallOption) (*DeleteInstanceAddonRepoResponse, error) {
	out := new(DeleteInstanceAddonRepoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteInstanceAddonRepo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceAddons(ctx context.Context, in *ListInstanceAddonsRequest, opts ...grpc.CallOption) (*ListInstanceAddonsResponse, error) {
	out := new(ListInstanceAddonsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceAddons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceAddonErrors(ctx context.Context, in *ListInstanceAddonErrorsRequest, opts ...grpc.CallOption) (*ListInstanceAddonErrorsResponse, error) {
	out := new(ListInstanceAddonErrorsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceAddonErrors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) GetInstanceAddon(ctx context.Context, in *GetInstanceAddonRequest, opts ...grpc.CallOption) (*GetInstanceAddonResponse, error) {
	out := new(GetInstanceAddonResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_GetInstanceAddon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteInstanceAddon(ctx context.Context, in *DeleteInstanceAddonRequest, opts ...grpc.CallOption) (*DeleteInstanceAddonResponse, error) {
	out := new(DeleteInstanceAddonResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteInstanceAddon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) RefreshInstanceAddon(ctx context.Context, in *RefreshInstanceAddonRequest, opts ...grpc.CallOption) (*RefreshInstanceAddonResponse, error) {
	out := new(RefreshInstanceAddonResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_RefreshInstanceAddon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateInstanceAddon(ctx context.Context, in *UpdateInstanceAddonRequest, opts ...grpc.CallOption) (*UpdateInstanceAddonResponse, error) {
	out := new(UpdateInstanceAddonResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateInstanceAddon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchInstanceAddon(ctx context.Context, in *PatchInstanceAddonRequest, opts ...grpc.CallOption) (*PatchInstanceAddonResponse, error) {
	out := new(PatchInstanceAddonResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchInstanceAddon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ClearAddonStatusSourceHistory(ctx context.Context, in *ClearAddonStatusSourceHistoryRequest, opts ...grpc.CallOption) (*ClearAddonStatusSourceHistoryResponse, error) {
	out := new(ClearAddonStatusSourceHistoryResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ClearAddonStatusSourceHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) WatchInstanceAddons(ctx context.Context, in *WatchInstanceAddonsRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceAddonsClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[3], ArgoCDService_WatchInstanceAddons_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceWatchInstanceAddonsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_WatchInstanceAddonsClient interface {
	Recv() (*WatchInstanceAddonsResponse, error)
	grpc.ClientStream
}

type argoCDServiceWatchInstanceAddonsClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceWatchInstanceAddonsClient) Recv() (*WatchInstanceAddonsResponse, error) {
	m := new(WatchInstanceAddonsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) WatchInstanceAddonRepos(ctx context.Context, in *WatchInstanceAddonReposRequest, opts ...grpc.CallOption) (ArgoCDService_WatchInstanceAddonReposClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[4], ArgoCDService_WatchInstanceAddonRepos_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceWatchInstanceAddonReposClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_WatchInstanceAddonReposClient interface {
	Recv() (*WatchInstanceAddonReposResponse, error)
	grpc.ClientStream
}

type argoCDServiceWatchInstanceAddonReposClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceWatchInstanceAddonReposClient) Recv() (*WatchInstanceAddonReposResponse, error) {
	m := new(WatchInstanceAddonReposResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) AddonMarketplaceInstall(ctx context.Context, in *AddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*AddonMarketplaceInstallResponse, error) {
	out := new(AddonMarketplaceInstallResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_AddonMarketplaceInstall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListAddonMarketplaceInstalls(ctx context.Context, in *ListAddonMarketplaceInstallsRequest, opts ...grpc.CallOption) (*ListAddonMarketplaceInstallsResponse, error) {
	out := new(ListAddonMarketplaceInstallsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListAddonMarketplaceInstalls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) WatchAddonMarketplaceInstalls(ctx context.Context, in *WatchAddonMarketplaceInstallsRequest, opts ...grpc.CallOption) (ArgoCDService_WatchAddonMarketplaceInstallsClient, error) {
	stream, err := c.cc.NewStream(ctx, &ArgoCDService_ServiceDesc.Streams[5], ArgoCDService_WatchAddonMarketplaceInstalls_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &argoCDServiceWatchAddonMarketplaceInstallsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArgoCDService_WatchAddonMarketplaceInstallsClient interface {
	Recv() (*WatchAddonMarketplaceInstallsResponse, error)
	grpc.ClientStream
}

type argoCDServiceWatchAddonMarketplaceInstallsClient struct {
	grpc.ClientStream
}

func (x *argoCDServiceWatchAddonMarketplaceInstallsClient) Recv() (*WatchAddonMarketplaceInstallsResponse, error) {
	m := new(WatchAddonMarketplaceInstallsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *argoCDServiceClient) UpdateAddonMarketplaceInstall(ctx context.Context, in *UpdateAddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*UpdateAddonMarketplaceInstallResponse, error) {
	out := new(UpdateAddonMarketplaceInstallResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateAddonMarketplaceInstall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceRepos(ctx context.Context, in *ListInstanceReposRequest, opts ...grpc.CallOption) (*ListInstanceReposResponse, error) {
	out := new(ListInstanceReposResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceRepos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) CreateInstanceRepo(ctx context.Context, in *CreateInstanceRepoRequest, opts ...grpc.CallOption) (*CreateInstanceRepoResponse, error) {
	out := new(CreateInstanceRepoResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_CreateInstanceRepo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteAddonMarketplaceInstall(ctx context.Context, in *DeleteAddonMarketplaceInstallRequest, opts ...grpc.CallOption) (*DeleteAddonMarketplaceInstallResponse, error) {
	out := new(DeleteAddonMarketplaceInstallResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteAddonMarketplaceInstall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) ListInstanceManagedSecrets(ctx context.Context, in *ListInstanceManagedSecretsRequest, opts ...grpc.CallOption) (*ListInstanceManagedSecretsResponse, error) {
	out := new(ListInstanceManagedSecretsResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_ListInstanceManagedSecrets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) CreateManagedSecret(ctx context.Context, in *CreateManagedSecretRequest, opts ...grpc.CallOption) (*CreateManagedSecretResponse, error) {
	out := new(CreateManagedSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_CreateManagedSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) DeleteManagedSecret(ctx context.Context, in *DeleteManagedSecretRequest, opts ...grpc.CallOption) (*DeleteManagedSecretResponse, error) {
	out := new(DeleteManagedSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_DeleteManagedSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) UpdateManagedSecret(ctx context.Context, in *UpdateManagedSecretRequest, opts ...grpc.CallOption) (*UpdateManagedSecretResponse, error) {
	out := new(UpdateManagedSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_UpdateManagedSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *argoCDServiceClient) PatchManagedSecret(ctx context.Context, in *PatchManagedSecretRequest, opts ...grpc.CallOption) (*PatchManagedSecretResponse, error) {
	out := new(PatchManagedSecretResponse)
	err := c.cc.Invoke(ctx, ArgoCDService_PatchManagedSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ArgoCDServiceServer is the server API for ArgoCDService service.
// All implementations must embed UnimplementedArgoCDServiceServer
// for forward compatibility
type ArgoCDServiceServer interface {
	ListInstanceVersions(context.Context, *ListInstanceVersionsRequest) (*ListInstanceVersionsResponse, error)
	ListInstances(context.Context, *ListInstancesRequest) (*ListInstancesResponse, error)
	WatchInstances(*WatchInstancesRequest, ArgoCDService_WatchInstancesServer) error
	CreateInstance(context.Context, *CreateInstanceRequest) (*CreateInstanceResponse, error)
	GetInstance(context.Context, *GetInstanceRequest) (*GetInstanceResponse, error)
	GetInstanceCSS(context.Context, *GetInstanceCSSRequest) (*GetInstanceCSSResponse, error)
	GetInstanceNotificationSettings(context.Context, *GetInstanceNotificationSettingsRequest) (*GetInstanceNotificationSettingsResponse, error)
	GetInstanceNotificationCatalog(context.Context, *GetInstanceNotificationCatalogRequest) (*GetInstanceNotificationCatalogResponse, error)
	GetInstanceImageUpdaterSettings(context.Context, *GetInstanceImageUpdaterSettingsRequest) (*GetInstanceImageUpdaterSettingsResponse, error)
	GetInstanceResourceCustomizations(context.Context, *GetInstanceResourceCustomizationsRequest) (*GetInstanceResourceCustomizationsResponse, error)
	GetInstanceConfigManagementPlugins(context.Context, *GetInstanceConfigManagementPluginsRequest) (*GetInstanceConfigManagementPluginsResponse, error)
	PatchInstance(context.Context, *PatchInstanceRequest) (*PatchInstanceResponse, error)
	PatchInstanceSecret(context.Context, *PatchInstanceSecretRequest) (*PatchInstanceSecretResponse, error)
	PatchInstanceNotificationSecret(context.Context, *PatchInstanceNotificationSecretRequest) (*PatchInstanceNotificationSecretResponse, error)
	PatchInstanceImageUpdaterSecret(context.Context, *PatchInstanceImageUpdaterSecretRequest) (*PatchInstanceImageUpdaterSecretResponse, error)
	GetInstanceAppsetSecret(context.Context, *GetInstanceAppsetSecretRequest) (*GetInstanceAppsetSecretResponse, error)
	PatchInstanceAppsetSecret(context.Context, *PatchInstanceAppsetSecretRequest) (*PatchInstanceAppsetSecretResponse, error)
	UpdateInstance(context.Context, *UpdateInstanceRequest) (*UpdateInstanceResponse, error)
	UpdateInstanceWorkspace(context.Context, *UpdateInstanceWorkspaceRequest) (*UpdateInstanceWorkspaceResponse, error)
	UpdateInstanceCSS(context.Context, *UpdateInstanceCSSRequest) (*UpdateInstanceCSSResponse, error)
	UpdateInstanceNotificationConfig(context.Context, *UpdateInstanceNotificationConfigRequest) (*UpdateInstanceNotificationConfigResponse, error)
	UpdateInstanceImageUpdaterConfig(context.Context, *UpdateInstanceImageUpdaterConfigRequest) (*UpdateInstanceImageUpdaterConfigResponse, error)
	UpdateInstanceImageUpdaterSSHConfig(context.Context, *UpdateInstanceImageUpdaterSSHConfigRequest) (*UpdateInstanceImageUpdaterSSHConfigResponse, error)
	UpdateInstanceResourceCustomizations(context.Context, *UpdateInstanceResourceCustomizationsRequest) (*UpdateInstanceResourceCustomizationsResponse, error)
	UpdateInstanceConfigManagementPlugins(context.Context, *UpdateInstanceConfigManagementPluginsRequest) (*UpdateInstanceConfigManagementPluginsResponse, error)
	DeleteInstance(context.Context, *DeleteInstanceRequest) (*DeleteInstanceResponse, error)
	ListInstanceAccounts(context.Context, *ListInstanceAccountsRequest) (*ListInstanceAccountsResponse, error)
	UpsertInstanceAccount(context.Context, *UpsertInstanceAccountRequest) (*UpsertInstanceAccountResponse, error)
	UpdateInstanceAccountPassword(context.Context, *UpdateInstanceAccountPasswordRequest) (*UpdateInstanceAccountPasswordResponse, error)
	RegenerateInstanceAccountPassword(context.Context, *RegenerateInstanceAccountPasswordRequest) (*RegenerateInstanceAccountPasswordResponse, error)
	DeleteInstanceAccount(context.Context, *DeleteInstanceAccountRequest) (*DeleteInstanceAccountResponse, error)
	ListInstanceClusters(context.Context, *ListInstanceClustersRequest) (*ListInstanceClustersResponse, error)
	WatchInstanceClusters(*WatchInstanceClustersRequest, ArgoCDService_WatchInstanceClustersServer) error
	CreateInstanceCluster(context.Context, *CreateInstanceClusterRequest) (*CreateInstanceClusterResponse, error)
	GetClusterAPIServerCAData(context.Context, *GetClusterAPIServerCADataRequest) (*GetClusterAPIServerCADataResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceCluster(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterResponse, error)
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetInstanceClusterInfo(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterInfoResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetInstanceClusterManifests(*GetInstanceClusterManifestsRequest, ArgoCDService_GetInstanceClusterManifestsServer) error
	UpdateInstanceCluster(context.Context, *UpdateInstanceClusterRequest) (*UpdateInstanceClusterResponse, error)
	UpdateInstanceClusters(context.Context, *UpdateInstanceClustersRequest) (*UpdateInstanceClustersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	UpdateInstanceClustersAgentVersion(context.Context, *UpdateInstanceClustersAgentVersionRequest) (*emptypb.Empty, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	RotateInstanceClusterCredentials(context.Context, *RotateInstanceClusterCredentialsRequest) (*RotateInstanceClusterCredentialsResponse, error)
	DeleteInstanceCluster(context.Context, *DeleteInstanceClusterRequest) (*DeleteInstanceClusterResponse, error)
	GetInstanceClusterCommand(context.Context, *GetInstanceClusterCommandRequest) (*GetInstanceClusterCommandResponse, error)
	GetAIAssistantUsageStats(context.Context, *GetAIAssistantUsageStatsRequest) (*GetAIAssistantUsageStatsResponse, error)
	GetSyncOperationsStats(context.Context, *GetSyncOperationsStatsRequest) (*GetSyncOperationsStatsResponse, error)
	GetSyncOperationsEvents(context.Context, *GetSyncOperationsEventsRequest) (*GetSyncOperationsEventsResponse, error)
	ApplyInstance(context.Context, *ApplyInstanceRequest) (*ApplyInstanceResponse, error)
	ExportInstance(context.Context, *ExportInstanceRequest) (*ExportInstanceResponse, error)
	ListInstanceAddonRepos(context.Context, *ListInstanceAddonReposRequest) (*ListInstanceAddonReposResponse, error)
	GetInstanceAddonRepo(context.Context, *GetInstanceAddonRepoRequest) (*GetInstanceAddonRepoResponse, error)
	CreateInstanceAddonRepo(context.Context, *CreateInstanceAddonRepoRequest) (*CreateInstanceAddonRepoResponse, error)
	RefreshInstanceAddonRepo(context.Context, *RefreshInstanceAddonRepoRequest) (*RefreshInstanceAddonRepoResponse, error)
	DeleteInstanceAddonRepo(context.Context, *DeleteInstanceAddonRepoRequest) (*DeleteInstanceAddonRepoResponse, error)
	ListInstanceAddons(context.Context, *ListInstanceAddonsRequest) (*ListInstanceAddonsResponse, error)
	ListInstanceAddonErrors(context.Context, *ListInstanceAddonErrorsRequest) (*ListInstanceAddonErrorsResponse, error)
	GetInstanceAddon(context.Context, *GetInstanceAddonRequest) (*GetInstanceAddonResponse, error)
	DeleteInstanceAddon(context.Context, *DeleteInstanceAddonRequest) (*DeleteInstanceAddonResponse, error)
	RefreshInstanceAddon(context.Context, *RefreshInstanceAddonRequest) (*RefreshInstanceAddonResponse, error)
	UpdateInstanceAddon(context.Context, *UpdateInstanceAddonRequest) (*UpdateInstanceAddonResponse, error)
	PatchInstanceAddon(context.Context, *PatchInstanceAddonRequest) (*PatchInstanceAddonResponse, error)
	ClearAddonStatusSourceHistory(context.Context, *ClearAddonStatusSourceHistoryRequest) (*ClearAddonStatusSourceHistoryResponse, error)
	WatchInstanceAddons(*WatchInstanceAddonsRequest, ArgoCDService_WatchInstanceAddonsServer) error
	WatchInstanceAddonRepos(*WatchInstanceAddonReposRequest, ArgoCDService_WatchInstanceAddonReposServer) error
	AddonMarketplaceInstall(context.Context, *AddonMarketplaceInstallRequest) (*AddonMarketplaceInstallResponse, error)
	ListAddonMarketplaceInstalls(context.Context, *ListAddonMarketplaceInstallsRequest) (*ListAddonMarketplaceInstallsResponse, error)
	WatchAddonMarketplaceInstalls(*WatchAddonMarketplaceInstallsRequest, ArgoCDService_WatchAddonMarketplaceInstallsServer) error
	UpdateAddonMarketplaceInstall(context.Context, *UpdateAddonMarketplaceInstallRequest) (*UpdateAddonMarketplaceInstallResponse, error)
	ListInstanceRepos(context.Context, *ListInstanceReposRequest) (*ListInstanceReposResponse, error)
	CreateInstanceRepo(context.Context, *CreateInstanceRepoRequest) (*CreateInstanceRepoResponse, error)
	DeleteAddonMarketplaceInstall(context.Context, *DeleteAddonMarketplaceInstallRequest) (*DeleteAddonMarketplaceInstallResponse, error)
	ListInstanceManagedSecrets(context.Context, *ListInstanceManagedSecretsRequest) (*ListInstanceManagedSecretsResponse, error)
	CreateManagedSecret(context.Context, *CreateManagedSecretRequest) (*CreateManagedSecretResponse, error)
	DeleteManagedSecret(context.Context, *DeleteManagedSecretRequest) (*DeleteManagedSecretResponse, error)
	UpdateManagedSecret(context.Context, *UpdateManagedSecretRequest) (*UpdateManagedSecretResponse, error)
	// PatchManagedSecret updates the current metadata information (i.e. access permissions and
	// labels) for the secret without modifying the actual secret data. If this is purely managed on
	// the control plane, all labels will overwrite the current labels. However, if this secret is
	// managed by another cluster, an error will be returned if any labels are set
	PatchManagedSecret(context.Context, *PatchManagedSecretRequest) (*PatchManagedSecretResponse, error)
	mustEmbedUnimplementedArgoCDServiceServer()
}

// UnimplementedArgoCDServiceServer must be embedded to have forward compatible implementations.
type UnimplementedArgoCDServiceServer struct {
}

func (UnimplementedArgoCDServiceServer) ListInstanceVersions(context.Context, *ListInstanceVersionsRequest) (*ListInstanceVersionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceVersions not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstances(context.Context, *ListInstancesRequest) (*ListInstancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstances not implemented")
}
func (UnimplementedArgoCDServiceServer) WatchInstances(*WatchInstancesRequest, ArgoCDService_WatchInstancesServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchInstances not implemented")
}
func (UnimplementedArgoCDServiceServer) CreateInstance(context.Context, *CreateInstanceRequest) (*CreateInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstance(context.Context, *GetInstanceRequest) (*GetInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceCSS(context.Context, *GetInstanceCSSRequest) (*GetInstanceCSSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceCSS not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceNotificationSettings(context.Context, *GetInstanceNotificationSettingsRequest) (*GetInstanceNotificationSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceNotificationSettings not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceNotificationCatalog(context.Context, *GetInstanceNotificationCatalogRequest) (*GetInstanceNotificationCatalogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceNotificationCatalog not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceImageUpdaterSettings(context.Context, *GetInstanceImageUpdaterSettingsRequest) (*GetInstanceImageUpdaterSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceImageUpdaterSettings not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceResourceCustomizations(context.Context, *GetInstanceResourceCustomizationsRequest) (*GetInstanceResourceCustomizationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceResourceCustomizations not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceConfigManagementPlugins(context.Context, *GetInstanceConfigManagementPluginsRequest) (*GetInstanceConfigManagementPluginsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceConfigManagementPlugins not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstance(context.Context, *PatchInstanceRequest) (*PatchInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstanceSecret(context.Context, *PatchInstanceSecretRequest) (*PatchInstanceSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstanceSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstanceNotificationSecret(context.Context, *PatchInstanceNotificationSecretRequest) (*PatchInstanceNotificationSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstanceNotificationSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstanceImageUpdaterSecret(context.Context, *PatchInstanceImageUpdaterSecretRequest) (*PatchInstanceImageUpdaterSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstanceImageUpdaterSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceAppsetSecret(context.Context, *GetInstanceAppsetSecretRequest) (*GetInstanceAppsetSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceAppsetSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstanceAppsetSecret(context.Context, *PatchInstanceAppsetSecretRequest) (*PatchInstanceAppsetSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstanceAppsetSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstance(context.Context, *UpdateInstanceRequest) (*UpdateInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceWorkspace(context.Context, *UpdateInstanceWorkspaceRequest) (*UpdateInstanceWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceWorkspace not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceCSS(context.Context, *UpdateInstanceCSSRequest) (*UpdateInstanceCSSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceCSS not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceNotificationConfig(context.Context, *UpdateInstanceNotificationConfigRequest) (*UpdateInstanceNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceNotificationConfig not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceImageUpdaterConfig(context.Context, *UpdateInstanceImageUpdaterConfigRequest) (*UpdateInstanceImageUpdaterConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceImageUpdaterConfig not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceImageUpdaterSSHConfig(context.Context, *UpdateInstanceImageUpdaterSSHConfigRequest) (*UpdateInstanceImageUpdaterSSHConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceImageUpdaterSSHConfig not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceResourceCustomizations(context.Context, *UpdateInstanceResourceCustomizationsRequest) (*UpdateInstanceResourceCustomizationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceResourceCustomizations not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceConfigManagementPlugins(context.Context, *UpdateInstanceConfigManagementPluginsRequest) (*UpdateInstanceConfigManagementPluginsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceConfigManagementPlugins not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteInstance(context.Context, *DeleteInstanceRequest) (*DeleteInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceAccounts(context.Context, *ListInstanceAccountsRequest) (*ListInstanceAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceAccounts not implemented")
}
func (UnimplementedArgoCDServiceServer) UpsertInstanceAccount(context.Context, *UpsertInstanceAccountRequest) (*UpsertInstanceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertInstanceAccount not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceAccountPassword(context.Context, *UpdateInstanceAccountPasswordRequest) (*UpdateInstanceAccountPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceAccountPassword not implemented")
}
func (UnimplementedArgoCDServiceServer) RegenerateInstanceAccountPassword(context.Context, *RegenerateInstanceAccountPasswordRequest) (*RegenerateInstanceAccountPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegenerateInstanceAccountPassword not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteInstanceAccount(context.Context, *DeleteInstanceAccountRequest) (*DeleteInstanceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceAccount not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceClusters(context.Context, *ListInstanceClustersRequest) (*ListInstanceClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceClusters not implemented")
}
func (UnimplementedArgoCDServiceServer) WatchInstanceClusters(*WatchInstanceClustersRequest, ArgoCDService_WatchInstanceClustersServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchInstanceClusters not implemented")
}
func (UnimplementedArgoCDServiceServer) CreateInstanceCluster(context.Context, *CreateInstanceClusterRequest) (*CreateInstanceClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstanceCluster not implemented")
}
func (UnimplementedArgoCDServiceServer) GetClusterAPIServerCAData(context.Context, *GetClusterAPIServerCADataRequest) (*GetClusterAPIServerCADataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClusterAPIServerCAData not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceCluster(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceCluster not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceClusterInfo(context.Context, *GetInstanceClusterRequest) (*GetInstanceClusterInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceClusterInfo not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceClusterManifests(*GetInstanceClusterManifestsRequest, ArgoCDService_GetInstanceClusterManifestsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetInstanceClusterManifests not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceCluster(context.Context, *UpdateInstanceClusterRequest) (*UpdateInstanceClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceCluster not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceClusters(context.Context, *UpdateInstanceClustersRequest) (*UpdateInstanceClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceClusters not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceClustersAgentVersion(context.Context, *UpdateInstanceClustersAgentVersionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceClustersAgentVersion not implemented")
}
func (UnimplementedArgoCDServiceServer) RotateInstanceClusterCredentials(context.Context, *RotateInstanceClusterCredentialsRequest) (*RotateInstanceClusterCredentialsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RotateInstanceClusterCredentials not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteInstanceCluster(context.Context, *DeleteInstanceClusterRequest) (*DeleteInstanceClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceCluster not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceClusterCommand(context.Context, *GetInstanceClusterCommandRequest) (*GetInstanceClusterCommandResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceClusterCommand not implemented")
}
func (UnimplementedArgoCDServiceServer) GetAIAssistantUsageStats(context.Context, *GetAIAssistantUsageStatsRequest) (*GetAIAssistantUsageStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAIAssistantUsageStats not implemented")
}
func (UnimplementedArgoCDServiceServer) GetSyncOperationsStats(context.Context, *GetSyncOperationsStatsRequest) (*GetSyncOperationsStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncOperationsStats not implemented")
}
func (UnimplementedArgoCDServiceServer) GetSyncOperationsEvents(context.Context, *GetSyncOperationsEventsRequest) (*GetSyncOperationsEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncOperationsEvents not implemented")
}
func (UnimplementedArgoCDServiceServer) ApplyInstance(context.Context, *ApplyInstanceRequest) (*ApplyInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) ExportInstance(context.Context, *ExportInstanceRequest) (*ExportInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportInstance not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceAddonRepos(context.Context, *ListInstanceAddonReposRequest) (*ListInstanceAddonReposResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceAddonRepos not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceAddonRepo(context.Context, *GetInstanceAddonRepoRequest) (*GetInstanceAddonRepoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceAddonRepo not implemented")
}
func (UnimplementedArgoCDServiceServer) CreateInstanceAddonRepo(context.Context, *CreateInstanceAddonRepoRequest) (*CreateInstanceAddonRepoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstanceAddonRepo not implemented")
}
func (UnimplementedArgoCDServiceServer) RefreshInstanceAddonRepo(context.Context, *RefreshInstanceAddonRepoRequest) (*RefreshInstanceAddonRepoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshInstanceAddonRepo not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteInstanceAddonRepo(context.Context, *DeleteInstanceAddonRepoRequest) (*DeleteInstanceAddonRepoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceAddonRepo not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceAddons(context.Context, *ListInstanceAddonsRequest) (*ListInstanceAddonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceAddons not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceAddonErrors(context.Context, *ListInstanceAddonErrorsRequest) (*ListInstanceAddonErrorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceAddonErrors not implemented")
}
func (UnimplementedArgoCDServiceServer) GetInstanceAddon(context.Context, *GetInstanceAddonRequest) (*GetInstanceAddonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceAddon not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteInstanceAddon(context.Context, *DeleteInstanceAddonRequest) (*DeleteInstanceAddonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceAddon not implemented")
}
func (UnimplementedArgoCDServiceServer) RefreshInstanceAddon(context.Context, *RefreshInstanceAddonRequest) (*RefreshInstanceAddonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshInstanceAddon not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateInstanceAddon(context.Context, *UpdateInstanceAddonRequest) (*UpdateInstanceAddonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceAddon not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchInstanceAddon(context.Context, *PatchInstanceAddonRequest) (*PatchInstanceAddonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchInstanceAddon not implemented")
}
func (UnimplementedArgoCDServiceServer) ClearAddonStatusSourceHistory(context.Context, *ClearAddonStatusSourceHistoryRequest) (*ClearAddonStatusSourceHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearAddonStatusSourceHistory not implemented")
}
func (UnimplementedArgoCDServiceServer) WatchInstanceAddons(*WatchInstanceAddonsRequest, ArgoCDService_WatchInstanceAddonsServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchInstanceAddons not implemented")
}
func (UnimplementedArgoCDServiceServer) WatchInstanceAddonRepos(*WatchInstanceAddonReposRequest, ArgoCDService_WatchInstanceAddonReposServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchInstanceAddonRepos not implemented")
}
func (UnimplementedArgoCDServiceServer) AddonMarketplaceInstall(context.Context, *AddonMarketplaceInstallRequest) (*AddonMarketplaceInstallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonMarketplaceInstall not implemented")
}
func (UnimplementedArgoCDServiceServer) ListAddonMarketplaceInstalls(context.Context, *ListAddonMarketplaceInstallsRequest) (*ListAddonMarketplaceInstallsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddonMarketplaceInstalls not implemented")
}
func (UnimplementedArgoCDServiceServer) WatchAddonMarketplaceInstalls(*WatchAddonMarketplaceInstallsRequest, ArgoCDService_WatchAddonMarketplaceInstallsServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchAddonMarketplaceInstalls not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateAddonMarketplaceInstall(context.Context, *UpdateAddonMarketplaceInstallRequest) (*UpdateAddonMarketplaceInstallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddonMarketplaceInstall not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceRepos(context.Context, *ListInstanceReposRequest) (*ListInstanceReposResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceRepos not implemented")
}
func (UnimplementedArgoCDServiceServer) CreateInstanceRepo(context.Context, *CreateInstanceRepoRequest) (*CreateInstanceRepoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstanceRepo not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteAddonMarketplaceInstall(context.Context, *DeleteAddonMarketplaceInstallRequest) (*DeleteAddonMarketplaceInstallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddonMarketplaceInstall not implemented")
}
func (UnimplementedArgoCDServiceServer) ListInstanceManagedSecrets(context.Context, *ListInstanceManagedSecretsRequest) (*ListInstanceManagedSecretsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstanceManagedSecrets not implemented")
}
func (UnimplementedArgoCDServiceServer) CreateManagedSecret(context.Context, *CreateManagedSecretRequest) (*CreateManagedSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateManagedSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) DeleteManagedSecret(context.Context, *DeleteManagedSecretRequest) (*DeleteManagedSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteManagedSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) UpdateManagedSecret(context.Context, *UpdateManagedSecretRequest) (*UpdateManagedSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateManagedSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) PatchManagedSecret(context.Context, *PatchManagedSecretRequest) (*PatchManagedSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchManagedSecret not implemented")
}
func (UnimplementedArgoCDServiceServer) mustEmbedUnimplementedArgoCDServiceServer() {}

// UnsafeArgoCDServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ArgoCDServiceServer will
// result in compilation errors.
type UnsafeArgoCDServiceServer interface {
	mustEmbedUnimplementedArgoCDServiceServer()
}

func RegisterArgoCDServiceServer(s grpc.ServiceRegistrar, srv ArgoCDServiceServer) {
	s.RegisterService(&ArgoCDService_ServiceDesc, srv)
}

func _ArgoCDService_ListInstanceVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceVersionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceVersions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceVersions(ctx, req.(*ListInstanceVersionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstances(ctx, req.(*ListInstancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_WatchInstances_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchInstancesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).WatchInstances(m, &argoCDServiceWatchInstancesServer{stream})
}

type ArgoCDService_WatchInstancesServer interface {
	Send(*WatchInstancesResponse) error
	grpc.ServerStream
}

type argoCDServiceWatchInstancesServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceWatchInstancesServer) Send(m *WatchInstancesResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_CreateInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).CreateInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_CreateInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).CreateInstance(ctx, req.(*CreateInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstance(ctx, req.(*GetInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceCSS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceCSSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceCSS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceCSS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceCSS(ctx, req.(*GetInstanceCSSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceNotificationSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceNotificationSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceNotificationSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceNotificationSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceNotificationSettings(ctx, req.(*GetInstanceNotificationSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceNotificationCatalog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceNotificationCatalogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceNotificationCatalog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceNotificationCatalog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceNotificationCatalog(ctx, req.(*GetInstanceNotificationCatalogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceImageUpdaterSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceImageUpdaterSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceImageUpdaterSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceImageUpdaterSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceImageUpdaterSettings(ctx, req.(*GetInstanceImageUpdaterSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceResourceCustomizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceResourceCustomizationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceResourceCustomizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceResourceCustomizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceResourceCustomizations(ctx, req.(*GetInstanceResourceCustomizationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceConfigManagementPlugins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceConfigManagementPluginsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceConfigManagementPlugins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceConfigManagementPlugins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceConfigManagementPlugins(ctx, req.(*GetInstanceConfigManagementPluginsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstance(ctx, req.(*PatchInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstanceSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstanceSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstanceSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstanceSecret(ctx, req.(*PatchInstanceSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstanceNotificationSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceNotificationSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstanceNotificationSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstanceNotificationSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstanceNotificationSecret(ctx, req.(*PatchInstanceNotificationSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstanceImageUpdaterSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceImageUpdaterSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstanceImageUpdaterSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstanceImageUpdaterSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstanceImageUpdaterSecret(ctx, req.(*PatchInstanceImageUpdaterSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceAppsetSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceAppsetSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceAppsetSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceAppsetSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceAppsetSecret(ctx, req.(*GetInstanceAppsetSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstanceAppsetSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceAppsetSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstanceAppsetSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstanceAppsetSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstanceAppsetSecret(ctx, req.(*PatchInstanceAppsetSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstance(ctx, req.(*UpdateInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceWorkspace(ctx, req.(*UpdateInstanceWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceCSS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceCSSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceCSS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceCSS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceCSS(ctx, req.(*UpdateInstanceCSSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceNotificationConfig(ctx, req.(*UpdateInstanceNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceImageUpdaterConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceImageUpdaterConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceImageUpdaterConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceImageUpdaterConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceImageUpdaterConfig(ctx, req.(*UpdateInstanceImageUpdaterConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceImageUpdaterSSHConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceImageUpdaterSSHConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceImageUpdaterSSHConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceImageUpdaterSSHConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceImageUpdaterSSHConfig(ctx, req.(*UpdateInstanceImageUpdaterSSHConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceResourceCustomizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceResourceCustomizationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceResourceCustomizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceResourceCustomizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceResourceCustomizations(ctx, req.(*UpdateInstanceResourceCustomizationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceConfigManagementPlugins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceConfigManagementPluginsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceConfigManagementPlugins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceConfigManagementPlugins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceConfigManagementPlugins(ctx, req.(*UpdateInstanceConfigManagementPluginsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteInstance(ctx, req.(*DeleteInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceAccounts(ctx, req.(*ListInstanceAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpsertInstanceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertInstanceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpsertInstanceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpsertInstanceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpsertInstanceAccount(ctx, req.(*UpsertInstanceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceAccountPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceAccountPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceAccountPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceAccountPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceAccountPassword(ctx, req.(*UpdateInstanceAccountPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_RegenerateInstanceAccountPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegenerateInstanceAccountPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).RegenerateInstanceAccountPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_RegenerateInstanceAccountPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).RegenerateInstanceAccountPassword(ctx, req.(*RegenerateInstanceAccountPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteInstanceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteInstanceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteInstanceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteInstanceAccount(ctx, req.(*DeleteInstanceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceClusters(ctx, req.(*ListInstanceClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_WatchInstanceClusters_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchInstanceClustersRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).WatchInstanceClusters(m, &argoCDServiceWatchInstanceClustersServer{stream})
}

type ArgoCDService_WatchInstanceClustersServer interface {
	Send(*WatchInstanceClustersResponse) error
	grpc.ServerStream
}

type argoCDServiceWatchInstanceClustersServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceWatchInstanceClustersServer) Send(m *WatchInstanceClustersResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_CreateInstanceCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).CreateInstanceCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_CreateInstanceCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).CreateInstanceCluster(ctx, req.(*CreateInstanceClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetClusterAPIServerCAData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterAPIServerCADataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetClusterAPIServerCAData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetClusterAPIServerCAData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetClusterAPIServerCAData(ctx, req.(*GetClusterAPIServerCADataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceCluster(ctx, req.(*GetInstanceClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceClusterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceClusterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceClusterInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceClusterInfo(ctx, req.(*GetInstanceClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceClusterManifests_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetInstanceClusterManifestsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).GetInstanceClusterManifests(m, &argoCDServiceGetInstanceClusterManifestsServer{stream})
}

type ArgoCDService_GetInstanceClusterManifestsServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type argoCDServiceGetInstanceClusterManifestsServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceGetInstanceClusterManifestsServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_UpdateInstanceCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceCluster(ctx, req.(*UpdateInstanceClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceClusters(ctx, req.(*UpdateInstanceClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceClustersAgentVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceClustersAgentVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceClustersAgentVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceClustersAgentVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceClustersAgentVersion(ctx, req.(*UpdateInstanceClustersAgentVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_RotateInstanceClusterCredentials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RotateInstanceClusterCredentialsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).RotateInstanceClusterCredentials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_RotateInstanceClusterCredentials_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).RotateInstanceClusterCredentials(ctx, req.(*RotateInstanceClusterCredentialsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteInstanceCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteInstanceCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteInstanceCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteInstanceCluster(ctx, req.(*DeleteInstanceClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceClusterCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceClusterCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceClusterCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceClusterCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceClusterCommand(ctx, req.(*GetInstanceClusterCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetAIAssistantUsageStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIAssistantUsageStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetAIAssistantUsageStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetAIAssistantUsageStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetAIAssistantUsageStats(ctx, req.(*GetAIAssistantUsageStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetSyncOperationsStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncOperationsStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetSyncOperationsStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetSyncOperationsStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetSyncOperationsStats(ctx, req.(*GetSyncOperationsStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetSyncOperationsEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncOperationsEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetSyncOperationsEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetSyncOperationsEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetSyncOperationsEvents(ctx, req.(*GetSyncOperationsEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ApplyInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ApplyInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ApplyInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ApplyInstance(ctx, req.(*ApplyInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ExportInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ExportInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ExportInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ExportInstance(ctx, req.(*ExportInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceAddonRepos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceAddonReposRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceAddonRepos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceAddonRepos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceAddonRepos(ctx, req.(*ListInstanceAddonReposRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceAddonRepo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceAddonRepoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceAddonRepo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceAddonRepo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceAddonRepo(ctx, req.(*GetInstanceAddonRepoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_CreateInstanceAddonRepo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceAddonRepoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).CreateInstanceAddonRepo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_CreateInstanceAddonRepo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).CreateInstanceAddonRepo(ctx, req.(*CreateInstanceAddonRepoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_RefreshInstanceAddonRepo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshInstanceAddonRepoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).RefreshInstanceAddonRepo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_RefreshInstanceAddonRepo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).RefreshInstanceAddonRepo(ctx, req.(*RefreshInstanceAddonRepoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteInstanceAddonRepo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceAddonRepoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteInstanceAddonRepo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteInstanceAddonRepo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteInstanceAddonRepo(ctx, req.(*DeleteInstanceAddonRepoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceAddons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceAddonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceAddons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceAddons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceAddons(ctx, req.(*ListInstanceAddonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceAddonErrors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceAddonErrorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceAddonErrors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceAddonErrors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceAddonErrors(ctx, req.(*ListInstanceAddonErrorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_GetInstanceAddon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceAddonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).GetInstanceAddon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_GetInstanceAddon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).GetInstanceAddon(ctx, req.(*GetInstanceAddonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteInstanceAddon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceAddonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteInstanceAddon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteInstanceAddon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteInstanceAddon(ctx, req.(*DeleteInstanceAddonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_RefreshInstanceAddon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshInstanceAddonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).RefreshInstanceAddon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_RefreshInstanceAddon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).RefreshInstanceAddon(ctx, req.(*RefreshInstanceAddonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateInstanceAddon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceAddonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateInstanceAddon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateInstanceAddon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateInstanceAddon(ctx, req.(*UpdateInstanceAddonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchInstanceAddon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchInstanceAddonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchInstanceAddon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchInstanceAddon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchInstanceAddon(ctx, req.(*PatchInstanceAddonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ClearAddonStatusSourceHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearAddonStatusSourceHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ClearAddonStatusSourceHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ClearAddonStatusSourceHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ClearAddonStatusSourceHistory(ctx, req.(*ClearAddonStatusSourceHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_WatchInstanceAddons_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchInstanceAddonsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).WatchInstanceAddons(m, &argoCDServiceWatchInstanceAddonsServer{stream})
}

type ArgoCDService_WatchInstanceAddonsServer interface {
	Send(*WatchInstanceAddonsResponse) error
	grpc.ServerStream
}

type argoCDServiceWatchInstanceAddonsServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceWatchInstanceAddonsServer) Send(m *WatchInstanceAddonsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_WatchInstanceAddonRepos_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchInstanceAddonReposRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).WatchInstanceAddonRepos(m, &argoCDServiceWatchInstanceAddonReposServer{stream})
}

type ArgoCDService_WatchInstanceAddonReposServer interface {
	Send(*WatchInstanceAddonReposResponse) error
	grpc.ServerStream
}

type argoCDServiceWatchInstanceAddonReposServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceWatchInstanceAddonReposServer) Send(m *WatchInstanceAddonReposResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_AddonMarketplaceInstall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddonMarketplaceInstallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).AddonMarketplaceInstall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_AddonMarketplaceInstall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).AddonMarketplaceInstall(ctx, req.(*AddonMarketplaceInstallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListAddonMarketplaceInstalls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddonMarketplaceInstallsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListAddonMarketplaceInstalls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListAddonMarketplaceInstalls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListAddonMarketplaceInstalls(ctx, req.(*ListAddonMarketplaceInstallsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_WatchAddonMarketplaceInstalls_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchAddonMarketplaceInstallsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArgoCDServiceServer).WatchAddonMarketplaceInstalls(m, &argoCDServiceWatchAddonMarketplaceInstallsServer{stream})
}

type ArgoCDService_WatchAddonMarketplaceInstallsServer interface {
	Send(*WatchAddonMarketplaceInstallsResponse) error
	grpc.ServerStream
}

type argoCDServiceWatchAddonMarketplaceInstallsServer struct {
	grpc.ServerStream
}

func (x *argoCDServiceWatchAddonMarketplaceInstallsServer) Send(m *WatchAddonMarketplaceInstallsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ArgoCDService_UpdateAddonMarketplaceInstall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAddonMarketplaceInstallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateAddonMarketplaceInstall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateAddonMarketplaceInstall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateAddonMarketplaceInstall(ctx, req.(*UpdateAddonMarketplaceInstallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceRepos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceReposRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceRepos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceRepos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceRepos(ctx, req.(*ListInstanceReposRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_CreateInstanceRepo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceRepoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).CreateInstanceRepo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_CreateInstanceRepo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).CreateInstanceRepo(ctx, req.(*CreateInstanceRepoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteAddonMarketplaceInstall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddonMarketplaceInstallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteAddonMarketplaceInstall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteAddonMarketplaceInstall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteAddonMarketplaceInstall(ctx, req.(*DeleteAddonMarketplaceInstallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_ListInstanceManagedSecrets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstanceManagedSecretsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).ListInstanceManagedSecrets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_ListInstanceManagedSecrets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).ListInstanceManagedSecrets(ctx, req.(*ListInstanceManagedSecretsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_CreateManagedSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateManagedSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).CreateManagedSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_CreateManagedSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).CreateManagedSecret(ctx, req.(*CreateManagedSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_DeleteManagedSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteManagedSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).DeleteManagedSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_DeleteManagedSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).DeleteManagedSecret(ctx, req.(*DeleteManagedSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_UpdateManagedSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateManagedSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).UpdateManagedSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_UpdateManagedSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).UpdateManagedSecret(ctx, req.(*UpdateManagedSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArgoCDService_PatchManagedSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchManagedSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArgoCDServiceServer).PatchManagedSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArgoCDService_PatchManagedSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArgoCDServiceServer).PatchManagedSecret(ctx, req.(*PatchManagedSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ArgoCDService_ServiceDesc is the grpc.ServiceDesc for ArgoCDService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ArgoCDService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "akuity.argocd.v1.ArgoCDService",
	HandlerType: (*ArgoCDServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListInstanceVersions",
			Handler:    _ArgoCDService_ListInstanceVersions_Handler,
		},
		{
			MethodName: "ListInstances",
			Handler:    _ArgoCDService_ListInstances_Handler,
		},
		{
			MethodName: "CreateInstance",
			Handler:    _ArgoCDService_CreateInstance_Handler,
		},
		{
			MethodName: "GetInstance",
			Handler:    _ArgoCDService_GetInstance_Handler,
		},
		{
			MethodName: "GetInstanceCSS",
			Handler:    _ArgoCDService_GetInstanceCSS_Handler,
		},
		{
			MethodName: "GetInstanceNotificationSettings",
			Handler:    _ArgoCDService_GetInstanceNotificationSettings_Handler,
		},
		{
			MethodName: "GetInstanceNotificationCatalog",
			Handler:    _ArgoCDService_GetInstanceNotificationCatalog_Handler,
		},
		{
			MethodName: "GetInstanceImageUpdaterSettings",
			Handler:    _ArgoCDService_GetInstanceImageUpdaterSettings_Handler,
		},
		{
			MethodName: "GetInstanceResourceCustomizations",
			Handler:    _ArgoCDService_GetInstanceResourceCustomizations_Handler,
		},
		{
			MethodName: "GetInstanceConfigManagementPlugins",
			Handler:    _ArgoCDService_GetInstanceConfigManagementPlugins_Handler,
		},
		{
			MethodName: "PatchInstance",
			Handler:    _ArgoCDService_PatchInstance_Handler,
		},
		{
			MethodName: "PatchInstanceSecret",
			Handler:    _ArgoCDService_PatchInstanceSecret_Handler,
		},
		{
			MethodName: "PatchInstanceNotificationSecret",
			Handler:    _ArgoCDService_PatchInstanceNotificationSecret_Handler,
		},
		{
			MethodName: "PatchInstanceImageUpdaterSecret",
			Handler:    _ArgoCDService_PatchInstanceImageUpdaterSecret_Handler,
		},
		{
			MethodName: "GetInstanceAppsetSecret",
			Handler:    _ArgoCDService_GetInstanceAppsetSecret_Handler,
		},
		{
			MethodName: "PatchInstanceAppsetSecret",
			Handler:    _ArgoCDService_PatchInstanceAppsetSecret_Handler,
		},
		{
			MethodName: "UpdateInstance",
			Handler:    _ArgoCDService_UpdateInstance_Handler,
		},
		{
			MethodName: "UpdateInstanceWorkspace",
			Handler:    _ArgoCDService_UpdateInstanceWorkspace_Handler,
		},
		{
			MethodName: "UpdateInstanceCSS",
			Handler:    _ArgoCDService_UpdateInstanceCSS_Handler,
		},
		{
			MethodName: "UpdateInstanceNotificationConfig",
			Handler:    _ArgoCDService_UpdateInstanceNotificationConfig_Handler,
		},
		{
			MethodName: "UpdateInstanceImageUpdaterConfig",
			Handler:    _ArgoCDService_UpdateInstanceImageUpdaterConfig_Handler,
		},
		{
			MethodName: "UpdateInstanceImageUpdaterSSHConfig",
			Handler:    _ArgoCDService_UpdateInstanceImageUpdaterSSHConfig_Handler,
		},
		{
			MethodName: "UpdateInstanceResourceCustomizations",
			Handler:    _ArgoCDService_UpdateInstanceResourceCustomizations_Handler,
		},
		{
			MethodName: "UpdateInstanceConfigManagementPlugins",
			Handler:    _ArgoCDService_UpdateInstanceConfigManagementPlugins_Handler,
		},
		{
			MethodName: "DeleteInstance",
			Handler:    _ArgoCDService_DeleteInstance_Handler,
		},
		{
			MethodName: "ListInstanceAccounts",
			Handler:    _ArgoCDService_ListInstanceAccounts_Handler,
		},
		{
			MethodName: "UpsertInstanceAccount",
			Handler:    _ArgoCDService_UpsertInstanceAccount_Handler,
		},
		{
			MethodName: "UpdateInstanceAccountPassword",
			Handler:    _ArgoCDService_UpdateInstanceAccountPassword_Handler,
		},
		{
			MethodName: "RegenerateInstanceAccountPassword",
			Handler:    _ArgoCDService_RegenerateInstanceAccountPassword_Handler,
		},
		{
			MethodName: "DeleteInstanceAccount",
			Handler:    _ArgoCDService_DeleteInstanceAccount_Handler,
		},
		{
			MethodName: "ListInstanceClusters",
			Handler:    _ArgoCDService_ListInstanceClusters_Handler,
		},
		{
			MethodName: "CreateInstanceCluster",
			Handler:    _ArgoCDService_CreateInstanceCluster_Handler,
		},
		{
			MethodName: "GetClusterAPIServerCAData",
			Handler:    _ArgoCDService_GetClusterAPIServerCAData_Handler,
		},
		{
			MethodName: "GetInstanceCluster",
			Handler:    _ArgoCDService_GetInstanceCluster_Handler,
		},
		{
			MethodName: "GetInstanceClusterInfo",
			Handler:    _ArgoCDService_GetInstanceClusterInfo_Handler,
		},
		{
			MethodName: "UpdateInstanceCluster",
			Handler:    _ArgoCDService_UpdateInstanceCluster_Handler,
		},
		{
			MethodName: "UpdateInstanceClusters",
			Handler:    _ArgoCDService_UpdateInstanceClusters_Handler,
		},
		{
			MethodName: "UpdateInstanceClustersAgentVersion",
			Handler:    _ArgoCDService_UpdateInstanceClustersAgentVersion_Handler,
		},
		{
			MethodName: "RotateInstanceClusterCredentials",
			Handler:    _ArgoCDService_RotateInstanceClusterCredentials_Handler,
		},
		{
			MethodName: "DeleteInstanceCluster",
			Handler:    _ArgoCDService_DeleteInstanceCluster_Handler,
		},
		{
			MethodName: "GetInstanceClusterCommand",
			Handler:    _ArgoCDService_GetInstanceClusterCommand_Handler,
		},
		{
			MethodName: "GetAIAssistantUsageStats",
			Handler:    _ArgoCDService_GetAIAssistantUsageStats_Handler,
		},
		{
			MethodName: "GetSyncOperationsStats",
			Handler:    _ArgoCDService_GetSyncOperationsStats_Handler,
		},
		{
			MethodName: "GetSyncOperationsEvents",
			Handler:    _ArgoCDService_GetSyncOperationsEvents_Handler,
		},
		{
			MethodName: "ApplyInstance",
			Handler:    _ArgoCDService_ApplyInstance_Handler,
		},
		{
			MethodName: "ExportInstance",
			Handler:    _ArgoCDService_ExportInstance_Handler,
		},
		{
			MethodName: "ListInstanceAddonRepos",
			Handler:    _ArgoCDService_ListInstanceAddonRepos_Handler,
		},
		{
			MethodName: "GetInstanceAddonRepo",
			Handler:    _ArgoCDService_GetInstanceAddonRepo_Handler,
		},
		{
			MethodName: "CreateInstanceAddonRepo",
			Handler:    _ArgoCDService_CreateInstanceAddonRepo_Handler,
		},
		{
			MethodName: "RefreshInstanceAddonRepo",
			Handler:    _ArgoCDService_RefreshInstanceAddonRepo_Handler,
		},
		{
			MethodName: "DeleteInstanceAddonRepo",
			Handler:    _ArgoCDService_DeleteInstanceAddonRepo_Handler,
		},
		{
			MethodName: "ListInstanceAddons",
			Handler:    _ArgoCDService_ListInstanceAddons_Handler,
		},
		{
			MethodName: "ListInstanceAddonErrors",
			Handler:    _ArgoCDService_ListInstanceAddonErrors_Handler,
		},
		{
			MethodName: "GetInstanceAddon",
			Handler:    _ArgoCDService_GetInstanceAddon_Handler,
		},
		{
			MethodName: "DeleteInstanceAddon",
			Handler:    _ArgoCDService_DeleteInstanceAddon_Handler,
		},
		{
			MethodName: "RefreshInstanceAddon",
			Handler:    _ArgoCDService_RefreshInstanceAddon_Handler,
		},
		{
			MethodName: "UpdateInstanceAddon",
			Handler:    _ArgoCDService_UpdateInstanceAddon_Handler,
		},
		{
			MethodName: "PatchInstanceAddon",
			Handler:    _ArgoCDService_PatchInstanceAddon_Handler,
		},
		{
			MethodName: "ClearAddonStatusSourceHistory",
			Handler:    _ArgoCDService_ClearAddonStatusSourceHistory_Handler,
		},
		{
			MethodName: "AddonMarketplaceInstall",
			Handler:    _ArgoCDService_AddonMarketplaceInstall_Handler,
		},
		{
			MethodName: "ListAddonMarketplaceInstalls",
			Handler:    _ArgoCDService_ListAddonMarketplaceInstalls_Handler,
		},
		{
			MethodName: "UpdateAddonMarketplaceInstall",
			Handler:    _ArgoCDService_UpdateAddonMarketplaceInstall_Handler,
		},
		{
			MethodName: "ListInstanceRepos",
			Handler:    _ArgoCDService_ListInstanceRepos_Handler,
		},
		{
			MethodName: "CreateInstanceRepo",
			Handler:    _ArgoCDService_CreateInstanceRepo_Handler,
		},
		{
			MethodName: "DeleteAddonMarketplaceInstall",
			Handler:    _ArgoCDService_DeleteAddonMarketplaceInstall_Handler,
		},
		{
			MethodName: "ListInstanceManagedSecrets",
			Handler:    _ArgoCDService_ListInstanceManagedSecrets_Handler,
		},
		{
			MethodName: "CreateManagedSecret",
			Handler:    _ArgoCDService_CreateManagedSecret_Handler,
		},
		{
			MethodName: "DeleteManagedSecret",
			Handler:    _ArgoCDService_DeleteManagedSecret_Handler,
		},
		{
			MethodName: "UpdateManagedSecret",
			Handler:    _ArgoCDService_UpdateManagedSecret_Handler,
		},
		{
			MethodName: "PatchManagedSecret",
			Handler:    _ArgoCDService_PatchManagedSecret_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WatchInstances",
			Handler:       _ArgoCDService_WatchInstances_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchInstanceClusters",
			Handler:       _ArgoCDService_WatchInstanceClusters_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetInstanceClusterManifests",
			Handler:       _ArgoCDService_GetInstanceClusterManifests_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchInstanceAddons",
			Handler:       _ArgoCDService_WatchInstanceAddons_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchInstanceAddonRepos",
			Handler:       _ArgoCDService_WatchInstanceAddonRepos_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchAddonMarketplaceInstalls",
			Handler:       _ArgoCDService_WatchAddonMarketplaceInstalls_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "argocd/v1/argocd.proto",
}
