// Code generated by protoc-gen-grpc-gateway-client. DO NOT EDIT.
// source: organization/v1/organization.proto

package organizationv1

import (
	context "context"
	fmt "fmt"
	gateway "github.com/akuity/grpc-gateway-client/pkg/grpc/gateway"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	url "net/url"
)

// OrganizationServiceGatewayClient is the interface for OrganizationService service client.
type OrganizationServiceGatewayClient interface {
	ListAuthenticatedUserOrganizations(context.Context, *ListAuthenticatedUserOrganizationsRequest) (*ListAuthenticatedUserOrganizationsResponse, error)
	GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error)
	GetOrganizationPermissions(context.Context, *GetOrganizationPermissionsRequest) (*GetOrganizationPermissionsResponse, error)
	CreateOrganization(context.Context, *CreateOrganizationRequest) (*CreateOrganizationResponse, error)
	UpdateOrganization(context.Context, *UpdateOrganizationRequest) (*UpdateOrganizationResponse, error)
	DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error)
	ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error)
	ListOrganizationInvitees(context.Context, *ListOrganizationInviteesRequest) (*ListOrganizationInviteesResponse, error)
	GetUserRoleInOrganization(context.Context, *GetUserRoleInOrganizationRequest) (*GetUserRoleInOrganizationResponse, error)
	InviteMembers(context.Context, *InviteMembersRequest) (*InviteMembersResponse, error)
	UninviteOrganizationMember(context.Context, *UninviteOrganizationMemberRequest) (*UninviteOrganizationMemberResponse, error)
	RemoveOrganizationMember(context.Context, *RemoveOrganizationMemberRequest) (*RemoveOrganizationMemberResponse, error)
	UpdateOrganizationMemberRole(context.Context, *UpdateOrganizationMemberRoleRequest) (*UpdateOrganizationMemberRoleResponse, error)
	JoinOrganization(context.Context, *JoinOrganizationRequest) (*JoinOrganizationResponse, error)
	RejectOrganization(context.Context, *RejectOrganizationRequest) (*RejectOrganizationResponse, error)
	ListOrganizationAPIKeys(context.Context, *ListOrganizationAPIKeysRequest) (*ListOrganizationAPIKeysResponse, error)
	CreateOrganizationAPIKey(context.Context, *CreateOrganizationAPIKeyRequest) (*CreateOrganizationAPIKeyResponse, error)
	ListWorkspaceAPIKeys(context.Context, *ListWorkspaceAPIKeysRequest) (*ListWorkspaceAPIKeysResponse, error)
	CreateWorkspaceAPIKey(context.Context, *CreateWorkspaceAPIKeyRequest) (*CreateWorkspaceAPIKeyResponse, error)
	GetAuditLogs(context.Context, *GetAuditLogsRequest) (*GetAuditLogsResponse, error)
	ListAuditLogsArchives(context.Context, *ListAuditLogsArchivesRequest) (*ListAuditLogsArchivesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetAuditLogsInCSV(context.Context, *GetAuditLogsInCSVRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	GetCustomerDetails(context.Context, *GetCustomerDetailsRequest) (*GetCustomerDetailsResponse, error)
	UpdateBillingDetails(context.Context, *UpdateBillingDetailsRequest) (*UpdateBillingDetailsResponse, error)
	BillingCheckout(context.Context, *BillingCheckoutRequest) (*BillingCheckoutResponse, error)
	UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error)
	ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error)
	GetAvailableAddons(context.Context, *GetAvailableAddonsRequest) (*GetAvailableAddonsResponse, error)
	GetSSOConfiguration(context.Context, *GetSSOConfigurationRequest) (*GetSSOConfigurationResponse, error)
	EnsureSSOConfiguration(context.Context, *EnsureSSOConfigurationRequest) (*EnsureSSOConfigurationResponse, error)
	DeleteSSOConfiguration(context.Context, *DeleteSSOConfigurationRequest) (*DeleteSSOConfigurationResponse, error)
	GetFeatureStatuses(context.Context, *GetFeatureStatusesRequest) (*GetFeatureStatusesResponse, error)
	GetOIDCMap(context.Context, *GetOIDCMapRequest) (*GetOIDCMapResponse, error)
	UpdateOIDCMap(context.Context, *UpdateOIDCMapRequest) (*UpdateOIDCMapResponse, error)
	GetTeamOIDCMap(context.Context, *GetTeamOIDCMapRequest) (*GetTeamOIDCMapResponse, error)
	UpdateTeamOIDCMap(context.Context, *UpdateTeamOIDCMapRequest) (*UpdateTeamOIDCMapResponse, error)
	CreateCustomRole(context.Context, *CreateCustomRoleRequest) (*CreateCustomRoleResponse, error)
	UpdateCustomRole(context.Context, *UpdateCustomRoleRequest) (*UpdateCustomRoleResponse, error)
	GetCustomRole(context.Context, *GetCustomRoleRequest) (*GetCustomRoleResponse, error)
	ListCustomRoles(context.Context, *ListCustomRolesRequest) (*ListCustomRolesResponse, error)
	DeleteCustomRole(context.Context, *DeleteCustomRoleRequest) (*DeleteCustomRoleResponse, error)
	CreateWorkspaceCustomRole(context.Context, *CreateWorkspaceCustomRoleRequest) (*CreateWorkspaceCustomRoleResponse, error)
	UpdateWorkspaceCustomRole(context.Context, *UpdateWorkspaceCustomRoleRequest) (*UpdateWorkspaceCustomRoleResponse, error)
	GetWorkspaceCustomRole(context.Context, *GetWorkspaceCustomRoleRequest) (*GetWorkspaceCustomRoleResponse, error)
	ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error)
	DeleteWorkspaceCustomRole(context.Context, *DeleteWorkspaceCustomRoleRequest) (*DeleteWorkspaceCustomRoleResponse, error)
	CreateTeam(context.Context, *CreateTeamRequest) (*CreateTeamResponse, error)
	UpdateTeam(context.Context, *UpdateTeamRequest) (*UpdateTeamResponse, error)
	GetTeam(context.Context, *GetTeamRequest) (*GetTeamResponse, error)
	ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error)
	DeleteTeam(context.Context, *DeleteTeamRequest) (*DeleteTeamResponse, error)
	AddTeamMember(context.Context, *AddTeamMemberRequest) (*AddTeamMemberResponse, error)
	GetTeamMember(context.Context, *GetTeamMemberRequest) (*GetTeamMemberResponse, error)
	ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error)
	RemoveTeamMember(context.Context, *RemoveTeamMemberRequest) (*RemoveTeamMemberResponse, error)
	UpdateArgocdInstancesQuota(context.Context, *UpdateArgocdInstancesQuotaRequest) (*UpdateArgocdInstancesQuotaResponse, error)
	ListArgocdInstancesQuota(context.Context, *ListArgocdInstancesQuotaRequest) (*ListArgocdInstancesQuotaResponse, error)
	UpdateKargoInstancesQuota(context.Context, *UpdateKargoInstancesQuotaRequest) (*UpdateKargoInstancesQuotaResponse, error)
	ListKargoInstancesQuota(context.Context, *ListKargoInstancesQuotaRequest) (*ListKargoInstancesQuotaResponse, error)
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceResponse, error)
	ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error)
	GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error)
	UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*UpdateWorkspaceResponse, error)
	DeleteWorkspace(context.Context, *DeleteWorkspaceRequest) (*DeleteWorkspaceResponse, error)
	AddWorkspaceMember(context.Context, *AddWorkspaceMemberRequest) (*AddWorkspaceMemberResponse, error)
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	UpdateWorkspaceMembers(context.Context, *UpdateWorkspaceMembersRequest) (*UpdateWorkspaceMembersResponse, error)
	GetWorkspaceMember(context.Context, *GetWorkspaceMemberRequest) (*GetWorkspaceMemberResponse, error)
	UpdateWorkspaceMember(context.Context, *UpdateWorkspaceMemberRequest) (*UpdateWorkspaceMemberResponse, error)
	RemoveWorkspaceMember(context.Context, *RemoveWorkspaceMemberRequest) (*RemoveWorkspaceMemberResponse, error)
	CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error)
	ListKubernetesResourceTypes(context.Context, *ListKubernetesResourceTypesRequest) (*ListKubernetesResourceTypesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesResources(context.Context, *ListKubernetesResourcesRequest) (*ListKubernetesResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesResourcesToCSV(context.Context, *ListKubernetesResourcesRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	SpotlightSearchKubernetesResources(context.Context, *SpotlightSearchKubernetesResourcesRequest) (*SpotlightSearchKubernetesResourcesResponse, error)
	GetKubernetesResourceDetail(context.Context, *GetKubernetesResourceDetailRequest) (*GetKubernetesResourceDetailResponse, error)
	GetKubernetesContainer(context.Context, *GetKubernetesContainerRequest) (*GetKubernetesContainerResponse, error)
	ListKubernetesNamespaces(context.Context, *ListKubernetesNamespacesRequest) (*ListKubernetesNamespacesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesImages(context.Context, *ListKubernetesImagesRequest) (*ListKubernetesImagesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesImagesToCSV(context.Context, *ListKubernetesImagesRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	GetKubernetesImageDetail(context.Context, *GetKubernetesImageDetailRequest) (*GetKubernetesImageDetailResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesContainers(context.Context, *ListKubernetesContainersRequest) (*ListKubernetesContainersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesContainersToCSV(context.Context, *ListKubernetesContainersRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	ListKubernetesEnabledClusters(context.Context, *ListKubernetesEnabledClustersRequest) (*ListKubernetesEnabledClustersResponse, error)
	GetKubernetesManifest(context.Context, *GetKubernetesManifestRequest) (*GetKubernetesManifestResponse, error)
	DeleteKubernetesResource(context.Context, *DeleteKubernetesResourceRequest) (*DeleteKubernetesResourceResponse, error)
	GetKubernetesLogs(context.Context, *GetKubernetesLogsRequest) (<-chan *GetKubernetesLogsResponse, <-chan error, error)
	GetKubernetesEvents(context.Context, *GetKubernetesEventsRequest) (*GetKubernetesEventsResponse, error)
	ListKubernetesAuditLogs(context.Context, *ListKubernetesAuditLogsRequest) (*ListKubernetesAuditLogsResponse, error)
	ListKubernetesNodes(context.Context, *ListKubernetesNodesRequest) (*ListKubernetesNodesResponse, error)
	GetKubernetesNode(context.Context, *GetKubernetesNodeRequest) (*GetKubernetesNodeResponse, error)
	ListKubernetesNamespacesDetails(context.Context, *ListKubernetesNamespacesDetailsRequest) (*ListKubernetesNamespacesDetailsResponse, error)
	GetKubernetesNamespaceDetail(context.Context, *GetKubernetesNamespaceDetailRequest) (*GetKubernetesNamespaceDetailResponse, error)
	GetKubernetesClusterDetail(context.Context, *GetKubernetesClusterDetailRequest) (*GetKubernetesClusterDetailResponse, error)
	GetKubernetesSummary(context.Context, *GetKubernetesSummaryRequest) (*GetKubernetesSummaryResponse, error)
	ListKubernetesPods(context.Context, *ListKubernetesPodsRequest) (*ListKubernetesPodsResponse, error)
	GetKubernetesPod(context.Context, *GetKubernetesPodRequest) (*GetKubernetesPodResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesDeprecatedAPIs(context.Context, *ListKubernetesDeprecatedAPIsRequest) (*ListKubernetesDeprecatedAPIsResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesDeprecatedAPIsToCSV(context.Context, *ListKubernetesDeprecatedAPIsRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	GetKubernetesAssistantSuggestion(context.Context, *GetKubernetesAssistantSuggestionRequest) (*GetKubernetesAssistantSuggestionResponse, error)
	ResolveKubernetesAssistantConversation(context.Context, *ResolveKubernetesAssistantConversationRequest) (*ResolveKubernetesAssistantConversationResponse, error)
	ListKubernetesTimelineEvents(context.Context, *ListKubernetesTimelineEventsRequest) (*ListKubernetesTimelineEventsResponse, error)
	ListKubernetesTimelineResources(context.Context, *ListKubernetesTimelineResourcesRequest) (*ListKubernetesTimelineResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKubeVisionUsageToCSV(context.Context, *GetKubeVisionUsageRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	// Notification Configs
	ListNotificationConfigs(context.Context, *ListNotificationConfigsRequest) (*ListNotificationConfigsResponse, error)
	GetNotificationConfig(context.Context, *GetNotificationConfigRequest) (*GetNotificationConfigResponse, error)
	CreateNotificationConfig(context.Context, *CreateNotificationConfigRequest) (*CreateNotificationConfigResponse, error)
	UpdateNotificationConfig(context.Context, *UpdateNotificationConfigRequest) (*UpdateNotificationConfigResponse, error)
	DeleteNotificationConfig(context.Context, *DeleteNotificationConfigRequest) (*DeleteNotificationConfigResponse, error)
	ListNotificationDeliveryHistory(context.Context, *ListNotificationDeliveryHistoryRequest) (*ListNotificationDeliveryHistoryResponse, error)
	GetNotificationDeliveryHistoryDetail(context.Context, *GetNotificationDeliveryHistoryDetailRequest) (*GetNotificationDeliveryHistoryDetailResponse, error)
	PingNotificationConfig(context.Context, *PingNotificationConfigRequest) (*PingNotificationConfigResponse, error)
	RedeliverNotification(context.Context, *RedeliverNotificationRequest) (*RedeliverNotificationResponse, error)
	ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error)
	DeleteOrganizationDomain(context.Context, *DeleteOrganizationDomainRequest) (*DeleteOrganizationDomainResponse, error)
	VerifyOrganizationDomains(context.Context, *VerifyOrganizationDomainsRequest) (*VerifyOrganizationDomainsResponse, error)
	CreateAIConversation(context.Context, *CreateAIConversationRequest) (*CreateAIConversationResponse, error)
	CreateIncident(context.Context, *CreateIncidentRequest) (*CreateIncidentResponse, error)
	UpdateAIConversation(context.Context, *UpdateAIConversationRequest) (*UpdateAIConversationResponse, error)
	DeleteAIConversation(context.Context, *DeleteAIConversationRequest) (*DeleteAIConversationResponse, error)
	GetAIConversation(context.Context, *GetAIConversationRequest) (*GetAIConversationResponse, error)
	GetAIConversationStream(context.Context, *GetAIConversationStreamRequest) (<-chan *GetAIConversationStreamResponse, <-chan error, error)
	ListAIConversations(context.Context, *ListAIConversationsRequest) (*ListAIConversationsResponse, error)
	CreateAIMessage(context.Context, *CreateAIMessageRequest) (*CreateAIMessageResponse, error)
	ListUsersMFAStatus(context.Context, *ListUsersMFAStatusRequest) (*ListUsersMFAStatusResponse, error)
	RequestMFAReset(context.Context, *RequestMFAResetRequest) (*RequestMFAResetResponse, error)
	ListAIConversationSuggestions(context.Context, *ListAIConversationSuggestionsRequest) (*ListAIConversationSuggestionsResponse, error)
	UpdateAIMessageFeedback(context.Context, *UpdateAIMessageFeedbackRequest) (*UpdateAIMessageFeedbackResponse, error)
	UpdateAIConversationFeedback(context.Context, *UpdateAIConversationFeedbackRequest) (*UpdateAIConversationFeedbackResponse, error)
}

func NewOrganizationServiceGatewayClient(c gateway.Client) OrganizationServiceGatewayClient {
	return &organizationServiceGatewayClient{
		gwc: c,
	}
}

type organizationServiceGatewayClient struct {
	gwc gateway.Client
}

func (c *organizationServiceGatewayClient) ListAuthenticatedUserOrganizations(ctx context.Context, req *ListAuthenticatedUserOrganizationsRequest) (*ListAuthenticatedUserOrganizationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations")
	return gateway.DoRequest[ListAuthenticatedUserOrganizationsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetOrganization(ctx context.Context, req *GetOrganizationRequest) (*GetOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	q.Add("idType", req.IdType.String())
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetOrganizationPermissions(ctx context.Context, req *GetOrganizationPermissionsRequest) (*GetOrganizationPermissionsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/permissions")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetOrganizationPermissionsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateOrganization(ctx context.Context, req *CreateOrganizationRequest) (*CreateOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations")
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateOrganization(ctx context.Context, req *UpdateOrganizationRequest) (*UpdateOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/organizations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteOrganization(ctx context.Context, req *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListOrganizationMembers(ctx context.Context, req *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/members")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[ListOrganizationMembersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListOrganizationInvitees(ctx context.Context, req *ListOrganizationInviteesRequest) (*ListOrganizationInviteesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/invitees")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[ListOrganizationInviteesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetUserRoleInOrganization(ctx context.Context, req *GetUserRoleInOrganizationRequest) (*GetUserRoleInOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/role")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetUserRoleInOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) InviteMembers(ctx context.Context, req *InviteMembersRequest) (*InviteMembersResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/members/invite")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[InviteMembersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UninviteOrganizationMember(ctx context.Context, req *UninviteOrganizationMemberRequest) (*UninviteOrganizationMemberResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/members/uninvite")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UninviteOrganizationMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RemoveOrganizationMember(ctx context.Context, req *RemoveOrganizationMemberRequest) (*RemoveOrganizationMemberResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{id}/members/{member_id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("member_id", fmt.Sprintf("%v", req.MemberId))
	gwReq.SetBody(req)
	return gateway.DoRequest[RemoveOrganizationMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateOrganizationMemberRole(ctx context.Context, req *UpdateOrganizationMemberRoleRequest) (*UpdateOrganizationMemberRoleResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/organizations/{id}/members/{member_id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("member_id", fmt.Sprintf("%v", req.MemberId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOrganizationMemberRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) JoinOrganization(ctx context.Context, req *JoinOrganizationRequest) (*JoinOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/join")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[JoinOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RejectOrganization(ctx context.Context, req *RejectOrganizationRequest) (*RejectOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/reject")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[RejectOrganizationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListOrganizationAPIKeys(ctx context.Context, req *ListOrganizationAPIKeysRequest) (*ListOrganizationAPIKeysResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/apikeys")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[ListOrganizationAPIKeysResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateOrganizationAPIKey(ctx context.Context, req *CreateOrganizationAPIKeyRequest) (*CreateOrganizationAPIKeyResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/apikeys")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateOrganizationAPIKeyResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListWorkspaceAPIKeys(ctx context.Context, req *ListWorkspaceAPIKeysRequest) (*ListWorkspaceAPIKeysResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/workspaces/{workspace_id}/apikeys")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	return gateway.DoRequest[ListWorkspaceAPIKeysResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateWorkspaceAPIKey(ctx context.Context, req *CreateWorkspaceAPIKeyRequest) (*CreateWorkspaceAPIKeyResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/workspaces/{workspace_id}/apikeys")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateWorkspaceAPIKeyResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetAuditLogs(ctx context.Context, req *GetAuditLogsRequest) (*GetAuditLogsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/audit-logs")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	for _, v := range req.Filters.ActorId {
		q.Add("filters.actorId", fmt.Sprintf("%v", v))
	}
	if req.Filters.K8SResource != nil {
		for _, v := range req.Filters.K8SResource.ObjectName {
			q.Add("filters.k8sResource.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectKind {
			q.Add("filters.k8sResource.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectGroup {
			q.Add("filters.k8sResource.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentName {
			q.Add("filters.k8sResource.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentParentName {
			q.Add("filters.k8sResource.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentApplicationName {
			q.Add("filters.k8sResource.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.K8SResource.Enabled != nil {
			q.Add("filters.k8sResource.enabled", fmt.Sprintf("%v", *req.Filters.K8SResource.Enabled))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentKargoProjectName {
			q.Add("filters.k8sResource.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdApplication != nil {
		for _, v := range req.Filters.ArgocdApplication.ObjectName {
			q.Add("filters.argocdApplication.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectKind {
			q.Add("filters.argocdApplication.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectGroup {
			q.Add("filters.argocdApplication.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentName {
			q.Add("filters.argocdApplication.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentParentName {
			q.Add("filters.argocdApplication.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentApplicationName {
			q.Add("filters.argocdApplication.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdApplication.Enabled != nil {
			q.Add("filters.argocdApplication.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdApplication.Enabled))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentKargoProjectName {
			q.Add("filters.argocdApplication.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdCluster != nil {
		for _, v := range req.Filters.ArgocdCluster.ObjectName {
			q.Add("filters.argocdCluster.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectKind {
			q.Add("filters.argocdCluster.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectGroup {
			q.Add("filters.argocdCluster.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentName {
			q.Add("filters.argocdCluster.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentParentName {
			q.Add("filters.argocdCluster.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentApplicationName {
			q.Add("filters.argocdCluster.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdCluster.Enabled != nil {
			q.Add("filters.argocdCluster.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdCluster.Enabled))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentKargoProjectName {
			q.Add("filters.argocdCluster.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdInstance != nil {
		for _, v := range req.Filters.ArgocdInstance.ObjectName {
			q.Add("filters.argocdInstance.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectKind {
			q.Add("filters.argocdInstance.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectGroup {
			q.Add("filters.argocdInstance.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentName {
			q.Add("filters.argocdInstance.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentParentName {
			q.Add("filters.argocdInstance.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentApplicationName {
			q.Add("filters.argocdInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdInstance.Enabled != nil {
			q.Add("filters.argocdInstance.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdInstance.Enabled))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentKargoProjectName {
			q.Add("filters.argocdInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdProject != nil {
		for _, v := range req.Filters.ArgocdProject.ObjectName {
			q.Add("filters.argocdProject.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectKind {
			q.Add("filters.argocdProject.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectGroup {
			q.Add("filters.argocdProject.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentName {
			q.Add("filters.argocdProject.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentParentName {
			q.Add("filters.argocdProject.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentApplicationName {
			q.Add("filters.argocdProject.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdProject.Enabled != nil {
			q.Add("filters.argocdProject.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdProject.Enabled))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentKargoProjectName {
			q.Add("filters.argocdProject.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Member != nil {
		for _, v := range req.Filters.Member.ObjectName {
			q.Add("filters.member.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectKind {
			q.Add("filters.member.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectGroup {
			q.Add("filters.member.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentName {
			q.Add("filters.member.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentParentName {
			q.Add("filters.member.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentApplicationName {
			q.Add("filters.member.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Member.Enabled != nil {
			q.Add("filters.member.enabled", fmt.Sprintf("%v", *req.Filters.Member.Enabled))
		}
		for _, v := range req.Filters.Member.ObjectParentKargoProjectName {
			q.Add("filters.member.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.OrganizationInvite != nil {
		for _, v := range req.Filters.OrganizationInvite.ObjectName {
			q.Add("filters.organizationInvite.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectKind {
			q.Add("filters.organizationInvite.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectGroup {
			q.Add("filters.organizationInvite.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentName {
			q.Add("filters.organizationInvite.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentParentName {
			q.Add("filters.organizationInvite.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentApplicationName {
			q.Add("filters.organizationInvite.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.OrganizationInvite.Enabled != nil {
			q.Add("filters.organizationInvite.enabled", fmt.Sprintf("%v", *req.Filters.OrganizationInvite.Enabled))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentKargoProjectName {
			q.Add("filters.organizationInvite.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	for _, v := range req.Filters.Action {
		q.Add("filters.action", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Filters.ActorType {
		q.Add("filters.actorType", fmt.Sprintf("%v", v))
	}
	if req.Filters.StartTime != nil {
		q.Add("filters.startTime", fmt.Sprintf("%v", *req.Filters.StartTime))
	}
	if req.Filters.EndTime != nil {
		q.Add("filters.endTime", fmt.Sprintf("%v", *req.Filters.EndTime))
	}
	if req.Filters.Limit != nil {
		q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
	}
	if req.Filters.Offset != nil {
		q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
	}
	if req.Filters.KargoInstance != nil {
		for _, v := range req.Filters.KargoInstance.ObjectName {
			q.Add("filters.kargoInstance.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectKind {
			q.Add("filters.kargoInstance.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectGroup {
			q.Add("filters.kargoInstance.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentName {
			q.Add("filters.kargoInstance.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentParentName {
			q.Add("filters.kargoInstance.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentApplicationName {
			q.Add("filters.kargoInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoInstance.Enabled != nil {
			q.Add("filters.kargoInstance.enabled", fmt.Sprintf("%v", *req.Filters.KargoInstance.Enabled))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentKargoProjectName {
			q.Add("filters.kargoInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoAgent != nil {
		for _, v := range req.Filters.KargoAgent.ObjectName {
			q.Add("filters.kargoAgent.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectKind {
			q.Add("filters.kargoAgent.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectGroup {
			q.Add("filters.kargoAgent.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentName {
			q.Add("filters.kargoAgent.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentParentName {
			q.Add("filters.kargoAgent.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentApplicationName {
			q.Add("filters.kargoAgent.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoAgent.Enabled != nil {
			q.Add("filters.kargoAgent.enabled", fmt.Sprintf("%v", *req.Filters.KargoAgent.Enabled))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentKargoProjectName {
			q.Add("filters.kargoAgent.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoPromotion != nil {
		for _, v := range req.Filters.KargoPromotion.ObjectName {
			q.Add("filters.kargoPromotion.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectKind {
			q.Add("filters.kargoPromotion.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectGroup {
			q.Add("filters.kargoPromotion.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentName {
			q.Add("filters.kargoPromotion.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentParentName {
			q.Add("filters.kargoPromotion.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentApplicationName {
			q.Add("filters.kargoPromotion.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoPromotion.Enabled != nil {
			q.Add("filters.kargoPromotion.enabled", fmt.Sprintf("%v", *req.Filters.KargoPromotion.Enabled))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentKargoProjectName {
			q.Add("filters.kargoPromotion.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoFreight != nil {
		for _, v := range req.Filters.KargoFreight.ObjectName {
			q.Add("filters.kargoFreight.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectKind {
			q.Add("filters.kargoFreight.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectGroup {
			q.Add("filters.kargoFreight.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentName {
			q.Add("filters.kargoFreight.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentParentName {
			q.Add("filters.kargoFreight.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentApplicationName {
			q.Add("filters.kargoFreight.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoFreight.Enabled != nil {
			q.Add("filters.kargoFreight.enabled", fmt.Sprintf("%v", *req.Filters.KargoFreight.Enabled))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentKargoProjectName {
			q.Add("filters.kargoFreight.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.CustomRoles != nil {
		for _, v := range req.Filters.CustomRoles.ObjectName {
			q.Add("filters.customRoles.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectKind {
			q.Add("filters.customRoles.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectGroup {
			q.Add("filters.customRoles.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentName {
			q.Add("filters.customRoles.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentParentName {
			q.Add("filters.customRoles.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentApplicationName {
			q.Add("filters.customRoles.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.CustomRoles.Enabled != nil {
			q.Add("filters.customRoles.enabled", fmt.Sprintf("%v", *req.Filters.CustomRoles.Enabled))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentKargoProjectName {
			q.Add("filters.customRoles.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.NotificationCfg != nil {
		for _, v := range req.Filters.NotificationCfg.ObjectName {
			q.Add("filters.notificationCfg.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectKind {
			q.Add("filters.notificationCfg.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectGroup {
			q.Add("filters.notificationCfg.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentName {
			q.Add("filters.notificationCfg.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentParentName {
			q.Add("filters.notificationCfg.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentApplicationName {
			q.Add("filters.notificationCfg.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.NotificationCfg.Enabled != nil {
			q.Add("filters.notificationCfg.enabled", fmt.Sprintf("%v", *req.Filters.NotificationCfg.Enabled))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentKargoProjectName {
			q.Add("filters.notificationCfg.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ApiKeys != nil {
		for _, v := range req.Filters.ApiKeys.ObjectName {
			q.Add("filters.apiKeys.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectKind {
			q.Add("filters.apiKeys.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectGroup {
			q.Add("filters.apiKeys.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentName {
			q.Add("filters.apiKeys.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentParentName {
			q.Add("filters.apiKeys.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentApplicationName {
			q.Add("filters.apiKeys.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ApiKeys.Enabled != nil {
			q.Add("filters.apiKeys.enabled", fmt.Sprintf("%v", *req.Filters.ApiKeys.Enabled))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentKargoProjectName {
			q.Add("filters.apiKeys.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Addons != nil {
		for _, v := range req.Filters.Addons.ObjectName {
			q.Add("filters.addons.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectKind {
			q.Add("filters.addons.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectGroup {
			q.Add("filters.addons.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentName {
			q.Add("filters.addons.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentParentName {
			q.Add("filters.addons.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentApplicationName {
			q.Add("filters.addons.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Addons.Enabled != nil {
			q.Add("filters.addons.enabled", fmt.Sprintf("%v", *req.Filters.Addons.Enabled))
		}
		for _, v := range req.Filters.Addons.ObjectParentKargoProjectName {
			q.Add("filters.addons.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.AddonRepos != nil {
		for _, v := range req.Filters.AddonRepos.ObjectName {
			q.Add("filters.addonRepos.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectKind {
			q.Add("filters.addonRepos.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectGroup {
			q.Add("filters.addonRepos.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentName {
			q.Add("filters.addonRepos.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentParentName {
			q.Add("filters.addonRepos.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentApplicationName {
			q.Add("filters.addonRepos.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.AddonRepos.Enabled != nil {
			q.Add("filters.addonRepos.enabled", fmt.Sprintf("%v", *req.Filters.AddonRepos.Enabled))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentKargoProjectName {
			q.Add("filters.addonRepos.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.AddonMarketplaceInstall != nil {
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectName {
			q.Add("filters.addonMarketplaceInstall.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectKind {
			q.Add("filters.addonMarketplaceInstall.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectGroup {
			q.Add("filters.addonMarketplaceInstall.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentName {
			q.Add("filters.addonMarketplaceInstall.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentParentName {
			q.Add("filters.addonMarketplaceInstall.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentApplicationName {
			q.Add("filters.addonMarketplaceInstall.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.AddonMarketplaceInstall.Enabled != nil {
			q.Add("filters.addonMarketplaceInstall.enabled", fmt.Sprintf("%v", *req.Filters.AddonMarketplaceInstall.Enabled))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentKargoProjectName {
			q.Add("filters.addonMarketplaceInstall.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Workspace != nil {
		for _, v := range req.Filters.Workspace.ObjectName {
			q.Add("filters.workspace.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectKind {
			q.Add("filters.workspace.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectGroup {
			q.Add("filters.workspace.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentName {
			q.Add("filters.workspace.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentParentName {
			q.Add("filters.workspace.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentApplicationName {
			q.Add("filters.workspace.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Workspace.Enabled != nil {
			q.Add("filters.workspace.enabled", fmt.Sprintf("%v", *req.Filters.Workspace.Enabled))
		}
		for _, v := range req.Filters.Workspace.ObjectParentKargoProjectName {
			q.Add("filters.workspace.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.WorkspaceMember != nil {
		for _, v := range req.Filters.WorkspaceMember.ObjectName {
			q.Add("filters.workspaceMember.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectKind {
			q.Add("filters.workspaceMember.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectGroup {
			q.Add("filters.workspaceMember.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentName {
			q.Add("filters.workspaceMember.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentParentName {
			q.Add("filters.workspaceMember.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentApplicationName {
			q.Add("filters.workspaceMember.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.WorkspaceMember.Enabled != nil {
			q.Add("filters.workspaceMember.enabled", fmt.Sprintf("%v", *req.Filters.WorkspaceMember.Enabled))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentKargoProjectName {
			q.Add("filters.workspaceMember.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetAuditLogsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListAuditLogsArchives(ctx context.Context, req *ListAuditLogsArchivesRequest) (*ListAuditLogsArchivesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/audit-logs-archives")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	if req.Filters.StartDate != nil {
		q.Add("filters.startDate", fmt.Sprintf("%v", *req.Filters.StartDate))
	}
	if req.Filters.EndDate != nil {
		q.Add("filters.endDate", fmt.Sprintf("%v", *req.Filters.EndDate))
	}
	if req.Filters.Limit != nil {
		q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
	}
	if req.Filters.Offset != nil {
		q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListAuditLogsArchivesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetAuditLogsInCSV(ctx context.Context, req *GetAuditLogsInCSVRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/csv-audit-logs")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	for _, v := range req.Filters.ActorId {
		q.Add("filters.actorId", fmt.Sprintf("%v", v))
	}
	if req.Filters.K8SResource != nil {
		for _, v := range req.Filters.K8SResource.ObjectName {
			q.Add("filters.k8sResource.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectKind {
			q.Add("filters.k8sResource.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectGroup {
			q.Add("filters.k8sResource.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentName {
			q.Add("filters.k8sResource.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentParentName {
			q.Add("filters.k8sResource.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentApplicationName {
			q.Add("filters.k8sResource.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.K8SResource.Enabled != nil {
			q.Add("filters.k8sResource.enabled", fmt.Sprintf("%v", *req.Filters.K8SResource.Enabled))
		}
		for _, v := range req.Filters.K8SResource.ObjectParentKargoProjectName {
			q.Add("filters.k8sResource.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdApplication != nil {
		for _, v := range req.Filters.ArgocdApplication.ObjectName {
			q.Add("filters.argocdApplication.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectKind {
			q.Add("filters.argocdApplication.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectGroup {
			q.Add("filters.argocdApplication.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentName {
			q.Add("filters.argocdApplication.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentParentName {
			q.Add("filters.argocdApplication.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentApplicationName {
			q.Add("filters.argocdApplication.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdApplication.Enabled != nil {
			q.Add("filters.argocdApplication.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdApplication.Enabled))
		}
		for _, v := range req.Filters.ArgocdApplication.ObjectParentKargoProjectName {
			q.Add("filters.argocdApplication.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdCluster != nil {
		for _, v := range req.Filters.ArgocdCluster.ObjectName {
			q.Add("filters.argocdCluster.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectKind {
			q.Add("filters.argocdCluster.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectGroup {
			q.Add("filters.argocdCluster.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentName {
			q.Add("filters.argocdCluster.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentParentName {
			q.Add("filters.argocdCluster.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentApplicationName {
			q.Add("filters.argocdCluster.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdCluster.Enabled != nil {
			q.Add("filters.argocdCluster.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdCluster.Enabled))
		}
		for _, v := range req.Filters.ArgocdCluster.ObjectParentKargoProjectName {
			q.Add("filters.argocdCluster.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdInstance != nil {
		for _, v := range req.Filters.ArgocdInstance.ObjectName {
			q.Add("filters.argocdInstance.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectKind {
			q.Add("filters.argocdInstance.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectGroup {
			q.Add("filters.argocdInstance.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentName {
			q.Add("filters.argocdInstance.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentParentName {
			q.Add("filters.argocdInstance.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentApplicationName {
			q.Add("filters.argocdInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdInstance.Enabled != nil {
			q.Add("filters.argocdInstance.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdInstance.Enabled))
		}
		for _, v := range req.Filters.ArgocdInstance.ObjectParentKargoProjectName {
			q.Add("filters.argocdInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ArgocdProject != nil {
		for _, v := range req.Filters.ArgocdProject.ObjectName {
			q.Add("filters.argocdProject.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectKind {
			q.Add("filters.argocdProject.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectGroup {
			q.Add("filters.argocdProject.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentName {
			q.Add("filters.argocdProject.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentParentName {
			q.Add("filters.argocdProject.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentApplicationName {
			q.Add("filters.argocdProject.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ArgocdProject.Enabled != nil {
			q.Add("filters.argocdProject.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdProject.Enabled))
		}
		for _, v := range req.Filters.ArgocdProject.ObjectParentKargoProjectName {
			q.Add("filters.argocdProject.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Member != nil {
		for _, v := range req.Filters.Member.ObjectName {
			q.Add("filters.member.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectKind {
			q.Add("filters.member.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectGroup {
			q.Add("filters.member.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentName {
			q.Add("filters.member.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentParentName {
			q.Add("filters.member.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Member.ObjectParentApplicationName {
			q.Add("filters.member.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Member.Enabled != nil {
			q.Add("filters.member.enabled", fmt.Sprintf("%v", *req.Filters.Member.Enabled))
		}
		for _, v := range req.Filters.Member.ObjectParentKargoProjectName {
			q.Add("filters.member.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.OrganizationInvite != nil {
		for _, v := range req.Filters.OrganizationInvite.ObjectName {
			q.Add("filters.organizationInvite.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectKind {
			q.Add("filters.organizationInvite.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectGroup {
			q.Add("filters.organizationInvite.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentName {
			q.Add("filters.organizationInvite.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentParentName {
			q.Add("filters.organizationInvite.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentApplicationName {
			q.Add("filters.organizationInvite.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.OrganizationInvite.Enabled != nil {
			q.Add("filters.organizationInvite.enabled", fmt.Sprintf("%v", *req.Filters.OrganizationInvite.Enabled))
		}
		for _, v := range req.Filters.OrganizationInvite.ObjectParentKargoProjectName {
			q.Add("filters.organizationInvite.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	for _, v := range req.Filters.Action {
		q.Add("filters.action", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Filters.ActorType {
		q.Add("filters.actorType", fmt.Sprintf("%v", v))
	}
	if req.Filters.StartTime != nil {
		q.Add("filters.startTime", fmt.Sprintf("%v", *req.Filters.StartTime))
	}
	if req.Filters.EndTime != nil {
		q.Add("filters.endTime", fmt.Sprintf("%v", *req.Filters.EndTime))
	}
	if req.Filters.Limit != nil {
		q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
	}
	if req.Filters.Offset != nil {
		q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
	}
	if req.Filters.KargoInstance != nil {
		for _, v := range req.Filters.KargoInstance.ObjectName {
			q.Add("filters.kargoInstance.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectKind {
			q.Add("filters.kargoInstance.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectGroup {
			q.Add("filters.kargoInstance.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentName {
			q.Add("filters.kargoInstance.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentParentName {
			q.Add("filters.kargoInstance.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentApplicationName {
			q.Add("filters.kargoInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoInstance.Enabled != nil {
			q.Add("filters.kargoInstance.enabled", fmt.Sprintf("%v", *req.Filters.KargoInstance.Enabled))
		}
		for _, v := range req.Filters.KargoInstance.ObjectParentKargoProjectName {
			q.Add("filters.kargoInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoAgent != nil {
		for _, v := range req.Filters.KargoAgent.ObjectName {
			q.Add("filters.kargoAgent.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectKind {
			q.Add("filters.kargoAgent.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectGroup {
			q.Add("filters.kargoAgent.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentName {
			q.Add("filters.kargoAgent.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentParentName {
			q.Add("filters.kargoAgent.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentApplicationName {
			q.Add("filters.kargoAgent.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoAgent.Enabled != nil {
			q.Add("filters.kargoAgent.enabled", fmt.Sprintf("%v", *req.Filters.KargoAgent.Enabled))
		}
		for _, v := range req.Filters.KargoAgent.ObjectParentKargoProjectName {
			q.Add("filters.kargoAgent.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoPromotion != nil {
		for _, v := range req.Filters.KargoPromotion.ObjectName {
			q.Add("filters.kargoPromotion.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectKind {
			q.Add("filters.kargoPromotion.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectGroup {
			q.Add("filters.kargoPromotion.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentName {
			q.Add("filters.kargoPromotion.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentParentName {
			q.Add("filters.kargoPromotion.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentApplicationName {
			q.Add("filters.kargoPromotion.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoPromotion.Enabled != nil {
			q.Add("filters.kargoPromotion.enabled", fmt.Sprintf("%v", *req.Filters.KargoPromotion.Enabled))
		}
		for _, v := range req.Filters.KargoPromotion.ObjectParentKargoProjectName {
			q.Add("filters.kargoPromotion.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.KargoFreight != nil {
		for _, v := range req.Filters.KargoFreight.ObjectName {
			q.Add("filters.kargoFreight.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectKind {
			q.Add("filters.kargoFreight.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectGroup {
			q.Add("filters.kargoFreight.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentName {
			q.Add("filters.kargoFreight.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentParentName {
			q.Add("filters.kargoFreight.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentApplicationName {
			q.Add("filters.kargoFreight.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.KargoFreight.Enabled != nil {
			q.Add("filters.kargoFreight.enabled", fmt.Sprintf("%v", *req.Filters.KargoFreight.Enabled))
		}
		for _, v := range req.Filters.KargoFreight.ObjectParentKargoProjectName {
			q.Add("filters.kargoFreight.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.CustomRoles != nil {
		for _, v := range req.Filters.CustomRoles.ObjectName {
			q.Add("filters.customRoles.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectKind {
			q.Add("filters.customRoles.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectGroup {
			q.Add("filters.customRoles.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentName {
			q.Add("filters.customRoles.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentParentName {
			q.Add("filters.customRoles.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentApplicationName {
			q.Add("filters.customRoles.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.CustomRoles.Enabled != nil {
			q.Add("filters.customRoles.enabled", fmt.Sprintf("%v", *req.Filters.CustomRoles.Enabled))
		}
		for _, v := range req.Filters.CustomRoles.ObjectParentKargoProjectName {
			q.Add("filters.customRoles.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.NotificationCfg != nil {
		for _, v := range req.Filters.NotificationCfg.ObjectName {
			q.Add("filters.notificationCfg.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectKind {
			q.Add("filters.notificationCfg.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectGroup {
			q.Add("filters.notificationCfg.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentName {
			q.Add("filters.notificationCfg.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentParentName {
			q.Add("filters.notificationCfg.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentApplicationName {
			q.Add("filters.notificationCfg.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.NotificationCfg.Enabled != nil {
			q.Add("filters.notificationCfg.enabled", fmt.Sprintf("%v", *req.Filters.NotificationCfg.Enabled))
		}
		for _, v := range req.Filters.NotificationCfg.ObjectParentKargoProjectName {
			q.Add("filters.notificationCfg.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.ApiKeys != nil {
		for _, v := range req.Filters.ApiKeys.ObjectName {
			q.Add("filters.apiKeys.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectKind {
			q.Add("filters.apiKeys.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectGroup {
			q.Add("filters.apiKeys.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentName {
			q.Add("filters.apiKeys.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentParentName {
			q.Add("filters.apiKeys.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentApplicationName {
			q.Add("filters.apiKeys.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.ApiKeys.Enabled != nil {
			q.Add("filters.apiKeys.enabled", fmt.Sprintf("%v", *req.Filters.ApiKeys.Enabled))
		}
		for _, v := range req.Filters.ApiKeys.ObjectParentKargoProjectName {
			q.Add("filters.apiKeys.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Addons != nil {
		for _, v := range req.Filters.Addons.ObjectName {
			q.Add("filters.addons.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectKind {
			q.Add("filters.addons.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectGroup {
			q.Add("filters.addons.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentName {
			q.Add("filters.addons.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentParentName {
			q.Add("filters.addons.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Addons.ObjectParentApplicationName {
			q.Add("filters.addons.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Addons.Enabled != nil {
			q.Add("filters.addons.enabled", fmt.Sprintf("%v", *req.Filters.Addons.Enabled))
		}
		for _, v := range req.Filters.Addons.ObjectParentKargoProjectName {
			q.Add("filters.addons.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.AddonRepos != nil {
		for _, v := range req.Filters.AddonRepos.ObjectName {
			q.Add("filters.addonRepos.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectKind {
			q.Add("filters.addonRepos.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectGroup {
			q.Add("filters.addonRepos.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentName {
			q.Add("filters.addonRepos.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentParentName {
			q.Add("filters.addonRepos.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentApplicationName {
			q.Add("filters.addonRepos.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.AddonRepos.Enabled != nil {
			q.Add("filters.addonRepos.enabled", fmt.Sprintf("%v", *req.Filters.AddonRepos.Enabled))
		}
		for _, v := range req.Filters.AddonRepos.ObjectParentKargoProjectName {
			q.Add("filters.addonRepos.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.AddonMarketplaceInstall != nil {
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectName {
			q.Add("filters.addonMarketplaceInstall.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectKind {
			q.Add("filters.addonMarketplaceInstall.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectGroup {
			q.Add("filters.addonMarketplaceInstall.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentName {
			q.Add("filters.addonMarketplaceInstall.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentParentName {
			q.Add("filters.addonMarketplaceInstall.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentApplicationName {
			q.Add("filters.addonMarketplaceInstall.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.AddonMarketplaceInstall.Enabled != nil {
			q.Add("filters.addonMarketplaceInstall.enabled", fmt.Sprintf("%v", *req.Filters.AddonMarketplaceInstall.Enabled))
		}
		for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentKargoProjectName {
			q.Add("filters.addonMarketplaceInstall.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.Workspace != nil {
		for _, v := range req.Filters.Workspace.ObjectName {
			q.Add("filters.workspace.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectKind {
			q.Add("filters.workspace.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectGroup {
			q.Add("filters.workspace.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentName {
			q.Add("filters.workspace.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentParentName {
			q.Add("filters.workspace.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.Workspace.ObjectParentApplicationName {
			q.Add("filters.workspace.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.Workspace.Enabled != nil {
			q.Add("filters.workspace.enabled", fmt.Sprintf("%v", *req.Filters.Workspace.Enabled))
		}
		for _, v := range req.Filters.Workspace.ObjectParentKargoProjectName {
			q.Add("filters.workspace.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	if req.Filters.WorkspaceMember != nil {
		for _, v := range req.Filters.WorkspaceMember.ObjectName {
			q.Add("filters.workspaceMember.objectName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectKind {
			q.Add("filters.workspaceMember.objectKind", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectGroup {
			q.Add("filters.workspaceMember.objectGroup", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentName {
			q.Add("filters.workspaceMember.objectParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentParentName {
			q.Add("filters.workspaceMember.objectParentParentName", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentApplicationName {
			q.Add("filters.workspaceMember.objectParentApplicationName", fmt.Sprintf("%v", v))
		}
		if req.Filters.WorkspaceMember.Enabled != nil {
			q.Add("filters.workspaceMember.enabled", fmt.Sprintf("%v", *req.Filters.WorkspaceMember.Enabled))
		}
		for _, v := range req.Filters.WorkspaceMember.ObjectParentKargoProjectName {
			q.Add("filters.workspaceMember.objectParentKargoProjectName", fmt.Sprintf("%v", v))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) GetCustomerDetails(ctx context.Context, req *GetCustomerDetailsRequest) (*GetCustomerDetailsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/billing/customer")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetCustomerDetailsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateBillingDetails(ctx context.Context, req *UpdateBillingDetailsRequest) (*UpdateBillingDetailsResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/organizations/{id}/billing")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateBillingDetailsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) BillingCheckout(ctx context.Context, req *BillingCheckoutRequest) (*BillingCheckoutResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{id}/checkout")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[BillingCheckoutResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateSubscription(ctx context.Context, req *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/organizations/{id}/billing/subscription")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateSubscriptionResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListAvailablePlans(ctx context.Context, req *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/billing/plans")
	q := url.Values{}
	q.Add("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListAvailablePlansResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetAvailableAddons(ctx context.Context, req *GetAvailableAddonsRequest) (*GetAvailableAddonsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/billing/{plan}/addons")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("plan", fmt.Sprintf("%v", req.Plan))
	return gateway.DoRequest[GetAvailableAddonsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetSSOConfiguration(ctx context.Context, req *GetSSOConfigurationRequest) (*GetSSOConfigurationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/sso/configuration")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetSSOConfigurationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) EnsureSSOConfiguration(ctx context.Context, req *EnsureSSOConfigurationRequest) (*EnsureSSOConfigurationResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/organizations/{id}/sso/configuration")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[EnsureSSOConfigurationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteSSOConfiguration(ctx context.Context, req *DeleteSSOConfigurationRequest) (*DeleteSSOConfigurationResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{id}/sso/configuration")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteSSOConfigurationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetFeatureStatuses(ctx context.Context, req *GetFeatureStatusesRequest) (*GetFeatureStatusesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/feature-statuses")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetFeatureStatusesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetOIDCMap(ctx context.Context, req *GetOIDCMapRequest) (*GetOIDCMapResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{id}/oidc-map")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetOIDCMapResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateOIDCMap(ctx context.Context, req *UpdateOIDCMapRequest) (*UpdateOIDCMapResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/organizations/{id}/oidc-map")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOIDCMapResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetTeamOIDCMap(ctx context.Context, req *GetTeamOIDCMapRequest) (*GetTeamOIDCMapResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/teams-oidc-map")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[GetTeamOIDCMapResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateTeamOIDCMap(ctx context.Context, req *UpdateTeamOIDCMapRequest) (*UpdateTeamOIDCMapResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/organizations/{organization_id}/teams-oidc-map")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateTeamOIDCMapResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateCustomRole(ctx context.Context, req *CreateCustomRoleRequest) (*CreateCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{organization_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateCustomRole(ctx context.Context, req *UpdateCustomRoleRequest) (*UpdateCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/organizations/{organization_id}/custom-roles/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetCustomRole(ctx context.Context, req *GetCustomRoleRequest) (*GetCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/custom-roles/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListCustomRoles(ctx context.Context, req *ListCustomRolesRequest) (*ListCustomRolesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListCustomRolesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteCustomRole(ctx context.Context, req *DeleteCustomRoleRequest) (*DeleteCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{organization_id}/custom-roles/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateWorkspaceCustomRole(ctx context.Context, req *CreateWorkspaceCustomRoleRequest) (*CreateWorkspaceCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateWorkspaceCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateWorkspaceCustomRole(ctx context.Context, req *UpdateWorkspaceCustomRoleRequest) (*UpdateWorkspaceCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateWorkspaceCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetWorkspaceCustomRole(ctx context.Context, req *GetWorkspaceCustomRoleRequest) (*GetWorkspaceCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetWorkspaceCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListWorkspaceCustomRoles(ctx context.Context, req *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspaceCustomRolesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteWorkspaceCustomRole(ctx context.Context, req *DeleteWorkspaceCustomRoleRequest) (*DeleteWorkspaceCustomRoleResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteWorkspaceCustomRoleResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateTeam(ctx context.Context, req *CreateTeamRequest) (*CreateTeamResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/teams")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateTeamResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateTeam(ctx context.Context, req *UpdateTeamRequest) (*UpdateTeamResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/teams/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateTeamResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetTeam(ctx context.Context, req *GetTeamRequest) (*GetTeamResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/teams/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	return gateway.DoRequest[GetTeamResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListTeams(ctx context.Context, req *ListTeamsRequest) (*ListTeamsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/teams")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListTeamsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteTeam(ctx context.Context, req *DeleteTeamRequest) (*DeleteTeamResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/teams/{name}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("name", fmt.Sprintf("%v", req.Name))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteTeamResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) AddTeamMember(ctx context.Context, req *AddTeamMemberRequest) (*AddTeamMemberResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/teams/{team_name}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("team_name", fmt.Sprintf("%v", req.TeamName))
	gwReq.SetBody(req)
	return gateway.DoRequest[AddTeamMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetTeamMember(ctx context.Context, req *GetTeamMemberRequest) (*GetTeamMemberResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/teams/{team_name}/members/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("team_name", fmt.Sprintf("%v", req.TeamName))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetTeamMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListTeamMembers(ctx context.Context, req *ListTeamMembersRequest) (*ListTeamMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/teams/{team_name}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("team_name", fmt.Sprintf("%v", req.TeamName))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListTeamMembersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RemoveTeamMember(ctx context.Context, req *RemoveTeamMemberRequest) (*RemoveTeamMemberResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/teams/{team_name}/members/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("team_name", fmt.Sprintf("%v", req.TeamName))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[RemoveTeamMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateArgocdInstancesQuota(ctx context.Context, req *UpdateArgocdInstancesQuotaRequest) (*UpdateArgocdInstancesQuotaResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/instances/quota")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateArgocdInstancesQuotaResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListArgocdInstancesQuota(ctx context.Context, req *ListArgocdInstancesQuotaRequest) (*ListArgocdInstancesQuotaResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/instances/quota")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListArgocdInstancesQuotaResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateKargoInstancesQuota(ctx context.Context, req *UpdateKargoInstancesQuotaRequest) (*UpdateKargoInstancesQuotaResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/kargo-instances/quota")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateKargoInstancesQuotaResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKargoInstancesQuota(ctx context.Context, req *ListKargoInstancesQuotaRequest) (*ListKargoInstancesQuotaResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/kargo-instances/quota")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListKargoInstancesQuotaResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateWorkspace(ctx context.Context, req *CreateWorkspaceRequest) (*CreateWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateWorkspaceResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListWorkspaces(ctx context.Context, req *ListWorkspacesRequest) (*ListWorkspacesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspacesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetWorkspace(ctx context.Context, req *GetWorkspaceRequest) (*GetWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetWorkspaceResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateWorkspace(ctx context.Context, req *UpdateWorkspaceRequest) (*UpdateWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/workspaces/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateWorkspaceResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteWorkspace(ctx context.Context, req *DeleteWorkspaceRequest) (*DeleteWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteWorkspaceResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) AddWorkspaceMember(ctx context.Context, req *AddWorkspaceMemberRequest) (*AddWorkspaceMemberResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/member")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[AddWorkspaceMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListWorkspaceMembers(ctx context.Context, req *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspaceMembersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateWorkspaceMembers(ctx context.Context, req *UpdateWorkspaceMembersRequest) (*UpdateWorkspaceMembersResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateWorkspaceMembersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetWorkspaceMember(ctx context.Context, req *GetWorkspaceMemberRequest) (*GetWorkspaceMemberResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/members/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetWorkspaceMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateWorkspaceMember(ctx context.Context, req *UpdateWorkspaceMemberRequest) (*UpdateWorkspaceMemberResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/members/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateWorkspaceMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RemoveWorkspaceMember(ctx context.Context, req *RemoveWorkspaceMemberRequest) (*RemoveWorkspaceMemberResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/workspaces/{workspace_id}/members/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[RemoveWorkspaceMemberResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CancelSubscription(ctx context.Context, req *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{organization_id}/billing/subscription/cancel")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CancelSubscriptionResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesResourceTypes(ctx context.Context, req *ListKubernetesResourceTypesRequest) (*ListKubernetesResourceTypesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resource-types")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesResourceTypesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesResources(ctx context.Context, req *ListKubernetesResourcesRequest) (*ListKubernetesResourcesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resources")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Namespaces {
		q.Add("namespaces", fmt.Sprintf("%v", v))
	}
	q.Add("kind", fmt.Sprintf("%v", req.Kind))
	q.Add("group", fmt.Sprintf("%v", req.Group))
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.OwnerId != nil {
		q.Add("ownerId", fmt.Sprintf("%v", *req.OwnerId))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	q.Add("version", fmt.Sprintf("%v", req.Version))
	if req.Where != nil {
		q.Add("where", fmt.Sprintf("%v", *req.Where))
	}
	if req.HasDeletionTimestamp != nil {
		q.Add("hasDeletionTimestamp", fmt.Sprintf("%v", *req.HasDeletionTimestamp))
	}
	if req.TreeView != nil {
		q.Add("treeView", fmt.Sprintf("%v", *req.TreeView))
	}
	if req.Name != nil {
		q.Add("name", fmt.Sprintf("%v", *req.Name))
	}
	if req.TreeViewNameContains != nil {
		q.Add("treeViewNameContains", fmt.Sprintf("%v", *req.TreeViewNameContains))
	}
	for _, v := range req.TreeViewResourceKinds {
		q.Add("treeViewResourceKinds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.TreeViewHealthStatuses {
		q.Add("treeViewHealthStatuses", v.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesResourcesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesResourcesToCSV(ctx context.Context, req *ListKubernetesResourcesRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/resources-csv")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Namespaces {
		q.Add("namespaces", fmt.Sprintf("%v", v))
	}
	q.Add("kind", fmt.Sprintf("%v", req.Kind))
	q.Add("group", fmt.Sprintf("%v", req.Group))
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.OwnerId != nil {
		q.Add("ownerId", fmt.Sprintf("%v", *req.OwnerId))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	q.Add("version", fmt.Sprintf("%v", req.Version))
	if req.Where != nil {
		q.Add("where", fmt.Sprintf("%v", *req.Where))
	}
	if req.HasDeletionTimestamp != nil {
		q.Add("hasDeletionTimestamp", fmt.Sprintf("%v", *req.HasDeletionTimestamp))
	}
	if req.TreeView != nil {
		q.Add("treeView", fmt.Sprintf("%v", *req.TreeView))
	}
	if req.Name != nil {
		q.Add("name", fmt.Sprintf("%v", *req.Name))
	}
	if req.TreeViewNameContains != nil {
		q.Add("treeViewNameContains", fmt.Sprintf("%v", *req.TreeViewNameContains))
	}
	for _, v := range req.TreeViewResourceKinds {
		q.Add("treeViewResourceKinds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.TreeViewHealthStatuses {
		q.Add("treeViewHealthStatuses", v.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) SpotlightSearchKubernetesResources(ctx context.Context, req *SpotlightSearchKubernetesResourcesRequest) (*SpotlightSearchKubernetesResourcesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/spotlight-search")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	if req.Query != nil {
		q.Add("query", fmt.Sprintf("%v", *req.Query))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.AiSearch != nil {
		q.Add("aiSearch", fmt.Sprintf("%v", *req.AiSearch))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[SpotlightSearchKubernetesResourcesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesResourceDetail(ctx context.Context, req *GetKubernetesResourceDetailRequest) (*GetKubernetesResourceDetailResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/detail")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesResourceDetailResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesContainer(ctx context.Context, req *GetKubernetesContainerRequest) (*GetKubernetesContainerResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/containers/{container_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("container_id", fmt.Sprintf("%v", req.ContainerId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesContainerResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesNamespaces(ctx context.Context, req *ListKubernetesNamespacesRequest) (*ListKubernetesNamespacesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/namespaces")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.NodeName != nil {
		q.Add("nodeName", fmt.Sprintf("%v", *req.NodeName))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesNamespacesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesImages(ctx context.Context, req *ListKubernetesImagesRequest) (*ListKubernetesImagesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/images")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.Digest != nil {
		q.Add("digest", fmt.Sprintf("%v", *req.Digest))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesImagesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesImagesToCSV(ctx context.Context, req *ListKubernetesImagesRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/images-csv")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.Digest != nil {
		q.Add("digest", fmt.Sprintf("%v", *req.Digest))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesImageDetail(ctx context.Context, req *GetKubernetesImageDetailRequest) (*GetKubernetesImageDetailResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/images/detail")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	q.Add("imageId", fmt.Sprintf("%v", req.ImageId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesImageDetailResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesContainers(ctx context.Context, req *ListKubernetesContainersRequest) (*ListKubernetesContainersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/containers")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.PodId != nil {
		q.Add("podId", fmt.Sprintf("%v", *req.PodId))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Image != nil {
		q.Add("image", fmt.Sprintf("%v", *req.Image))
	}
	if req.ImageTag != nil {
		q.Add("imageTag", fmt.Sprintf("%v", *req.ImageTag))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.Status != nil {
		q.Add("status", req.Status.String())
	}
	if req.Type != nil {
		q.Add("type", req.Type.String())
	}
	if req.ImageDigest != nil {
		q.Add("imageDigest", fmt.Sprintf("%v", *req.ImageDigest))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesContainersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesContainersToCSV(ctx context.Context, req *ListKubernetesContainersRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/containers-csv")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.PodId != nil {
		q.Add("podId", fmt.Sprintf("%v", *req.PodId))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Image != nil {
		q.Add("image", fmt.Sprintf("%v", *req.Image))
	}
	if req.ImageTag != nil {
		q.Add("imageTag", fmt.Sprintf("%v", *req.ImageTag))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.NameContains != nil {
		q.Add("nameContains", fmt.Sprintf("%v", *req.NameContains))
	}
	if req.Status != nil {
		q.Add("status", req.Status.String())
	}
	if req.Type != nil {
		q.Add("type", req.Type.String())
	}
	if req.ImageDigest != nil {
		q.Add("imageDigest", fmt.Sprintf("%v", *req.ImageDigest))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesEnabledClusters(ctx context.Context, req *ListKubernetesEnabledClustersRequest) (*ListKubernetesEnabledClustersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/clusters")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	if req.HasDeprecatedApis != nil {
		q.Add("hasDeprecatedApis", fmt.Sprintf("%v", *req.HasDeprecatedApis))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesEnabledClustersResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesManifest(ctx context.Context, req *GetKubernetesManifestRequest) (*GetKubernetesManifestResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/manifest")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesManifestResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteKubernetesResource(ctx context.Context, req *DeleteKubernetesResourceRequest) (*DeleteKubernetesResourceResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/k8s/instances/{instance_id}/clusters/{cluster_id}/resources/{resource_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteKubernetesResourceResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesLogs(ctx context.Context, req *GetKubernetesLogsRequest) (<-chan *GetKubernetesLogsResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/resources/{resource_id}/logs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	if req.SinceSeconds != nil {
		q.Add("sinceSeconds", fmt.Sprintf("%v", *req.SinceSeconds))
	}
	if req.SinceTime != nil {
		q.Add("sinceTime.seconds", fmt.Sprintf("%v", req.SinceTime.Seconds))
		q.Add("sinceTime.nanos", fmt.Sprintf("%v", req.SinceTime.Nanos))
	}
	if req.TailLines != nil {
		q.Add("tailLines", fmt.Sprintf("%v", *req.TailLines))
	}
	if req.Follow != nil {
		q.Add("follow", fmt.Sprintf("%v", *req.Follow))
	}
	if req.UntilTime != nil {
		q.Add("untilTime.seconds", fmt.Sprintf("%v", req.UntilTime.Seconds))
		q.Add("untilTime.nanos", fmt.Sprintf("%v", req.UntilTime.Nanos))
	}
	if req.Previous != nil {
		q.Add("previous", fmt.Sprintf("%v", *req.Previous))
	}
	if req.Filter != nil {
		q.Add("filter", fmt.Sprintf("%v", *req.Filter))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[GetKubernetesLogsResponse](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesEvents(ctx context.Context, req *GetKubernetesEventsRequest) (*GetKubernetesEventsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/events")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesEventsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesAuditLogs(ctx context.Context, req *ListKubernetesAuditLogsRequest) (*ListKubernetesAuditLogsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/audit-logs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	if req.StartTime != nil {
		q.Add("startTime.seconds", fmt.Sprintf("%v", req.StartTime.Seconds))
		q.Add("startTime.nanos", fmt.Sprintf("%v", req.StartTime.Nanos))
	}
	if req.EndTime != nil {
		q.Add("endTime.seconds", fmt.Sprintf("%v", req.EndTime.Seconds))
		q.Add("endTime.nanos", fmt.Sprintf("%v", req.EndTime.Nanos))
	}
	for _, v := range req.ActorId {
		q.Add("actorId", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Action {
		q.Add("action", fmt.Sprintf("%v", v))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesAuditLogsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesNodes(ctx context.Context, req *ListKubernetesNodesRequest) (*ListKubernetesNodesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/nodes")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.GroupBy {
		q.Add("groupBy", v.String())
	}
	if req.Filler != nil {
		q.Add("filler", req.Filler.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesNodesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesNode(ctx context.Context, req *GetKubernetesNodeRequest) (*GetKubernetesNodeResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/nodes/{node_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("node_id", fmt.Sprintf("%v", req.NodeId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesNodeResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesNamespacesDetails(ctx context.Context, req *ListKubernetesNamespacesDetailsRequest) (*ListKubernetesNamespacesDetailsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/namespaces-details")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.GroupBy {
		q.Add("groupBy", v.String())
	}
	if req.Filler != nil {
		q.Add("filler", req.Filler.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesNamespacesDetailsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesNamespaceDetail(ctx context.Context, req *GetKubernetesNamespaceDetailRequest) (*GetKubernetesNamespaceDetailResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/namespaces-details/{namespace_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("namespace_id", fmt.Sprintf("%v", req.NamespaceId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesNamespaceDetailResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesClusterDetail(ctx context.Context, req *GetKubernetesClusterDetailRequest) (*GetKubernetesClusterDetailResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/instances/{instance_id}/clusters/{cluster_id}/detail")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	return gateway.DoRequest[GetKubernetesClusterDetailResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesSummary(ctx context.Context, req *GetKubernetesSummaryRequest) (*GetKubernetesSummaryResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/summary")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesSummaryResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesPods(ctx context.Context, req *ListKubernetesPodsRequest) (*ListKubernetesPodsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/pods")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.GroupBy {
		q.Add("groupBy", v.String())
	}
	if req.Filler != nil {
		q.Add("filler", req.Filler.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesPodsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesPod(ctx context.Context, req *GetKubernetesPodRequest) (*GetKubernetesPodResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/pods/{pod_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("pod_id", fmt.Sprintf("%v", req.PodId))
	q := url.Values{}
	q.Add("instanceId", fmt.Sprintf("%v", req.InstanceId))
	q.Add("clusterId", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubernetesPodResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesDeprecatedAPIs(ctx context.Context, req *ListKubernetesDeprecatedAPIsRequest) (*ListKubernetesDeprecatedAPIsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/deprecated-apis")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Group != nil {
		q.Add("group", fmt.Sprintf("%v", *req.Group))
	}
	if req.Version != nil {
		q.Add("version", fmt.Sprintf("%v", *req.Version))
	}
	if req.Kind != nil {
		q.Add("kind", fmt.Sprintf("%v", *req.Kind))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.ApiVersionContains != nil {
		q.Add("apiVersionContains", fmt.Sprintf("%v", *req.ApiVersionContains))
	}
	if req.Severity != nil {
		q.Add("severity", req.Severity.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesDeprecatedAPIsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesDeprecatedAPIsToCSV(ctx context.Context, req *ListKubernetesDeprecatedAPIsRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/deprecated-apis-csv")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	if req.OrderBy != nil {
		q.Add("orderBy", fmt.Sprintf("%v", *req.OrderBy))
	}
	if req.Group != nil {
		q.Add("group", fmt.Sprintf("%v", *req.Group))
	}
	if req.Version != nil {
		q.Add("version", fmt.Sprintf("%v", *req.Version))
	}
	if req.Kind != nil {
		q.Add("kind", fmt.Sprintf("%v", *req.Kind))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.ApiVersionContains != nil {
		q.Add("apiVersionContains", fmt.Sprintf("%v", *req.ApiVersionContains))
	}
	if req.Severity != nil {
		q.Add("severity", req.Severity.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubernetesAssistantSuggestion(ctx context.Context, req *GetKubernetesAssistantSuggestionRequest) (*GetKubernetesAssistantSuggestionResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/assistant-suggestion")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[GetKubernetesAssistantSuggestionResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ResolveKubernetesAssistantConversation(ctx context.Context, req *ResolveKubernetesAssistantConversationRequest) (*ResolveKubernetesAssistantConversationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/k8s/resources/{resource_id}/resolve-assistant-conversation")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("resource_id", fmt.Sprintf("%v", req.ResourceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[ResolveKubernetesAssistantConversationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesTimelineEvents(ctx context.Context, req *ListKubernetesTimelineEventsRequest) (*ListKubernetesTimelineEventsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/timeline-events")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Namespaces {
		q.Add("namespaces", fmt.Sprintf("%v", v))
	}
	for _, v := range req.TimelineResourceIds {
		q.Add("timelineResourceIds", fmt.Sprintf("%v", v))
	}
	if req.StartTime != nil {
		q.Add("startTime.seconds", fmt.Sprintf("%v", req.StartTime.Seconds))
		q.Add("startTime.nanos", fmt.Sprintf("%v", req.StartTime.Nanos))
	}
	if req.EndTime != nil {
		q.Add("endTime.seconds", fmt.Sprintf("%v", req.EndTime.Seconds))
		q.Add("endTime.nanos", fmt.Sprintf("%v", req.EndTime.Nanos))
	}
	for _, v := range req.ApplicationNames {
		q.Add("applicationNames", fmt.Sprintf("%v", v))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesTimelineEventsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListKubernetesTimelineResources(ctx context.Context, req *ListKubernetesTimelineResourcesRequest) (*ListKubernetesTimelineResourcesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/timeline-resources")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	for _, v := range req.ClusterIds {
		q.Add("clusterIds", fmt.Sprintf("%v", v))
	}
	for _, v := range req.Namespaces {
		q.Add("namespaces", fmt.Sprintf("%v", v))
	}
	if req.Group != nil {
		q.Add("group", fmt.Sprintf("%v", *req.Group))
	}
	if req.Kind != nil {
		q.Add("kind", fmt.Sprintf("%v", *req.Kind))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKubernetesTimelineResourcesResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubeVisionUsage(ctx context.Context, req *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/k8s/usage")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.StartTime != nil {
		q.Add("startTime.seconds", fmt.Sprintf("%v", req.StartTime.Seconds))
		q.Add("startTime.nanos", fmt.Sprintf("%v", req.StartTime.Nanos))
	}
	if req.EndTime != nil {
		q.Add("endTime.seconds", fmt.Sprintf("%v", req.EndTime.Seconds))
		q.Add("endTime.nanos", fmt.Sprintf("%v", req.EndTime.Nanos))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubeVisionUsageResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetKubeVisionUsageToCSV(ctx context.Context, req *GetKubeVisionUsageRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/k8s/usage-csv")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.StartTime != nil {
		q.Add("startTime.seconds", fmt.Sprintf("%v", req.StartTime.Seconds))
		q.Add("startTime.nanos", fmt.Sprintf("%v", req.StartTime.Nanos))
	}
	if req.EndTime != nil {
		q.Add("endTime.seconds", fmt.Sprintf("%v", req.EndTime.Seconds))
		q.Add("endTime.nanos", fmt.Sprintf("%v", req.EndTime.Nanos))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) ListNotificationConfigs(ctx context.Context, req *ListNotificationConfigsRequest) (*ListNotificationConfigsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/notification-configs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	for _, v := range req.DeliveryMethods {
		q.Add("deliveryMethods", v.String())
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListNotificationConfigsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetNotificationConfig(ctx context.Context, req *GetNotificationConfigRequest) (*GetNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/notification-configs/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetNotificationConfigResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateNotificationConfig(ctx context.Context, req *CreateNotificationConfigRequest) (*CreateNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/notification-configs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateNotificationConfigResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateNotificationConfig(ctx context.Context, req *UpdateNotificationConfigRequest) (*UpdateNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/notification-configs/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateNotificationConfigResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteNotificationConfig(ctx context.Context, req *DeleteNotificationConfigRequest) (*DeleteNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/notification-configs/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteNotificationConfigResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListNotificationDeliveryHistory(ctx context.Context, req *ListNotificationDeliveryHistoryRequest) (*ListNotificationDeliveryHistoryResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/notification-configs/{id}/delivery-history")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListNotificationDeliveryHistoryResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetNotificationDeliveryHistoryDetail(ctx context.Context, req *GetNotificationDeliveryHistoryDetailRequest) (*GetNotificationDeliveryHistoryDetailResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/notification-configs/{config_id}/delivery-history/{id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("config_id", fmt.Sprintf("%v", req.ConfigId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetNotificationDeliveryHistoryDetailResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) PingNotificationConfig(ctx context.Context, req *PingNotificationConfigRequest) (*PingNotificationConfigResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/notification-configs/{id}/ping")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PingNotificationConfigResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RedeliverNotification(ctx context.Context, req *RedeliverNotificationRequest) (*RedeliverNotificationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/notification-configs/{config_id}/notifications/{id}/redeliver")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("config_id", fmt.Sprintf("%v", req.ConfigId))
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[RedeliverNotificationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListOrganizationDomains(ctx context.Context, req *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/domains")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListOrganizationDomainsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteOrganizationDomain(ctx context.Context, req *DeleteOrganizationDomainRequest) (*DeleteOrganizationDomainResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/organizations/{organization_id}/domains/{domain}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("domain", fmt.Sprintf("%v", req.Domain))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteOrganizationDomainResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) VerifyOrganizationDomains(ctx context.Context, req *VerifyOrganizationDomainsRequest) (*VerifyOrganizationDomainsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{organization_id}/domains")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[VerifyOrganizationDomainsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateAIConversation(ctx context.Context, req *CreateAIConversationRequest) (*CreateAIConversationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/ai/conversations")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateAIConversationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateIncident(ctx context.Context, req *CreateIncidentRequest) (*CreateIncidentResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/ai/incidents")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req.Body)
	return gateway.DoRequest[CreateIncidentResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateAIConversation(ctx context.Context, req *UpdateAIConversationRequest) (*UpdateAIConversationResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/orgs/{organization_id}/ai/conversations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateAIConversationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) DeleteAIConversation(ctx context.Context, req *DeleteAIConversationRequest) (*DeleteAIConversationResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/orgs/{organization_id}/ai/conversations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteAIConversationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetAIConversation(ctx context.Context, req *GetAIConversationRequest) (*GetAIConversationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/ai/conversations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetAIConversationResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) GetAIConversationStream(ctx context.Context, req *GetAIConversationStreamRequest) (<-chan *GetAIConversationStreamResponse, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/stream/orgs/{organization_id}/ai/conversations/{id}/messages")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[GetAIConversationStreamResponse](ctx, c.gwc, gwReq)
}

func (c *organizationServiceGatewayClient) ListAIConversations(ctx context.Context, req *ListAIConversationsRequest) (*ListAIConversationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/orgs/{organization_id}/ai/conversations")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	if req.IncidentOnly != nil {
		q.Add("incidentOnly", fmt.Sprintf("%v", *req.IncidentOnly))
	}
	if req.IncidentStatus != nil {
		q.Add("incidentStatus", req.IncidentStatus.String())
	}
	if req.Application != nil {
		q.Add("application", fmt.Sprintf("%v", *req.Application))
	}
	if req.Namespace != nil {
		q.Add("namespace", fmt.Sprintf("%v", *req.Namespace))
	}
	if req.TitleContains != nil {
		q.Add("titleContains", fmt.Sprintf("%v", *req.TitleContains))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.ClusterId != nil {
		q.Add("clusterId", fmt.Sprintf("%v", *req.ClusterId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListAIConversationsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) CreateAIMessage(ctx context.Context, req *CreateAIMessageRequest) (*CreateAIMessageResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/ai/conversations/{conversation_id}/messages")
	gwReq.SetPathParam("conversation_id", fmt.Sprintf("%v", req.ConversationId))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[CreateAIMessageResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListUsersMFAStatus(ctx context.Context, req *ListUsersMFAStatusRequest) (*ListUsersMFAStatusResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/organizations/{organization_id}/users-mfa-status")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListUsersMFAStatusResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) RequestMFAReset(ctx context.Context, req *RequestMFAResetRequest) (*RequestMFAResetResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/organizations/{organization_id}/users/{email}/mfa/reset")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("email", fmt.Sprintf("%v", req.Email))
	gwReq.SetBody(req)
	return gateway.DoRequest[RequestMFAResetResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) ListAIConversationSuggestions(ctx context.Context, req *ListAIConversationSuggestionsRequest) (*ListAIConversationSuggestionsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/orgs/{organization_id}/ai/conversations/{conversation_id}/suggestions")
	gwReq.SetPathParam("conversation_id", fmt.Sprintf("%v", req.ConversationId))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[ListAIConversationSuggestionsResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateAIMessageFeedback(ctx context.Context, req *UpdateAIMessageFeedbackRequest) (*UpdateAIMessageFeedbackResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/ai/conversations/{conversation_id}/feedback")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("conversation_id", fmt.Sprintf("%v", req.ConversationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateAIMessageFeedbackResponse](ctx, gwReq)
}

func (c *organizationServiceGatewayClient) UpdateAIConversationFeedback(ctx context.Context, req *UpdateAIConversationFeedbackRequest) (*UpdateAIConversationFeedbackResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/orgs/{organization_id}/ai/conversations/{conversation_id}/conversation-feedback")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("conversation_id", fmt.Sprintf("%v", req.ConversationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateAIConversationFeedbackResponse](ctx, gwReq)
}
