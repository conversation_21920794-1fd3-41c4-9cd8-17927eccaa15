// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: organization/v1/organization.proto

package organizationv1

import (
	context "context"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OrganizationService_ListAuthenticatedUserOrganizations_FullMethodName     = "/akuity.organization.v1.OrganizationService/ListAuthenticatedUserOrganizations"
	OrganizationService_GetOrganization_FullMethodName                        = "/akuity.organization.v1.OrganizationService/GetOrganization"
	OrganizationService_GetOrganizationPermissions_FullMethodName             = "/akuity.organization.v1.OrganizationService/GetOrganizationPermissions"
	OrganizationService_CreateOrganization_FullMethodName                     = "/akuity.organization.v1.OrganizationService/CreateOrganization"
	OrganizationService_UpdateOrganization_FullMethodName                     = "/akuity.organization.v1.OrganizationService/UpdateOrganization"
	OrganizationService_DeleteOrganization_FullMethodName                     = "/akuity.organization.v1.OrganizationService/DeleteOrganization"
	OrganizationService_ListOrganizationMembers_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListOrganizationMembers"
	OrganizationService_ListOrganizationInvitees_FullMethodName               = "/akuity.organization.v1.OrganizationService/ListOrganizationInvitees"
	OrganizationService_GetUserRoleInOrganization_FullMethodName              = "/akuity.organization.v1.OrganizationService/GetUserRoleInOrganization"
	OrganizationService_InviteMembers_FullMethodName                          = "/akuity.organization.v1.OrganizationService/InviteMembers"
	OrganizationService_UninviteOrganizationMember_FullMethodName             = "/akuity.organization.v1.OrganizationService/UninviteOrganizationMember"
	OrganizationService_RemoveOrganizationMember_FullMethodName               = "/akuity.organization.v1.OrganizationService/RemoveOrganizationMember"
	OrganizationService_UpdateOrganizationMemberRole_FullMethodName           = "/akuity.organization.v1.OrganizationService/UpdateOrganizationMemberRole"
	OrganizationService_JoinOrganization_FullMethodName                       = "/akuity.organization.v1.OrganizationService/JoinOrganization"
	OrganizationService_RejectOrganization_FullMethodName                     = "/akuity.organization.v1.OrganizationService/RejectOrganization"
	OrganizationService_ListOrganizationAPIKeys_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListOrganizationAPIKeys"
	OrganizationService_CreateOrganizationAPIKey_FullMethodName               = "/akuity.organization.v1.OrganizationService/CreateOrganizationAPIKey"
	OrganizationService_ListWorkspaceAPIKeys_FullMethodName                   = "/akuity.organization.v1.OrganizationService/ListWorkspaceAPIKeys"
	OrganizationService_CreateWorkspaceAPIKey_FullMethodName                  = "/akuity.organization.v1.OrganizationService/CreateWorkspaceAPIKey"
	OrganizationService_GetAuditLogs_FullMethodName                           = "/akuity.organization.v1.OrganizationService/GetAuditLogs"
	OrganizationService_ListAuditLogsArchives_FullMethodName                  = "/akuity.organization.v1.OrganizationService/ListAuditLogsArchives"
	OrganizationService_GetAuditLogsInCSV_FullMethodName                      = "/akuity.organization.v1.OrganizationService/GetAuditLogsInCSV"
	OrganizationService_GetCustomerDetails_FullMethodName                     = "/akuity.organization.v1.OrganizationService/GetCustomerDetails"
	OrganizationService_UpdateBillingDetails_FullMethodName                   = "/akuity.organization.v1.OrganizationService/UpdateBillingDetails"
	OrganizationService_BillingCheckout_FullMethodName                        = "/akuity.organization.v1.OrganizationService/BillingCheckout"
	OrganizationService_UpdateSubscription_FullMethodName                     = "/akuity.organization.v1.OrganizationService/UpdateSubscription"
	OrganizationService_ListAvailablePlans_FullMethodName                     = "/akuity.organization.v1.OrganizationService/ListAvailablePlans"
	OrganizationService_GetAvailableAddons_FullMethodName                     = "/akuity.organization.v1.OrganizationService/GetAvailableAddons"
	OrganizationService_GetSSOConfiguration_FullMethodName                    = "/akuity.organization.v1.OrganizationService/GetSSOConfiguration"
	OrganizationService_EnsureSSOConfiguration_FullMethodName                 = "/akuity.organization.v1.OrganizationService/EnsureSSOConfiguration"
	OrganizationService_DeleteSSOConfiguration_FullMethodName                 = "/akuity.organization.v1.OrganizationService/DeleteSSOConfiguration"
	OrganizationService_GetFeatureStatuses_FullMethodName                     = "/akuity.organization.v1.OrganizationService/GetFeatureStatuses"
	OrganizationService_GetOIDCMap_FullMethodName                             = "/akuity.organization.v1.OrganizationService/GetOIDCMap"
	OrganizationService_UpdateOIDCMap_FullMethodName                          = "/akuity.organization.v1.OrganizationService/UpdateOIDCMap"
	OrganizationService_GetTeamOIDCMap_FullMethodName                         = "/akuity.organization.v1.OrganizationService/GetTeamOIDCMap"
	OrganizationService_UpdateTeamOIDCMap_FullMethodName                      = "/akuity.organization.v1.OrganizationService/UpdateTeamOIDCMap"
	OrganizationService_CreateCustomRole_FullMethodName                       = "/akuity.organization.v1.OrganizationService/CreateCustomRole"
	OrganizationService_UpdateCustomRole_FullMethodName                       = "/akuity.organization.v1.OrganizationService/UpdateCustomRole"
	OrganizationService_GetCustomRole_FullMethodName                          = "/akuity.organization.v1.OrganizationService/GetCustomRole"
	OrganizationService_ListCustomRoles_FullMethodName                        = "/akuity.organization.v1.OrganizationService/ListCustomRoles"
	OrganizationService_DeleteCustomRole_FullMethodName                       = "/akuity.organization.v1.OrganizationService/DeleteCustomRole"
	OrganizationService_CreateWorkspaceCustomRole_FullMethodName              = "/akuity.organization.v1.OrganizationService/CreateWorkspaceCustomRole"
	OrganizationService_UpdateWorkspaceCustomRole_FullMethodName              = "/akuity.organization.v1.OrganizationService/UpdateWorkspaceCustomRole"
	OrganizationService_GetWorkspaceCustomRole_FullMethodName                 = "/akuity.organization.v1.OrganizationService/GetWorkspaceCustomRole"
	OrganizationService_ListWorkspaceCustomRoles_FullMethodName               = "/akuity.organization.v1.OrganizationService/ListWorkspaceCustomRoles"
	OrganizationService_DeleteWorkspaceCustomRole_FullMethodName              = "/akuity.organization.v1.OrganizationService/DeleteWorkspaceCustomRole"
	OrganizationService_CreateTeam_FullMethodName                             = "/akuity.organization.v1.OrganizationService/CreateTeam"
	OrganizationService_UpdateTeam_FullMethodName                             = "/akuity.organization.v1.OrganizationService/UpdateTeam"
	OrganizationService_GetTeam_FullMethodName                                = "/akuity.organization.v1.OrganizationService/GetTeam"
	OrganizationService_ListTeams_FullMethodName                              = "/akuity.organization.v1.OrganizationService/ListTeams"
	OrganizationService_DeleteTeam_FullMethodName                             = "/akuity.organization.v1.OrganizationService/DeleteTeam"
	OrganizationService_AddTeamMember_FullMethodName                          = "/akuity.organization.v1.OrganizationService/AddTeamMember"
	OrganizationService_GetTeamMember_FullMethodName                          = "/akuity.organization.v1.OrganizationService/GetTeamMember"
	OrganizationService_ListTeamMembers_FullMethodName                        = "/akuity.organization.v1.OrganizationService/ListTeamMembers"
	OrganizationService_RemoveTeamMember_FullMethodName                       = "/akuity.organization.v1.OrganizationService/RemoveTeamMember"
	OrganizationService_UpdateArgocdInstancesQuota_FullMethodName             = "/akuity.organization.v1.OrganizationService/UpdateArgocdInstancesQuota"
	OrganizationService_ListArgocdInstancesQuota_FullMethodName               = "/akuity.organization.v1.OrganizationService/ListArgocdInstancesQuota"
	OrganizationService_UpdateKargoInstancesQuota_FullMethodName              = "/akuity.organization.v1.OrganizationService/UpdateKargoInstancesQuota"
	OrganizationService_ListKargoInstancesQuota_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListKargoInstancesQuota"
	OrganizationService_CreateWorkspace_FullMethodName                        = "/akuity.organization.v1.OrganizationService/CreateWorkspace"
	OrganizationService_ListWorkspaces_FullMethodName                         = "/akuity.organization.v1.OrganizationService/ListWorkspaces"
	OrganizationService_GetWorkspace_FullMethodName                           = "/akuity.organization.v1.OrganizationService/GetWorkspace"
	OrganizationService_UpdateWorkspace_FullMethodName                        = "/akuity.organization.v1.OrganizationService/UpdateWorkspace"
	OrganizationService_DeleteWorkspace_FullMethodName                        = "/akuity.organization.v1.OrganizationService/DeleteWorkspace"
	OrganizationService_AddWorkspaceMember_FullMethodName                     = "/akuity.organization.v1.OrganizationService/AddWorkspaceMember"
	OrganizationService_ListWorkspaceMembers_FullMethodName                   = "/akuity.organization.v1.OrganizationService/ListWorkspaceMembers"
	OrganizationService_UpdateWorkspaceMembers_FullMethodName                 = "/akuity.organization.v1.OrganizationService/UpdateWorkspaceMembers"
	OrganizationService_GetWorkspaceMember_FullMethodName                     = "/akuity.organization.v1.OrganizationService/GetWorkspaceMember"
	OrganizationService_UpdateWorkspaceMember_FullMethodName                  = "/akuity.organization.v1.OrganizationService/UpdateWorkspaceMember"
	OrganizationService_RemoveWorkspaceMember_FullMethodName                  = "/akuity.organization.v1.OrganizationService/RemoveWorkspaceMember"
	OrganizationService_CancelSubscription_FullMethodName                     = "/akuity.organization.v1.OrganizationService/CancelSubscription"
	OrganizationService_ListKubernetesResourceTypes_FullMethodName            = "/akuity.organization.v1.OrganizationService/ListKubernetesResourceTypes"
	OrganizationService_ListKubernetesResources_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListKubernetesResources"
	OrganizationService_ListKubernetesResourcesToCSV_FullMethodName           = "/akuity.organization.v1.OrganizationService/ListKubernetesResourcesToCSV"
	OrganizationService_SpotlightSearchKubernetesResources_FullMethodName     = "/akuity.organization.v1.OrganizationService/SpotlightSearchKubernetesResources"
	OrganizationService_GetKubernetesResourceDetail_FullMethodName            = "/akuity.organization.v1.OrganizationService/GetKubernetesResourceDetail"
	OrganizationService_GetKubernetesContainer_FullMethodName                 = "/akuity.organization.v1.OrganizationService/GetKubernetesContainer"
	OrganizationService_ListKubernetesNamespaces_FullMethodName               = "/akuity.organization.v1.OrganizationService/ListKubernetesNamespaces"
	OrganizationService_ListKubernetesImages_FullMethodName                   = "/akuity.organization.v1.OrganizationService/ListKubernetesImages"
	OrganizationService_ListKubernetesImagesToCSV_FullMethodName              = "/akuity.organization.v1.OrganizationService/ListKubernetesImagesToCSV"
	OrganizationService_GetKubernetesImageDetail_FullMethodName               = "/akuity.organization.v1.OrganizationService/GetKubernetesImageDetail"
	OrganizationService_ListKubernetesContainers_FullMethodName               = "/akuity.organization.v1.OrganizationService/ListKubernetesContainers"
	OrganizationService_ListKubernetesContainersToCSV_FullMethodName          = "/akuity.organization.v1.OrganizationService/ListKubernetesContainersToCSV"
	OrganizationService_ListKubernetesEnabledClusters_FullMethodName          = "/akuity.organization.v1.OrganizationService/ListKubernetesEnabledClusters"
	OrganizationService_GetKubernetesManifest_FullMethodName                  = "/akuity.organization.v1.OrganizationService/GetKubernetesManifest"
	OrganizationService_DeleteKubernetesResource_FullMethodName               = "/akuity.organization.v1.OrganizationService/DeleteKubernetesResource"
	OrganizationService_GetKubernetesLogs_FullMethodName                      = "/akuity.organization.v1.OrganizationService/GetKubernetesLogs"
	OrganizationService_GetKubernetesEvents_FullMethodName                    = "/akuity.organization.v1.OrganizationService/GetKubernetesEvents"
	OrganizationService_ListKubernetesAuditLogs_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListKubernetesAuditLogs"
	OrganizationService_ListKubernetesNodes_FullMethodName                    = "/akuity.organization.v1.OrganizationService/ListKubernetesNodes"
	OrganizationService_GetKubernetesNode_FullMethodName                      = "/akuity.organization.v1.OrganizationService/GetKubernetesNode"
	OrganizationService_ListKubernetesNamespacesDetails_FullMethodName        = "/akuity.organization.v1.OrganizationService/ListKubernetesNamespacesDetails"
	OrganizationService_GetKubernetesNamespaceDetail_FullMethodName           = "/akuity.organization.v1.OrganizationService/GetKubernetesNamespaceDetail"
	OrganizationService_GetKubernetesClusterDetail_FullMethodName             = "/akuity.organization.v1.OrganizationService/GetKubernetesClusterDetail"
	OrganizationService_GetKubernetesSummary_FullMethodName                   = "/akuity.organization.v1.OrganizationService/GetKubernetesSummary"
	OrganizationService_ListKubernetesPods_FullMethodName                     = "/akuity.organization.v1.OrganizationService/ListKubernetesPods"
	OrganizationService_GetKubernetesPod_FullMethodName                       = "/akuity.organization.v1.OrganizationService/GetKubernetesPod"
	OrganizationService_ListKubernetesDeprecatedAPIs_FullMethodName           = "/akuity.organization.v1.OrganizationService/ListKubernetesDeprecatedAPIs"
	OrganizationService_ListKubernetesDeprecatedAPIsToCSV_FullMethodName      = "/akuity.organization.v1.OrganizationService/ListKubernetesDeprecatedAPIsToCSV"
	OrganizationService_GetKubernetesAssistantSuggestion_FullMethodName       = "/akuity.organization.v1.OrganizationService/GetKubernetesAssistantSuggestion"
	OrganizationService_ResolveKubernetesAssistantConversation_FullMethodName = "/akuity.organization.v1.OrganizationService/ResolveKubernetesAssistantConversation"
	OrganizationService_ListKubernetesTimelineEvents_FullMethodName           = "/akuity.organization.v1.OrganizationService/ListKubernetesTimelineEvents"
	OrganizationService_ListKubernetesTimelineResources_FullMethodName        = "/akuity.organization.v1.OrganizationService/ListKubernetesTimelineResources"
	OrganizationService_GetKubeVisionUsage_FullMethodName                     = "/akuity.organization.v1.OrganizationService/GetKubeVisionUsage"
	OrganizationService_GetKubeVisionUsageToCSV_FullMethodName                = "/akuity.organization.v1.OrganizationService/GetKubeVisionUsageToCSV"
	OrganizationService_ListNotificationConfigs_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListNotificationConfigs"
	OrganizationService_GetNotificationConfig_FullMethodName                  = "/akuity.organization.v1.OrganizationService/GetNotificationConfig"
	OrganizationService_CreateNotificationConfig_FullMethodName               = "/akuity.organization.v1.OrganizationService/CreateNotificationConfig"
	OrganizationService_UpdateNotificationConfig_FullMethodName               = "/akuity.organization.v1.OrganizationService/UpdateNotificationConfig"
	OrganizationService_DeleteNotificationConfig_FullMethodName               = "/akuity.organization.v1.OrganizationService/DeleteNotificationConfig"
	OrganizationService_ListNotificationDeliveryHistory_FullMethodName        = "/akuity.organization.v1.OrganizationService/ListNotificationDeliveryHistory"
	OrganizationService_GetNotificationDeliveryHistoryDetail_FullMethodName   = "/akuity.organization.v1.OrganizationService/GetNotificationDeliveryHistoryDetail"
	OrganizationService_PingNotificationConfig_FullMethodName                 = "/akuity.organization.v1.OrganizationService/PingNotificationConfig"
	OrganizationService_RedeliverNotification_FullMethodName                  = "/akuity.organization.v1.OrganizationService/RedeliverNotification"
	OrganizationService_ListOrganizationDomains_FullMethodName                = "/akuity.organization.v1.OrganizationService/ListOrganizationDomains"
	OrganizationService_DeleteOrganizationDomain_FullMethodName               = "/akuity.organization.v1.OrganizationService/DeleteOrganizationDomain"
	OrganizationService_VerifyOrganizationDomains_FullMethodName              = "/akuity.organization.v1.OrganizationService/VerifyOrganizationDomains"
	OrganizationService_CreateAIConversation_FullMethodName                   = "/akuity.organization.v1.OrganizationService/CreateAIConversation"
	OrganizationService_CreateIncident_FullMethodName                         = "/akuity.organization.v1.OrganizationService/CreateIncident"
	OrganizationService_UpdateAIConversation_FullMethodName                   = "/akuity.organization.v1.OrganizationService/UpdateAIConversation"
	OrganizationService_DeleteAIConversation_FullMethodName                   = "/akuity.organization.v1.OrganizationService/DeleteAIConversation"
	OrganizationService_GetAIConversation_FullMethodName                      = "/akuity.organization.v1.OrganizationService/GetAIConversation"
	OrganizationService_GetAIConversationStream_FullMethodName                = "/akuity.organization.v1.OrganizationService/GetAIConversationStream"
	OrganizationService_ListAIConversations_FullMethodName                    = "/akuity.organization.v1.OrganizationService/ListAIConversations"
	OrganizationService_CreateAIMessage_FullMethodName                        = "/akuity.organization.v1.OrganizationService/CreateAIMessage"
	OrganizationService_ListUsersMFAStatus_FullMethodName                     = "/akuity.organization.v1.OrganizationService/ListUsersMFAStatus"
	OrganizationService_RequestMFAReset_FullMethodName                        = "/akuity.organization.v1.OrganizationService/RequestMFAReset"
	OrganizationService_ListAIConversationSuggestions_FullMethodName          = "/akuity.organization.v1.OrganizationService/ListAIConversationSuggestions"
	OrganizationService_UpdateAIMessageFeedback_FullMethodName                = "/akuity.organization.v1.OrganizationService/UpdateAIMessageFeedback"
	OrganizationService_UpdateAIConversationFeedback_FullMethodName           = "/akuity.organization.v1.OrganizationService/UpdateAIConversationFeedback"
)

// OrganizationServiceClient is the client API for OrganizationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrganizationServiceClient interface {
	ListAuthenticatedUserOrganizations(ctx context.Context, in *ListAuthenticatedUserOrganizationsRequest, opts ...grpc.CallOption) (*ListAuthenticatedUserOrganizationsResponse, error)
	GetOrganization(ctx context.Context, in *GetOrganizationRequest, opts ...grpc.CallOption) (*GetOrganizationResponse, error)
	GetOrganizationPermissions(ctx context.Context, in *GetOrganizationPermissionsRequest, opts ...grpc.CallOption) (*GetOrganizationPermissionsResponse, error)
	CreateOrganization(ctx context.Context, in *CreateOrganizationRequest, opts ...grpc.CallOption) (*CreateOrganizationResponse, error)
	UpdateOrganization(ctx context.Context, in *UpdateOrganizationRequest, opts ...grpc.CallOption) (*UpdateOrganizationResponse, error)
	DeleteOrganization(ctx context.Context, in *DeleteOrganizationRequest, opts ...grpc.CallOption) (*DeleteOrganizationResponse, error)
	ListOrganizationMembers(ctx context.Context, in *ListOrganizationMembersRequest, opts ...grpc.CallOption) (*ListOrganizationMembersResponse, error)
	ListOrganizationInvitees(ctx context.Context, in *ListOrganizationInviteesRequest, opts ...grpc.CallOption) (*ListOrganizationInviteesResponse, error)
	GetUserRoleInOrganization(ctx context.Context, in *GetUserRoleInOrganizationRequest, opts ...grpc.CallOption) (*GetUserRoleInOrganizationResponse, error)
	InviteMembers(ctx context.Context, in *InviteMembersRequest, opts ...grpc.CallOption) (*InviteMembersResponse, error)
	UninviteOrganizationMember(ctx context.Context, in *UninviteOrganizationMemberRequest, opts ...grpc.CallOption) (*UninviteOrganizationMemberResponse, error)
	RemoveOrganizationMember(ctx context.Context, in *RemoveOrganizationMemberRequest, opts ...grpc.CallOption) (*RemoveOrganizationMemberResponse, error)
	UpdateOrganizationMemberRole(ctx context.Context, in *UpdateOrganizationMemberRoleRequest, opts ...grpc.CallOption) (*UpdateOrganizationMemberRoleResponse, error)
	JoinOrganization(ctx context.Context, in *JoinOrganizationRequest, opts ...grpc.CallOption) (*JoinOrganizationResponse, error)
	RejectOrganization(ctx context.Context, in *RejectOrganizationRequest, opts ...grpc.CallOption) (*RejectOrganizationResponse, error)
	ListOrganizationAPIKeys(ctx context.Context, in *ListOrganizationAPIKeysRequest, opts ...grpc.CallOption) (*ListOrganizationAPIKeysResponse, error)
	CreateOrganizationAPIKey(ctx context.Context, in *CreateOrganizationAPIKeyRequest, opts ...grpc.CallOption) (*CreateOrganizationAPIKeyResponse, error)
	ListWorkspaceAPIKeys(ctx context.Context, in *ListWorkspaceAPIKeysRequest, opts ...grpc.CallOption) (*ListWorkspaceAPIKeysResponse, error)
	CreateWorkspaceAPIKey(ctx context.Context, in *CreateWorkspaceAPIKeyRequest, opts ...grpc.CallOption) (*CreateWorkspaceAPIKeyResponse, error)
	GetAuditLogs(ctx context.Context, in *GetAuditLogsRequest, opts ...grpc.CallOption) (*GetAuditLogsResponse, error)
	ListAuditLogsArchives(ctx context.Context, in *ListAuditLogsArchivesRequest, opts ...grpc.CallOption) (*ListAuditLogsArchivesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetAuditLogsInCSV(ctx context.Context, in *GetAuditLogsInCSVRequest, opts ...grpc.CallOption) (OrganizationService_GetAuditLogsInCSVClient, error)
	GetCustomerDetails(ctx context.Context, in *GetCustomerDetailsRequest, opts ...grpc.CallOption) (*GetCustomerDetailsResponse, error)
	UpdateBillingDetails(ctx context.Context, in *UpdateBillingDetailsRequest, opts ...grpc.CallOption) (*UpdateBillingDetailsResponse, error)
	BillingCheckout(ctx context.Context, in *BillingCheckoutRequest, opts ...grpc.CallOption) (*BillingCheckoutResponse, error)
	UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error)
	ListAvailablePlans(ctx context.Context, in *ListAvailablePlansRequest, opts ...grpc.CallOption) (*ListAvailablePlansResponse, error)
	GetAvailableAddons(ctx context.Context, in *GetAvailableAddonsRequest, opts ...grpc.CallOption) (*GetAvailableAddonsResponse, error)
	GetSSOConfiguration(ctx context.Context, in *GetSSOConfigurationRequest, opts ...grpc.CallOption) (*GetSSOConfigurationResponse, error)
	EnsureSSOConfiguration(ctx context.Context, in *EnsureSSOConfigurationRequest, opts ...grpc.CallOption) (*EnsureSSOConfigurationResponse, error)
	DeleteSSOConfiguration(ctx context.Context, in *DeleteSSOConfigurationRequest, opts ...grpc.CallOption) (*DeleteSSOConfigurationResponse, error)
	GetFeatureStatuses(ctx context.Context, in *GetFeatureStatusesRequest, opts ...grpc.CallOption) (*GetFeatureStatusesResponse, error)
	GetOIDCMap(ctx context.Context, in *GetOIDCMapRequest, opts ...grpc.CallOption) (*GetOIDCMapResponse, error)
	UpdateOIDCMap(ctx context.Context, in *UpdateOIDCMapRequest, opts ...grpc.CallOption) (*UpdateOIDCMapResponse, error)
	GetTeamOIDCMap(ctx context.Context, in *GetTeamOIDCMapRequest, opts ...grpc.CallOption) (*GetTeamOIDCMapResponse, error)
	UpdateTeamOIDCMap(ctx context.Context, in *UpdateTeamOIDCMapRequest, opts ...grpc.CallOption) (*UpdateTeamOIDCMapResponse, error)
	CreateCustomRole(ctx context.Context, in *CreateCustomRoleRequest, opts ...grpc.CallOption) (*CreateCustomRoleResponse, error)
	UpdateCustomRole(ctx context.Context, in *UpdateCustomRoleRequest, opts ...grpc.CallOption) (*UpdateCustomRoleResponse, error)
	GetCustomRole(ctx context.Context, in *GetCustomRoleRequest, opts ...grpc.CallOption) (*GetCustomRoleResponse, error)
	ListCustomRoles(ctx context.Context, in *ListCustomRolesRequest, opts ...grpc.CallOption) (*ListCustomRolesResponse, error)
	DeleteCustomRole(ctx context.Context, in *DeleteCustomRoleRequest, opts ...grpc.CallOption) (*DeleteCustomRoleResponse, error)
	CreateWorkspaceCustomRole(ctx context.Context, in *CreateWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*CreateWorkspaceCustomRoleResponse, error)
	UpdateWorkspaceCustomRole(ctx context.Context, in *UpdateWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*UpdateWorkspaceCustomRoleResponse, error)
	GetWorkspaceCustomRole(ctx context.Context, in *GetWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*GetWorkspaceCustomRoleResponse, error)
	ListWorkspaceCustomRoles(ctx context.Context, in *ListWorkspaceCustomRolesRequest, opts ...grpc.CallOption) (*ListWorkspaceCustomRolesResponse, error)
	DeleteWorkspaceCustomRole(ctx context.Context, in *DeleteWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*DeleteWorkspaceCustomRoleResponse, error)
	CreateTeam(ctx context.Context, in *CreateTeamRequest, opts ...grpc.CallOption) (*CreateTeamResponse, error)
	UpdateTeam(ctx context.Context, in *UpdateTeamRequest, opts ...grpc.CallOption) (*UpdateTeamResponse, error)
	GetTeam(ctx context.Context, in *GetTeamRequest, opts ...grpc.CallOption) (*GetTeamResponse, error)
	ListTeams(ctx context.Context, in *ListTeamsRequest, opts ...grpc.CallOption) (*ListTeamsResponse, error)
	DeleteTeam(ctx context.Context, in *DeleteTeamRequest, opts ...grpc.CallOption) (*DeleteTeamResponse, error)
	AddTeamMember(ctx context.Context, in *AddTeamMemberRequest, opts ...grpc.CallOption) (*AddTeamMemberResponse, error)
	GetTeamMember(ctx context.Context, in *GetTeamMemberRequest, opts ...grpc.CallOption) (*GetTeamMemberResponse, error)
	ListTeamMembers(ctx context.Context, in *ListTeamMembersRequest, opts ...grpc.CallOption) (*ListTeamMembersResponse, error)
	RemoveTeamMember(ctx context.Context, in *RemoveTeamMemberRequest, opts ...grpc.CallOption) (*RemoveTeamMemberResponse, error)
	UpdateArgocdInstancesQuota(ctx context.Context, in *UpdateArgocdInstancesQuotaRequest, opts ...grpc.CallOption) (*UpdateArgocdInstancesQuotaResponse, error)
	ListArgocdInstancesQuota(ctx context.Context, in *ListArgocdInstancesQuotaRequest, opts ...grpc.CallOption) (*ListArgocdInstancesQuotaResponse, error)
	UpdateKargoInstancesQuota(ctx context.Context, in *UpdateKargoInstancesQuotaRequest, opts ...grpc.CallOption) (*UpdateKargoInstancesQuotaResponse, error)
	ListKargoInstancesQuota(ctx context.Context, in *ListKargoInstancesQuotaRequest, opts ...grpc.CallOption) (*ListKargoInstancesQuotaResponse, error)
	CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceResponse, error)
	ListWorkspaces(ctx context.Context, in *ListWorkspacesRequest, opts ...grpc.CallOption) (*ListWorkspacesResponse, error)
	GetWorkspace(ctx context.Context, in *GetWorkspaceRequest, opts ...grpc.CallOption) (*GetWorkspaceResponse, error)
	UpdateWorkspace(ctx context.Context, in *UpdateWorkspaceRequest, opts ...grpc.CallOption) (*UpdateWorkspaceResponse, error)
	DeleteWorkspace(ctx context.Context, in *DeleteWorkspaceRequest, opts ...grpc.CallOption) (*DeleteWorkspaceResponse, error)
	AddWorkspaceMember(ctx context.Context, in *AddWorkspaceMemberRequest, opts ...grpc.CallOption) (*AddWorkspaceMemberResponse, error)
	ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error)
	UpdateWorkspaceMembers(ctx context.Context, in *UpdateWorkspaceMembersRequest, opts ...grpc.CallOption) (*UpdateWorkspaceMembersResponse, error)
	GetWorkspaceMember(ctx context.Context, in *GetWorkspaceMemberRequest, opts ...grpc.CallOption) (*GetWorkspaceMemberResponse, error)
	UpdateWorkspaceMember(ctx context.Context, in *UpdateWorkspaceMemberRequest, opts ...grpc.CallOption) (*UpdateWorkspaceMemberResponse, error)
	RemoveWorkspaceMember(ctx context.Context, in *RemoveWorkspaceMemberRequest, opts ...grpc.CallOption) (*RemoveWorkspaceMemberResponse, error)
	CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error)
	ListKubernetesResourceTypes(ctx context.Context, in *ListKubernetesResourceTypesRequest, opts ...grpc.CallOption) (*ListKubernetesResourceTypesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesResources(ctx context.Context, in *ListKubernetesResourcesRequest, opts ...grpc.CallOption) (*ListKubernetesResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesResourcesToCSV(ctx context.Context, in *ListKubernetesResourcesRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesResourcesToCSVClient, error)
	SpotlightSearchKubernetesResources(ctx context.Context, in *SpotlightSearchKubernetesResourcesRequest, opts ...grpc.CallOption) (*SpotlightSearchKubernetesResourcesResponse, error)
	GetKubernetesResourceDetail(ctx context.Context, in *GetKubernetesResourceDetailRequest, opts ...grpc.CallOption) (*GetKubernetesResourceDetailResponse, error)
	GetKubernetesContainer(ctx context.Context, in *GetKubernetesContainerRequest, opts ...grpc.CallOption) (*GetKubernetesContainerResponse, error)
	ListKubernetesNamespaces(ctx context.Context, in *ListKubernetesNamespacesRequest, opts ...grpc.CallOption) (*ListKubernetesNamespacesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesImages(ctx context.Context, in *ListKubernetesImagesRequest, opts ...grpc.CallOption) (*ListKubernetesImagesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesImagesToCSV(ctx context.Context, in *ListKubernetesImagesRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesImagesToCSVClient, error)
	GetKubernetesImageDetail(ctx context.Context, in *GetKubernetesImageDetailRequest, opts ...grpc.CallOption) (*GetKubernetesImageDetailResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesContainers(ctx context.Context, in *ListKubernetesContainersRequest, opts ...grpc.CallOption) (*ListKubernetesContainersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesContainersToCSV(ctx context.Context, in *ListKubernetesContainersRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesContainersToCSVClient, error)
	ListKubernetesEnabledClusters(ctx context.Context, in *ListKubernetesEnabledClustersRequest, opts ...grpc.CallOption) (*ListKubernetesEnabledClustersResponse, error)
	GetKubernetesManifest(ctx context.Context, in *GetKubernetesManifestRequest, opts ...grpc.CallOption) (*GetKubernetesManifestResponse, error)
	DeleteKubernetesResource(ctx context.Context, in *DeleteKubernetesResourceRequest, opts ...grpc.CallOption) (*DeleteKubernetesResourceResponse, error)
	GetKubernetesLogs(ctx context.Context, in *GetKubernetesLogsRequest, opts ...grpc.CallOption) (OrganizationService_GetKubernetesLogsClient, error)
	GetKubernetesEvents(ctx context.Context, in *GetKubernetesEventsRequest, opts ...grpc.CallOption) (*GetKubernetesEventsResponse, error)
	ListKubernetesAuditLogs(ctx context.Context, in *ListKubernetesAuditLogsRequest, opts ...grpc.CallOption) (*ListKubernetesAuditLogsResponse, error)
	ListKubernetesNodes(ctx context.Context, in *ListKubernetesNodesRequest, opts ...grpc.CallOption) (*ListKubernetesNodesResponse, error)
	GetKubernetesNode(ctx context.Context, in *GetKubernetesNodeRequest, opts ...grpc.CallOption) (*GetKubernetesNodeResponse, error)
	ListKubernetesNamespacesDetails(ctx context.Context, in *ListKubernetesNamespacesDetailsRequest, opts ...grpc.CallOption) (*ListKubernetesNamespacesDetailsResponse, error)
	GetKubernetesNamespaceDetail(ctx context.Context, in *GetKubernetesNamespaceDetailRequest, opts ...grpc.CallOption) (*GetKubernetesNamespaceDetailResponse, error)
	GetKubernetesClusterDetail(ctx context.Context, in *GetKubernetesClusterDetailRequest, opts ...grpc.CallOption) (*GetKubernetesClusterDetailResponse, error)
	GetKubernetesSummary(ctx context.Context, in *GetKubernetesSummaryRequest, opts ...grpc.CallOption) (*GetKubernetesSummaryResponse, error)
	ListKubernetesPods(ctx context.Context, in *ListKubernetesPodsRequest, opts ...grpc.CallOption) (*ListKubernetesPodsResponse, error)
	GetKubernetesPod(ctx context.Context, in *GetKubernetesPodRequest, opts ...grpc.CallOption) (*GetKubernetesPodResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesDeprecatedAPIs(ctx context.Context, in *ListKubernetesDeprecatedAPIsRequest, opts ...grpc.CallOption) (*ListKubernetesDeprecatedAPIsResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesDeprecatedAPIsToCSV(ctx context.Context, in *ListKubernetesDeprecatedAPIsRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesDeprecatedAPIsToCSVClient, error)
	GetKubernetesAssistantSuggestion(ctx context.Context, in *GetKubernetesAssistantSuggestionRequest, opts ...grpc.CallOption) (*GetKubernetesAssistantSuggestionResponse, error)
	ResolveKubernetesAssistantConversation(ctx context.Context, in *ResolveKubernetesAssistantConversationRequest, opts ...grpc.CallOption) (*ResolveKubernetesAssistantConversationResponse, error)
	ListKubernetesTimelineEvents(ctx context.Context, in *ListKubernetesTimelineEventsRequest, opts ...grpc.CallOption) (*ListKubernetesTimelineEventsResponse, error)
	ListKubernetesTimelineResources(ctx context.Context, in *ListKubernetesTimelineResourcesRequest, opts ...grpc.CallOption) (*ListKubernetesTimelineResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetKubeVisionUsage(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (*GetKubeVisionUsageResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKubeVisionUsageToCSV(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (OrganizationService_GetKubeVisionUsageToCSVClient, error)
	// Notification Configs
	ListNotificationConfigs(ctx context.Context, in *ListNotificationConfigsRequest, opts ...grpc.CallOption) (*ListNotificationConfigsResponse, error)
	GetNotificationConfig(ctx context.Context, in *GetNotificationConfigRequest, opts ...grpc.CallOption) (*GetNotificationConfigResponse, error)
	CreateNotificationConfig(ctx context.Context, in *CreateNotificationConfigRequest, opts ...grpc.CallOption) (*CreateNotificationConfigResponse, error)
	UpdateNotificationConfig(ctx context.Context, in *UpdateNotificationConfigRequest, opts ...grpc.CallOption) (*UpdateNotificationConfigResponse, error)
	DeleteNotificationConfig(ctx context.Context, in *DeleteNotificationConfigRequest, opts ...grpc.CallOption) (*DeleteNotificationConfigResponse, error)
	ListNotificationDeliveryHistory(ctx context.Context, in *ListNotificationDeliveryHistoryRequest, opts ...grpc.CallOption) (*ListNotificationDeliveryHistoryResponse, error)
	GetNotificationDeliveryHistoryDetail(ctx context.Context, in *GetNotificationDeliveryHistoryDetailRequest, opts ...grpc.CallOption) (*GetNotificationDeliveryHistoryDetailResponse, error)
	PingNotificationConfig(ctx context.Context, in *PingNotificationConfigRequest, opts ...grpc.CallOption) (*PingNotificationConfigResponse, error)
	RedeliverNotification(ctx context.Context, in *RedeliverNotificationRequest, opts ...grpc.CallOption) (*RedeliverNotificationResponse, error)
	ListOrganizationDomains(ctx context.Context, in *ListOrganizationDomainsRequest, opts ...grpc.CallOption) (*ListOrganizationDomainsResponse, error)
	DeleteOrganizationDomain(ctx context.Context, in *DeleteOrganizationDomainRequest, opts ...grpc.CallOption) (*DeleteOrganizationDomainResponse, error)
	VerifyOrganizationDomains(ctx context.Context, in *VerifyOrganizationDomainsRequest, opts ...grpc.CallOption) (*VerifyOrganizationDomainsResponse, error)
	CreateAIConversation(ctx context.Context, in *CreateAIConversationRequest, opts ...grpc.CallOption) (*CreateAIConversationResponse, error)
	CreateIncident(ctx context.Context, in *CreateIncidentRequest, opts ...grpc.CallOption) (*CreateIncidentResponse, error)
	UpdateAIConversation(ctx context.Context, in *UpdateAIConversationRequest, opts ...grpc.CallOption) (*UpdateAIConversationResponse, error)
	DeleteAIConversation(ctx context.Context, in *DeleteAIConversationRequest, opts ...grpc.CallOption) (*DeleteAIConversationResponse, error)
	GetAIConversation(ctx context.Context, in *GetAIConversationRequest, opts ...grpc.CallOption) (*GetAIConversationResponse, error)
	GetAIConversationStream(ctx context.Context, in *GetAIConversationStreamRequest, opts ...grpc.CallOption) (OrganizationService_GetAIConversationStreamClient, error)
	ListAIConversations(ctx context.Context, in *ListAIConversationsRequest, opts ...grpc.CallOption) (*ListAIConversationsResponse, error)
	CreateAIMessage(ctx context.Context, in *CreateAIMessageRequest, opts ...grpc.CallOption) (*CreateAIMessageResponse, error)
	ListUsersMFAStatus(ctx context.Context, in *ListUsersMFAStatusRequest, opts ...grpc.CallOption) (*ListUsersMFAStatusResponse, error)
	RequestMFAReset(ctx context.Context, in *RequestMFAResetRequest, opts ...grpc.CallOption) (*RequestMFAResetResponse, error)
	ListAIConversationSuggestions(ctx context.Context, in *ListAIConversationSuggestionsRequest, opts ...grpc.CallOption) (*ListAIConversationSuggestionsResponse, error)
	UpdateAIMessageFeedback(ctx context.Context, in *UpdateAIMessageFeedbackRequest, opts ...grpc.CallOption) (*UpdateAIMessageFeedbackResponse, error)
	UpdateAIConversationFeedback(ctx context.Context, in *UpdateAIConversationFeedbackRequest, opts ...grpc.CallOption) (*UpdateAIConversationFeedbackResponse, error)
}

type organizationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrganizationServiceClient(cc grpc.ClientConnInterface) OrganizationServiceClient {
	return &organizationServiceClient{cc}
}

func (c *organizationServiceClient) ListAuthenticatedUserOrganizations(ctx context.Context, in *ListAuthenticatedUserOrganizationsRequest, opts ...grpc.CallOption) (*ListAuthenticatedUserOrganizationsResponse, error) {
	out := new(ListAuthenticatedUserOrganizationsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListAuthenticatedUserOrganizations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetOrganization(ctx context.Context, in *GetOrganizationRequest, opts ...grpc.CallOption) (*GetOrganizationResponse, error) {
	out := new(GetOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetOrganizationPermissions(ctx context.Context, in *GetOrganizationPermissionsRequest, opts ...grpc.CallOption) (*GetOrganizationPermissionsResponse, error) {
	out := new(GetOrganizationPermissionsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetOrganizationPermissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateOrganization(ctx context.Context, in *CreateOrganizationRequest, opts ...grpc.CallOption) (*CreateOrganizationResponse, error) {
	out := new(CreateOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateOrganization(ctx context.Context, in *UpdateOrganizationRequest, opts ...grpc.CallOption) (*UpdateOrganizationResponse, error) {
	out := new(UpdateOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteOrganization(ctx context.Context, in *DeleteOrganizationRequest, opts ...grpc.CallOption) (*DeleteOrganizationResponse, error) {
	out := new(DeleteOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListOrganizationMembers(ctx context.Context, in *ListOrganizationMembersRequest, opts ...grpc.CallOption) (*ListOrganizationMembersResponse, error) {
	out := new(ListOrganizationMembersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListOrganizationMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListOrganizationInvitees(ctx context.Context, in *ListOrganizationInviteesRequest, opts ...grpc.CallOption) (*ListOrganizationInviteesResponse, error) {
	out := new(ListOrganizationInviteesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListOrganizationInvitees_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetUserRoleInOrganization(ctx context.Context, in *GetUserRoleInOrganizationRequest, opts ...grpc.CallOption) (*GetUserRoleInOrganizationResponse, error) {
	out := new(GetUserRoleInOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetUserRoleInOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) InviteMembers(ctx context.Context, in *InviteMembersRequest, opts ...grpc.CallOption) (*InviteMembersResponse, error) {
	out := new(InviteMembersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_InviteMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UninviteOrganizationMember(ctx context.Context, in *UninviteOrganizationMemberRequest, opts ...grpc.CallOption) (*UninviteOrganizationMemberResponse, error) {
	out := new(UninviteOrganizationMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UninviteOrganizationMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RemoveOrganizationMember(ctx context.Context, in *RemoveOrganizationMemberRequest, opts ...grpc.CallOption) (*RemoveOrganizationMemberResponse, error) {
	out := new(RemoveOrganizationMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RemoveOrganizationMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateOrganizationMemberRole(ctx context.Context, in *UpdateOrganizationMemberRoleRequest, opts ...grpc.CallOption) (*UpdateOrganizationMemberRoleResponse, error) {
	out := new(UpdateOrganizationMemberRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateOrganizationMemberRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) JoinOrganization(ctx context.Context, in *JoinOrganizationRequest, opts ...grpc.CallOption) (*JoinOrganizationResponse, error) {
	out := new(JoinOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_JoinOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RejectOrganization(ctx context.Context, in *RejectOrganizationRequest, opts ...grpc.CallOption) (*RejectOrganizationResponse, error) {
	out := new(RejectOrganizationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RejectOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListOrganizationAPIKeys(ctx context.Context, in *ListOrganizationAPIKeysRequest, opts ...grpc.CallOption) (*ListOrganizationAPIKeysResponse, error) {
	out := new(ListOrganizationAPIKeysResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListOrganizationAPIKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateOrganizationAPIKey(ctx context.Context, in *CreateOrganizationAPIKeyRequest, opts ...grpc.CallOption) (*CreateOrganizationAPIKeyResponse, error) {
	out := new(CreateOrganizationAPIKeyResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateOrganizationAPIKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListWorkspaceAPIKeys(ctx context.Context, in *ListWorkspaceAPIKeysRequest, opts ...grpc.CallOption) (*ListWorkspaceAPIKeysResponse, error) {
	out := new(ListWorkspaceAPIKeysResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListWorkspaceAPIKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateWorkspaceAPIKey(ctx context.Context, in *CreateWorkspaceAPIKeyRequest, opts ...grpc.CallOption) (*CreateWorkspaceAPIKeyResponse, error) {
	out := new(CreateWorkspaceAPIKeyResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateWorkspaceAPIKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetAuditLogs(ctx context.Context, in *GetAuditLogsRequest, opts ...grpc.CallOption) (*GetAuditLogsResponse, error) {
	out := new(GetAuditLogsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetAuditLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListAuditLogsArchives(ctx context.Context, in *ListAuditLogsArchivesRequest, opts ...grpc.CallOption) (*ListAuditLogsArchivesResponse, error) {
	out := new(ListAuditLogsArchivesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListAuditLogsArchives_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetAuditLogsInCSV(ctx context.Context, in *GetAuditLogsInCSVRequest, opts ...grpc.CallOption) (OrganizationService_GetAuditLogsInCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[0], OrganizationService_GetAuditLogsInCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceGetAuditLogsInCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_GetAuditLogsInCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceGetAuditLogsInCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceGetAuditLogsInCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) GetCustomerDetails(ctx context.Context, in *GetCustomerDetailsRequest, opts ...grpc.CallOption) (*GetCustomerDetailsResponse, error) {
	out := new(GetCustomerDetailsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetCustomerDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateBillingDetails(ctx context.Context, in *UpdateBillingDetailsRequest, opts ...grpc.CallOption) (*UpdateBillingDetailsResponse, error) {
	out := new(UpdateBillingDetailsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateBillingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) BillingCheckout(ctx context.Context, in *BillingCheckoutRequest, opts ...grpc.CallOption) (*BillingCheckoutResponse, error) {
	out := new(BillingCheckoutResponse)
	err := c.cc.Invoke(ctx, OrganizationService_BillingCheckout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error) {
	out := new(UpdateSubscriptionResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateSubscription_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListAvailablePlans(ctx context.Context, in *ListAvailablePlansRequest, opts ...grpc.CallOption) (*ListAvailablePlansResponse, error) {
	out := new(ListAvailablePlansResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListAvailablePlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetAvailableAddons(ctx context.Context, in *GetAvailableAddonsRequest, opts ...grpc.CallOption) (*GetAvailableAddonsResponse, error) {
	out := new(GetAvailableAddonsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetAvailableAddons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetSSOConfiguration(ctx context.Context, in *GetSSOConfigurationRequest, opts ...grpc.CallOption) (*GetSSOConfigurationResponse, error) {
	out := new(GetSSOConfigurationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetSSOConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) EnsureSSOConfiguration(ctx context.Context, in *EnsureSSOConfigurationRequest, opts ...grpc.CallOption) (*EnsureSSOConfigurationResponse, error) {
	out := new(EnsureSSOConfigurationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_EnsureSSOConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteSSOConfiguration(ctx context.Context, in *DeleteSSOConfigurationRequest, opts ...grpc.CallOption) (*DeleteSSOConfigurationResponse, error) {
	out := new(DeleteSSOConfigurationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteSSOConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetFeatureStatuses(ctx context.Context, in *GetFeatureStatusesRequest, opts ...grpc.CallOption) (*GetFeatureStatusesResponse, error) {
	out := new(GetFeatureStatusesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetFeatureStatuses_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetOIDCMap(ctx context.Context, in *GetOIDCMapRequest, opts ...grpc.CallOption) (*GetOIDCMapResponse, error) {
	out := new(GetOIDCMapResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetOIDCMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateOIDCMap(ctx context.Context, in *UpdateOIDCMapRequest, opts ...grpc.CallOption) (*UpdateOIDCMapResponse, error) {
	out := new(UpdateOIDCMapResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateOIDCMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetTeamOIDCMap(ctx context.Context, in *GetTeamOIDCMapRequest, opts ...grpc.CallOption) (*GetTeamOIDCMapResponse, error) {
	out := new(GetTeamOIDCMapResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetTeamOIDCMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateTeamOIDCMap(ctx context.Context, in *UpdateTeamOIDCMapRequest, opts ...grpc.CallOption) (*UpdateTeamOIDCMapResponse, error) {
	out := new(UpdateTeamOIDCMapResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateTeamOIDCMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateCustomRole(ctx context.Context, in *CreateCustomRoleRequest, opts ...grpc.CallOption) (*CreateCustomRoleResponse, error) {
	out := new(CreateCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateCustomRole(ctx context.Context, in *UpdateCustomRoleRequest, opts ...grpc.CallOption) (*UpdateCustomRoleResponse, error) {
	out := new(UpdateCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetCustomRole(ctx context.Context, in *GetCustomRoleRequest, opts ...grpc.CallOption) (*GetCustomRoleResponse, error) {
	out := new(GetCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListCustomRoles(ctx context.Context, in *ListCustomRolesRequest, opts ...grpc.CallOption) (*ListCustomRolesResponse, error) {
	out := new(ListCustomRolesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListCustomRoles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteCustomRole(ctx context.Context, in *DeleteCustomRoleRequest, opts ...grpc.CallOption) (*DeleteCustomRoleResponse, error) {
	out := new(DeleteCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateWorkspaceCustomRole(ctx context.Context, in *CreateWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*CreateWorkspaceCustomRoleResponse, error) {
	out := new(CreateWorkspaceCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateWorkspaceCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateWorkspaceCustomRole(ctx context.Context, in *UpdateWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*UpdateWorkspaceCustomRoleResponse, error) {
	out := new(UpdateWorkspaceCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateWorkspaceCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetWorkspaceCustomRole(ctx context.Context, in *GetWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*GetWorkspaceCustomRoleResponse, error) {
	out := new(GetWorkspaceCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetWorkspaceCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListWorkspaceCustomRoles(ctx context.Context, in *ListWorkspaceCustomRolesRequest, opts ...grpc.CallOption) (*ListWorkspaceCustomRolesResponse, error) {
	out := new(ListWorkspaceCustomRolesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListWorkspaceCustomRoles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteWorkspaceCustomRole(ctx context.Context, in *DeleteWorkspaceCustomRoleRequest, opts ...grpc.CallOption) (*DeleteWorkspaceCustomRoleResponse, error) {
	out := new(DeleteWorkspaceCustomRoleResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteWorkspaceCustomRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateTeam(ctx context.Context, in *CreateTeamRequest, opts ...grpc.CallOption) (*CreateTeamResponse, error) {
	out := new(CreateTeamResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateTeam(ctx context.Context, in *UpdateTeamRequest, opts ...grpc.CallOption) (*UpdateTeamResponse, error) {
	out := new(UpdateTeamResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetTeam(ctx context.Context, in *GetTeamRequest, opts ...grpc.CallOption) (*GetTeamResponse, error) {
	out := new(GetTeamResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListTeams(ctx context.Context, in *ListTeamsRequest, opts ...grpc.CallOption) (*ListTeamsResponse, error) {
	out := new(ListTeamsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListTeams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteTeam(ctx context.Context, in *DeleteTeamRequest, opts ...grpc.CallOption) (*DeleteTeamResponse, error) {
	out := new(DeleteTeamResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) AddTeamMember(ctx context.Context, in *AddTeamMemberRequest, opts ...grpc.CallOption) (*AddTeamMemberResponse, error) {
	out := new(AddTeamMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_AddTeamMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetTeamMember(ctx context.Context, in *GetTeamMemberRequest, opts ...grpc.CallOption) (*GetTeamMemberResponse, error) {
	out := new(GetTeamMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetTeamMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListTeamMembers(ctx context.Context, in *ListTeamMembersRequest, opts ...grpc.CallOption) (*ListTeamMembersResponse, error) {
	out := new(ListTeamMembersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListTeamMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RemoveTeamMember(ctx context.Context, in *RemoveTeamMemberRequest, opts ...grpc.CallOption) (*RemoveTeamMemberResponse, error) {
	out := new(RemoveTeamMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RemoveTeamMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateArgocdInstancesQuota(ctx context.Context, in *UpdateArgocdInstancesQuotaRequest, opts ...grpc.CallOption) (*UpdateArgocdInstancesQuotaResponse, error) {
	out := new(UpdateArgocdInstancesQuotaResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateArgocdInstancesQuota_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListArgocdInstancesQuota(ctx context.Context, in *ListArgocdInstancesQuotaRequest, opts ...grpc.CallOption) (*ListArgocdInstancesQuotaResponse, error) {
	out := new(ListArgocdInstancesQuotaResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListArgocdInstancesQuota_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateKargoInstancesQuota(ctx context.Context, in *UpdateKargoInstancesQuotaRequest, opts ...grpc.CallOption) (*UpdateKargoInstancesQuotaResponse, error) {
	out := new(UpdateKargoInstancesQuotaResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateKargoInstancesQuota_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKargoInstancesQuota(ctx context.Context, in *ListKargoInstancesQuotaRequest, opts ...grpc.CallOption) (*ListKargoInstancesQuotaResponse, error) {
	out := new(ListKargoInstancesQuotaResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKargoInstancesQuota_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceResponse, error) {
	out := new(CreateWorkspaceResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListWorkspaces(ctx context.Context, in *ListWorkspacesRequest, opts ...grpc.CallOption) (*ListWorkspacesResponse, error) {
	out := new(ListWorkspacesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListWorkspaces_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetWorkspace(ctx context.Context, in *GetWorkspaceRequest, opts ...grpc.CallOption) (*GetWorkspaceResponse, error) {
	out := new(GetWorkspaceResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateWorkspace(ctx context.Context, in *UpdateWorkspaceRequest, opts ...grpc.CallOption) (*UpdateWorkspaceResponse, error) {
	out := new(UpdateWorkspaceResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteWorkspace(ctx context.Context, in *DeleteWorkspaceRequest, opts ...grpc.CallOption) (*DeleteWorkspaceResponse, error) {
	out := new(DeleteWorkspaceResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) AddWorkspaceMember(ctx context.Context, in *AddWorkspaceMemberRequest, opts ...grpc.CallOption) (*AddWorkspaceMemberResponse, error) {
	out := new(AddWorkspaceMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_AddWorkspaceMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error) {
	out := new(ListWorkspaceMembersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListWorkspaceMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateWorkspaceMembers(ctx context.Context, in *UpdateWorkspaceMembersRequest, opts ...grpc.CallOption) (*UpdateWorkspaceMembersResponse, error) {
	out := new(UpdateWorkspaceMembersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateWorkspaceMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetWorkspaceMember(ctx context.Context, in *GetWorkspaceMemberRequest, opts ...grpc.CallOption) (*GetWorkspaceMemberResponse, error) {
	out := new(GetWorkspaceMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetWorkspaceMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateWorkspaceMember(ctx context.Context, in *UpdateWorkspaceMemberRequest, opts ...grpc.CallOption) (*UpdateWorkspaceMemberResponse, error) {
	out := new(UpdateWorkspaceMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateWorkspaceMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RemoveWorkspaceMember(ctx context.Context, in *RemoveWorkspaceMemberRequest, opts ...grpc.CallOption) (*RemoveWorkspaceMemberResponse, error) {
	out := new(RemoveWorkspaceMemberResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RemoveWorkspaceMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error) {
	out := new(CancelSubscriptionResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CancelSubscription_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesResourceTypes(ctx context.Context, in *ListKubernetesResourceTypesRequest, opts ...grpc.CallOption) (*ListKubernetesResourceTypesResponse, error) {
	out := new(ListKubernetesResourceTypesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesResourceTypes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesResources(ctx context.Context, in *ListKubernetesResourcesRequest, opts ...grpc.CallOption) (*ListKubernetesResourcesResponse, error) {
	out := new(ListKubernetesResourcesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesResources_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesResourcesToCSV(ctx context.Context, in *ListKubernetesResourcesRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesResourcesToCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[1], OrganizationService_ListKubernetesResourcesToCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceListKubernetesResourcesToCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_ListKubernetesResourcesToCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceListKubernetesResourcesToCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceListKubernetesResourcesToCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) SpotlightSearchKubernetesResources(ctx context.Context, in *SpotlightSearchKubernetesResourcesRequest, opts ...grpc.CallOption) (*SpotlightSearchKubernetesResourcesResponse, error) {
	out := new(SpotlightSearchKubernetesResourcesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_SpotlightSearchKubernetesResources_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesResourceDetail(ctx context.Context, in *GetKubernetesResourceDetailRequest, opts ...grpc.CallOption) (*GetKubernetesResourceDetailResponse, error) {
	out := new(GetKubernetesResourceDetailResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesResourceDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesContainer(ctx context.Context, in *GetKubernetesContainerRequest, opts ...grpc.CallOption) (*GetKubernetesContainerResponse, error) {
	out := new(GetKubernetesContainerResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesContainer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesNamespaces(ctx context.Context, in *ListKubernetesNamespacesRequest, opts ...grpc.CallOption) (*ListKubernetesNamespacesResponse, error) {
	out := new(ListKubernetesNamespacesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesNamespaces_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesImages(ctx context.Context, in *ListKubernetesImagesRequest, opts ...grpc.CallOption) (*ListKubernetesImagesResponse, error) {
	out := new(ListKubernetesImagesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesImages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesImagesToCSV(ctx context.Context, in *ListKubernetesImagesRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesImagesToCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[2], OrganizationService_ListKubernetesImagesToCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceListKubernetesImagesToCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_ListKubernetesImagesToCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceListKubernetesImagesToCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceListKubernetesImagesToCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) GetKubernetesImageDetail(ctx context.Context, in *GetKubernetesImageDetailRequest, opts ...grpc.CallOption) (*GetKubernetesImageDetailResponse, error) {
	out := new(GetKubernetesImageDetailResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesImageDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesContainers(ctx context.Context, in *ListKubernetesContainersRequest, opts ...grpc.CallOption) (*ListKubernetesContainersResponse, error) {
	out := new(ListKubernetesContainersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesContainers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesContainersToCSV(ctx context.Context, in *ListKubernetesContainersRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesContainersToCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[3], OrganizationService_ListKubernetesContainersToCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceListKubernetesContainersToCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_ListKubernetesContainersToCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceListKubernetesContainersToCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceListKubernetesContainersToCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) ListKubernetesEnabledClusters(ctx context.Context, in *ListKubernetesEnabledClustersRequest, opts ...grpc.CallOption) (*ListKubernetesEnabledClustersResponse, error) {
	out := new(ListKubernetesEnabledClustersResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesEnabledClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesManifest(ctx context.Context, in *GetKubernetesManifestRequest, opts ...grpc.CallOption) (*GetKubernetesManifestResponse, error) {
	out := new(GetKubernetesManifestResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesManifest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteKubernetesResource(ctx context.Context, in *DeleteKubernetesResourceRequest, opts ...grpc.CallOption) (*DeleteKubernetesResourceResponse, error) {
	out := new(DeleteKubernetesResourceResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteKubernetesResource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesLogs(ctx context.Context, in *GetKubernetesLogsRequest, opts ...grpc.CallOption) (OrganizationService_GetKubernetesLogsClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[4], OrganizationService_GetKubernetesLogs_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceGetKubernetesLogsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_GetKubernetesLogsClient interface {
	Recv() (*GetKubernetesLogsResponse, error)
	grpc.ClientStream
}

type organizationServiceGetKubernetesLogsClient struct {
	grpc.ClientStream
}

func (x *organizationServiceGetKubernetesLogsClient) Recv() (*GetKubernetesLogsResponse, error) {
	m := new(GetKubernetesLogsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) GetKubernetesEvents(ctx context.Context, in *GetKubernetesEventsRequest, opts ...grpc.CallOption) (*GetKubernetesEventsResponse, error) {
	out := new(GetKubernetesEventsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesAuditLogs(ctx context.Context, in *ListKubernetesAuditLogsRequest, opts ...grpc.CallOption) (*ListKubernetesAuditLogsResponse, error) {
	out := new(ListKubernetesAuditLogsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesAuditLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesNodes(ctx context.Context, in *ListKubernetesNodesRequest, opts ...grpc.CallOption) (*ListKubernetesNodesResponse, error) {
	out := new(ListKubernetesNodesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesNodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesNode(ctx context.Context, in *GetKubernetesNodeRequest, opts ...grpc.CallOption) (*GetKubernetesNodeResponse, error) {
	out := new(GetKubernetesNodeResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesNamespacesDetails(ctx context.Context, in *ListKubernetesNamespacesDetailsRequest, opts ...grpc.CallOption) (*ListKubernetesNamespacesDetailsResponse, error) {
	out := new(ListKubernetesNamespacesDetailsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesNamespacesDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesNamespaceDetail(ctx context.Context, in *GetKubernetesNamespaceDetailRequest, opts ...grpc.CallOption) (*GetKubernetesNamespaceDetailResponse, error) {
	out := new(GetKubernetesNamespaceDetailResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesNamespaceDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesClusterDetail(ctx context.Context, in *GetKubernetesClusterDetailRequest, opts ...grpc.CallOption) (*GetKubernetesClusterDetailResponse, error) {
	out := new(GetKubernetesClusterDetailResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesClusterDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesSummary(ctx context.Context, in *GetKubernetesSummaryRequest, opts ...grpc.CallOption) (*GetKubernetesSummaryResponse, error) {
	out := new(GetKubernetesSummaryResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesPods(ctx context.Context, in *ListKubernetesPodsRequest, opts ...grpc.CallOption) (*ListKubernetesPodsResponse, error) {
	out := new(ListKubernetesPodsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesPods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubernetesPod(ctx context.Context, in *GetKubernetesPodRequest, opts ...grpc.CallOption) (*GetKubernetesPodResponse, error) {
	out := new(GetKubernetesPodResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesPod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesDeprecatedAPIs(ctx context.Context, in *ListKubernetesDeprecatedAPIsRequest, opts ...grpc.CallOption) (*ListKubernetesDeprecatedAPIsResponse, error) {
	out := new(ListKubernetesDeprecatedAPIsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesDeprecatedAPIs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesDeprecatedAPIsToCSV(ctx context.Context, in *ListKubernetesDeprecatedAPIsRequest, opts ...grpc.CallOption) (OrganizationService_ListKubernetesDeprecatedAPIsToCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[5], OrganizationService_ListKubernetesDeprecatedAPIsToCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceListKubernetesDeprecatedAPIsToCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_ListKubernetesDeprecatedAPIsToCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceListKubernetesDeprecatedAPIsToCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceListKubernetesDeprecatedAPIsToCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) GetKubernetesAssistantSuggestion(ctx context.Context, in *GetKubernetesAssistantSuggestionRequest, opts ...grpc.CallOption) (*GetKubernetesAssistantSuggestionResponse, error) {
	out := new(GetKubernetesAssistantSuggestionResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubernetesAssistantSuggestion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ResolveKubernetesAssistantConversation(ctx context.Context, in *ResolveKubernetesAssistantConversationRequest, opts ...grpc.CallOption) (*ResolveKubernetesAssistantConversationResponse, error) {
	out := new(ResolveKubernetesAssistantConversationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ResolveKubernetesAssistantConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesTimelineEvents(ctx context.Context, in *ListKubernetesTimelineEventsRequest, opts ...grpc.CallOption) (*ListKubernetesTimelineEventsResponse, error) {
	out := new(ListKubernetesTimelineEventsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesTimelineEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListKubernetesTimelineResources(ctx context.Context, in *ListKubernetesTimelineResourcesRequest, opts ...grpc.CallOption) (*ListKubernetesTimelineResourcesResponse, error) {
	out := new(ListKubernetesTimelineResourcesResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListKubernetesTimelineResources_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubeVisionUsage(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (*GetKubeVisionUsageResponse, error) {
	out := new(GetKubeVisionUsageResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetKubeVisionUsage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetKubeVisionUsageToCSV(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (OrganizationService_GetKubeVisionUsageToCSVClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[6], OrganizationService_GetKubeVisionUsageToCSV_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceGetKubeVisionUsageToCSVClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_GetKubeVisionUsageToCSVClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type organizationServiceGetKubeVisionUsageToCSVClient struct {
	grpc.ClientStream
}

func (x *organizationServiceGetKubeVisionUsageToCSVClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) ListNotificationConfigs(ctx context.Context, in *ListNotificationConfigsRequest, opts ...grpc.CallOption) (*ListNotificationConfigsResponse, error) {
	out := new(ListNotificationConfigsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListNotificationConfigs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetNotificationConfig(ctx context.Context, in *GetNotificationConfigRequest, opts ...grpc.CallOption) (*GetNotificationConfigResponse, error) {
	out := new(GetNotificationConfigResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateNotificationConfig(ctx context.Context, in *CreateNotificationConfigRequest, opts ...grpc.CallOption) (*CreateNotificationConfigResponse, error) {
	out := new(CreateNotificationConfigResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateNotificationConfig(ctx context.Context, in *UpdateNotificationConfigRequest, opts ...grpc.CallOption) (*UpdateNotificationConfigResponse, error) {
	out := new(UpdateNotificationConfigResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteNotificationConfig(ctx context.Context, in *DeleteNotificationConfigRequest, opts ...grpc.CallOption) (*DeleteNotificationConfigResponse, error) {
	out := new(DeleteNotificationConfigResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListNotificationDeliveryHistory(ctx context.Context, in *ListNotificationDeliveryHistoryRequest, opts ...grpc.CallOption) (*ListNotificationDeliveryHistoryResponse, error) {
	out := new(ListNotificationDeliveryHistoryResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListNotificationDeliveryHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetNotificationDeliveryHistoryDetail(ctx context.Context, in *GetNotificationDeliveryHistoryDetailRequest, opts ...grpc.CallOption) (*GetNotificationDeliveryHistoryDetailResponse, error) {
	out := new(GetNotificationDeliveryHistoryDetailResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetNotificationDeliveryHistoryDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) PingNotificationConfig(ctx context.Context, in *PingNotificationConfigRequest, opts ...grpc.CallOption) (*PingNotificationConfigResponse, error) {
	out := new(PingNotificationConfigResponse)
	err := c.cc.Invoke(ctx, OrganizationService_PingNotificationConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RedeliverNotification(ctx context.Context, in *RedeliverNotificationRequest, opts ...grpc.CallOption) (*RedeliverNotificationResponse, error) {
	out := new(RedeliverNotificationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RedeliverNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListOrganizationDomains(ctx context.Context, in *ListOrganizationDomainsRequest, opts ...grpc.CallOption) (*ListOrganizationDomainsResponse, error) {
	out := new(ListOrganizationDomainsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListOrganizationDomains_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteOrganizationDomain(ctx context.Context, in *DeleteOrganizationDomainRequest, opts ...grpc.CallOption) (*DeleteOrganizationDomainResponse, error) {
	out := new(DeleteOrganizationDomainResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteOrganizationDomain_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) VerifyOrganizationDomains(ctx context.Context, in *VerifyOrganizationDomainsRequest, opts ...grpc.CallOption) (*VerifyOrganizationDomainsResponse, error) {
	out := new(VerifyOrganizationDomainsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_VerifyOrganizationDomains_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateAIConversation(ctx context.Context, in *CreateAIConversationRequest, opts ...grpc.CallOption) (*CreateAIConversationResponse, error) {
	out := new(CreateAIConversationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateAIConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateIncident(ctx context.Context, in *CreateIncidentRequest, opts ...grpc.CallOption) (*CreateIncidentResponse, error) {
	out := new(CreateIncidentResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateIncident_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateAIConversation(ctx context.Context, in *UpdateAIConversationRequest, opts ...grpc.CallOption) (*UpdateAIConversationResponse, error) {
	out := new(UpdateAIConversationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateAIConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) DeleteAIConversation(ctx context.Context, in *DeleteAIConversationRequest, opts ...grpc.CallOption) (*DeleteAIConversationResponse, error) {
	out := new(DeleteAIConversationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_DeleteAIConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetAIConversation(ctx context.Context, in *GetAIConversationRequest, opts ...grpc.CallOption) (*GetAIConversationResponse, error) {
	out := new(GetAIConversationResponse)
	err := c.cc.Invoke(ctx, OrganizationService_GetAIConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) GetAIConversationStream(ctx context.Context, in *GetAIConversationStreamRequest, opts ...grpc.CallOption) (OrganizationService_GetAIConversationStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &OrganizationService_ServiceDesc.Streams[7], OrganizationService_GetAIConversationStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &organizationServiceGetAIConversationStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type OrganizationService_GetAIConversationStreamClient interface {
	Recv() (*GetAIConversationStreamResponse, error)
	grpc.ClientStream
}

type organizationServiceGetAIConversationStreamClient struct {
	grpc.ClientStream
}

func (x *organizationServiceGetAIConversationStreamClient) Recv() (*GetAIConversationStreamResponse, error) {
	m := new(GetAIConversationStreamResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *organizationServiceClient) ListAIConversations(ctx context.Context, in *ListAIConversationsRequest, opts ...grpc.CallOption) (*ListAIConversationsResponse, error) {
	out := new(ListAIConversationsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListAIConversations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) CreateAIMessage(ctx context.Context, in *CreateAIMessageRequest, opts ...grpc.CallOption) (*CreateAIMessageResponse, error) {
	out := new(CreateAIMessageResponse)
	err := c.cc.Invoke(ctx, OrganizationService_CreateAIMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListUsersMFAStatus(ctx context.Context, in *ListUsersMFAStatusRequest, opts ...grpc.CallOption) (*ListUsersMFAStatusResponse, error) {
	out := new(ListUsersMFAStatusResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListUsersMFAStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RequestMFAReset(ctx context.Context, in *RequestMFAResetRequest, opts ...grpc.CallOption) (*RequestMFAResetResponse, error) {
	out := new(RequestMFAResetResponse)
	err := c.cc.Invoke(ctx, OrganizationService_RequestMFAReset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ListAIConversationSuggestions(ctx context.Context, in *ListAIConversationSuggestionsRequest, opts ...grpc.CallOption) (*ListAIConversationSuggestionsResponse, error) {
	out := new(ListAIConversationSuggestionsResponse)
	err := c.cc.Invoke(ctx, OrganizationService_ListAIConversationSuggestions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateAIMessageFeedback(ctx context.Context, in *UpdateAIMessageFeedbackRequest, opts ...grpc.CallOption) (*UpdateAIMessageFeedbackResponse, error) {
	out := new(UpdateAIMessageFeedbackResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateAIMessageFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) UpdateAIConversationFeedback(ctx context.Context, in *UpdateAIConversationFeedbackRequest, opts ...grpc.CallOption) (*UpdateAIConversationFeedbackResponse, error) {
	out := new(UpdateAIConversationFeedbackResponse)
	err := c.cc.Invoke(ctx, OrganizationService_UpdateAIConversationFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrganizationServiceServer is the server API for OrganizationService service.
// All implementations must embed UnimplementedOrganizationServiceServer
// for forward compatibility
type OrganizationServiceServer interface {
	ListAuthenticatedUserOrganizations(context.Context, *ListAuthenticatedUserOrganizationsRequest) (*ListAuthenticatedUserOrganizationsResponse, error)
	GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error)
	GetOrganizationPermissions(context.Context, *GetOrganizationPermissionsRequest) (*GetOrganizationPermissionsResponse, error)
	CreateOrganization(context.Context, *CreateOrganizationRequest) (*CreateOrganizationResponse, error)
	UpdateOrganization(context.Context, *UpdateOrganizationRequest) (*UpdateOrganizationResponse, error)
	DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error)
	ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error)
	ListOrganizationInvitees(context.Context, *ListOrganizationInviteesRequest) (*ListOrganizationInviteesResponse, error)
	GetUserRoleInOrganization(context.Context, *GetUserRoleInOrganizationRequest) (*GetUserRoleInOrganizationResponse, error)
	InviteMembers(context.Context, *InviteMembersRequest) (*InviteMembersResponse, error)
	UninviteOrganizationMember(context.Context, *UninviteOrganizationMemberRequest) (*UninviteOrganizationMemberResponse, error)
	RemoveOrganizationMember(context.Context, *RemoveOrganizationMemberRequest) (*RemoveOrganizationMemberResponse, error)
	UpdateOrganizationMemberRole(context.Context, *UpdateOrganizationMemberRoleRequest) (*UpdateOrganizationMemberRoleResponse, error)
	JoinOrganization(context.Context, *JoinOrganizationRequest) (*JoinOrganizationResponse, error)
	RejectOrganization(context.Context, *RejectOrganizationRequest) (*RejectOrganizationResponse, error)
	ListOrganizationAPIKeys(context.Context, *ListOrganizationAPIKeysRequest) (*ListOrganizationAPIKeysResponse, error)
	CreateOrganizationAPIKey(context.Context, *CreateOrganizationAPIKeyRequest) (*CreateOrganizationAPIKeyResponse, error)
	ListWorkspaceAPIKeys(context.Context, *ListWorkspaceAPIKeysRequest) (*ListWorkspaceAPIKeysResponse, error)
	CreateWorkspaceAPIKey(context.Context, *CreateWorkspaceAPIKeyRequest) (*CreateWorkspaceAPIKeyResponse, error)
	GetAuditLogs(context.Context, *GetAuditLogsRequest) (*GetAuditLogsResponse, error)
	ListAuditLogsArchives(context.Context, *ListAuditLogsArchivesRequest) (*ListAuditLogsArchivesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetAuditLogsInCSV(*GetAuditLogsInCSVRequest, OrganizationService_GetAuditLogsInCSVServer) error
	GetCustomerDetails(context.Context, *GetCustomerDetailsRequest) (*GetCustomerDetailsResponse, error)
	UpdateBillingDetails(context.Context, *UpdateBillingDetailsRequest) (*UpdateBillingDetailsResponse, error)
	BillingCheckout(context.Context, *BillingCheckoutRequest) (*BillingCheckoutResponse, error)
	UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error)
	ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error)
	GetAvailableAddons(context.Context, *GetAvailableAddonsRequest) (*GetAvailableAddonsResponse, error)
	GetSSOConfiguration(context.Context, *GetSSOConfigurationRequest) (*GetSSOConfigurationResponse, error)
	EnsureSSOConfiguration(context.Context, *EnsureSSOConfigurationRequest) (*EnsureSSOConfigurationResponse, error)
	DeleteSSOConfiguration(context.Context, *DeleteSSOConfigurationRequest) (*DeleteSSOConfigurationResponse, error)
	GetFeatureStatuses(context.Context, *GetFeatureStatusesRequest) (*GetFeatureStatusesResponse, error)
	GetOIDCMap(context.Context, *GetOIDCMapRequest) (*GetOIDCMapResponse, error)
	UpdateOIDCMap(context.Context, *UpdateOIDCMapRequest) (*UpdateOIDCMapResponse, error)
	GetTeamOIDCMap(context.Context, *GetTeamOIDCMapRequest) (*GetTeamOIDCMapResponse, error)
	UpdateTeamOIDCMap(context.Context, *UpdateTeamOIDCMapRequest) (*UpdateTeamOIDCMapResponse, error)
	CreateCustomRole(context.Context, *CreateCustomRoleRequest) (*CreateCustomRoleResponse, error)
	UpdateCustomRole(context.Context, *UpdateCustomRoleRequest) (*UpdateCustomRoleResponse, error)
	GetCustomRole(context.Context, *GetCustomRoleRequest) (*GetCustomRoleResponse, error)
	ListCustomRoles(context.Context, *ListCustomRolesRequest) (*ListCustomRolesResponse, error)
	DeleteCustomRole(context.Context, *DeleteCustomRoleRequest) (*DeleteCustomRoleResponse, error)
	CreateWorkspaceCustomRole(context.Context, *CreateWorkspaceCustomRoleRequest) (*CreateWorkspaceCustomRoleResponse, error)
	UpdateWorkspaceCustomRole(context.Context, *UpdateWorkspaceCustomRoleRequest) (*UpdateWorkspaceCustomRoleResponse, error)
	GetWorkspaceCustomRole(context.Context, *GetWorkspaceCustomRoleRequest) (*GetWorkspaceCustomRoleResponse, error)
	ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error)
	DeleteWorkspaceCustomRole(context.Context, *DeleteWorkspaceCustomRoleRequest) (*DeleteWorkspaceCustomRoleResponse, error)
	CreateTeam(context.Context, *CreateTeamRequest) (*CreateTeamResponse, error)
	UpdateTeam(context.Context, *UpdateTeamRequest) (*UpdateTeamResponse, error)
	GetTeam(context.Context, *GetTeamRequest) (*GetTeamResponse, error)
	ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error)
	DeleteTeam(context.Context, *DeleteTeamRequest) (*DeleteTeamResponse, error)
	AddTeamMember(context.Context, *AddTeamMemberRequest) (*AddTeamMemberResponse, error)
	GetTeamMember(context.Context, *GetTeamMemberRequest) (*GetTeamMemberResponse, error)
	ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error)
	RemoveTeamMember(context.Context, *RemoveTeamMemberRequest) (*RemoveTeamMemberResponse, error)
	UpdateArgocdInstancesQuota(context.Context, *UpdateArgocdInstancesQuotaRequest) (*UpdateArgocdInstancesQuotaResponse, error)
	ListArgocdInstancesQuota(context.Context, *ListArgocdInstancesQuotaRequest) (*ListArgocdInstancesQuotaResponse, error)
	UpdateKargoInstancesQuota(context.Context, *UpdateKargoInstancesQuotaRequest) (*UpdateKargoInstancesQuotaResponse, error)
	ListKargoInstancesQuota(context.Context, *ListKargoInstancesQuotaRequest) (*ListKargoInstancesQuotaResponse, error)
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceResponse, error)
	ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error)
	GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error)
	UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*UpdateWorkspaceResponse, error)
	DeleteWorkspace(context.Context, *DeleteWorkspaceRequest) (*DeleteWorkspaceResponse, error)
	AddWorkspaceMember(context.Context, *AddWorkspaceMemberRequest) (*AddWorkspaceMemberResponse, error)
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	UpdateWorkspaceMembers(context.Context, *UpdateWorkspaceMembersRequest) (*UpdateWorkspaceMembersResponse, error)
	GetWorkspaceMember(context.Context, *GetWorkspaceMemberRequest) (*GetWorkspaceMemberResponse, error)
	UpdateWorkspaceMember(context.Context, *UpdateWorkspaceMemberRequest) (*UpdateWorkspaceMemberResponse, error)
	RemoveWorkspaceMember(context.Context, *RemoveWorkspaceMemberRequest) (*RemoveWorkspaceMemberResponse, error)
	CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error)
	ListKubernetesResourceTypes(context.Context, *ListKubernetesResourceTypesRequest) (*ListKubernetesResourceTypesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesResources(context.Context, *ListKubernetesResourcesRequest) (*ListKubernetesResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesResourcesToCSV(*ListKubernetesResourcesRequest, OrganizationService_ListKubernetesResourcesToCSVServer) error
	SpotlightSearchKubernetesResources(context.Context, *SpotlightSearchKubernetesResourcesRequest) (*SpotlightSearchKubernetesResourcesResponse, error)
	GetKubernetesResourceDetail(context.Context, *GetKubernetesResourceDetailRequest) (*GetKubernetesResourceDetailResponse, error)
	GetKubernetesContainer(context.Context, *GetKubernetesContainerRequest) (*GetKubernetesContainerResponse, error)
	ListKubernetesNamespaces(context.Context, *ListKubernetesNamespacesRequest) (*ListKubernetesNamespacesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesImages(context.Context, *ListKubernetesImagesRequest) (*ListKubernetesImagesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesImagesToCSV(*ListKubernetesImagesRequest, OrganizationService_ListKubernetesImagesToCSVServer) error
	GetKubernetesImageDetail(context.Context, *GetKubernetesImageDetailRequest) (*GetKubernetesImageDetailResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesContainers(context.Context, *ListKubernetesContainersRequest) (*ListKubernetesContainersResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesContainersToCSV(*ListKubernetesContainersRequest, OrganizationService_ListKubernetesContainersToCSVServer) error
	ListKubernetesEnabledClusters(context.Context, *ListKubernetesEnabledClustersRequest) (*ListKubernetesEnabledClustersResponse, error)
	GetKubernetesManifest(context.Context, *GetKubernetesManifestRequest) (*GetKubernetesManifestResponse, error)
	DeleteKubernetesResource(context.Context, *DeleteKubernetesResourceRequest) (*DeleteKubernetesResourceResponse, error)
	GetKubernetesLogs(*GetKubernetesLogsRequest, OrganizationService_GetKubernetesLogsServer) error
	GetKubernetesEvents(context.Context, *GetKubernetesEventsRequest) (*GetKubernetesEventsResponse, error)
	ListKubernetesAuditLogs(context.Context, *ListKubernetesAuditLogsRequest) (*ListKubernetesAuditLogsResponse, error)
	ListKubernetesNodes(context.Context, *ListKubernetesNodesRequest) (*ListKubernetesNodesResponse, error)
	GetKubernetesNode(context.Context, *GetKubernetesNodeRequest) (*GetKubernetesNodeResponse, error)
	ListKubernetesNamespacesDetails(context.Context, *ListKubernetesNamespacesDetailsRequest) (*ListKubernetesNamespacesDetailsResponse, error)
	GetKubernetesNamespaceDetail(context.Context, *GetKubernetesNamespaceDetailRequest) (*GetKubernetesNamespaceDetailResponse, error)
	GetKubernetesClusterDetail(context.Context, *GetKubernetesClusterDetailRequest) (*GetKubernetesClusterDetailResponse, error)
	GetKubernetesSummary(context.Context, *GetKubernetesSummaryRequest) (*GetKubernetesSummaryResponse, error)
	ListKubernetesPods(context.Context, *ListKubernetesPodsRequest) (*ListKubernetesPodsResponse, error)
	GetKubernetesPod(context.Context, *GetKubernetesPodRequest) (*GetKubernetesPodResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	ListKubernetesDeprecatedAPIs(context.Context, *ListKubernetesDeprecatedAPIsRequest) (*ListKubernetesDeprecatedAPIsResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	ListKubernetesDeprecatedAPIsToCSV(*ListKubernetesDeprecatedAPIsRequest, OrganizationService_ListKubernetesDeprecatedAPIsToCSVServer) error
	GetKubernetesAssistantSuggestion(context.Context, *GetKubernetesAssistantSuggestionRequest) (*GetKubernetesAssistantSuggestionResponse, error)
	ResolveKubernetesAssistantConversation(context.Context, *ResolveKubernetesAssistantConversationRequest) (*ResolveKubernetesAssistantConversationResponse, error)
	ListKubernetesTimelineEvents(context.Context, *ListKubernetesTimelineEventsRequest) (*ListKubernetesTimelineEventsResponse, error)
	ListKubernetesTimelineResources(context.Context, *ListKubernetesTimelineResourcesRequest) (*ListKubernetesTimelineResourcesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKubeVisionUsageToCSV(*GetKubeVisionUsageRequest, OrganizationService_GetKubeVisionUsageToCSVServer) error
	// Notification Configs
	ListNotificationConfigs(context.Context, *ListNotificationConfigsRequest) (*ListNotificationConfigsResponse, error)
	GetNotificationConfig(context.Context, *GetNotificationConfigRequest) (*GetNotificationConfigResponse, error)
	CreateNotificationConfig(context.Context, *CreateNotificationConfigRequest) (*CreateNotificationConfigResponse, error)
	UpdateNotificationConfig(context.Context, *UpdateNotificationConfigRequest) (*UpdateNotificationConfigResponse, error)
	DeleteNotificationConfig(context.Context, *DeleteNotificationConfigRequest) (*DeleteNotificationConfigResponse, error)
	ListNotificationDeliveryHistory(context.Context, *ListNotificationDeliveryHistoryRequest) (*ListNotificationDeliveryHistoryResponse, error)
	GetNotificationDeliveryHistoryDetail(context.Context, *GetNotificationDeliveryHistoryDetailRequest) (*GetNotificationDeliveryHistoryDetailResponse, error)
	PingNotificationConfig(context.Context, *PingNotificationConfigRequest) (*PingNotificationConfigResponse, error)
	RedeliverNotification(context.Context, *RedeliverNotificationRequest) (*RedeliverNotificationResponse, error)
	ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error)
	DeleteOrganizationDomain(context.Context, *DeleteOrganizationDomainRequest) (*DeleteOrganizationDomainResponse, error)
	VerifyOrganizationDomains(context.Context, *VerifyOrganizationDomainsRequest) (*VerifyOrganizationDomainsResponse, error)
	CreateAIConversation(context.Context, *CreateAIConversationRequest) (*CreateAIConversationResponse, error)
	CreateIncident(context.Context, *CreateIncidentRequest) (*CreateIncidentResponse, error)
	UpdateAIConversation(context.Context, *UpdateAIConversationRequest) (*UpdateAIConversationResponse, error)
	DeleteAIConversation(context.Context, *DeleteAIConversationRequest) (*DeleteAIConversationResponse, error)
	GetAIConversation(context.Context, *GetAIConversationRequest) (*GetAIConversationResponse, error)
	GetAIConversationStream(*GetAIConversationStreamRequest, OrganizationService_GetAIConversationStreamServer) error
	ListAIConversations(context.Context, *ListAIConversationsRequest) (*ListAIConversationsResponse, error)
	CreateAIMessage(context.Context, *CreateAIMessageRequest) (*CreateAIMessageResponse, error)
	ListUsersMFAStatus(context.Context, *ListUsersMFAStatusRequest) (*ListUsersMFAStatusResponse, error)
	RequestMFAReset(context.Context, *RequestMFAResetRequest) (*RequestMFAResetResponse, error)
	ListAIConversationSuggestions(context.Context, *ListAIConversationSuggestionsRequest) (*ListAIConversationSuggestionsResponse, error)
	UpdateAIMessageFeedback(context.Context, *UpdateAIMessageFeedbackRequest) (*UpdateAIMessageFeedbackResponse, error)
	UpdateAIConversationFeedback(context.Context, *UpdateAIConversationFeedbackRequest) (*UpdateAIConversationFeedbackResponse, error)
	mustEmbedUnimplementedOrganizationServiceServer()
}

// UnimplementedOrganizationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrganizationServiceServer struct {
}

func (UnimplementedOrganizationServiceServer) ListAuthenticatedUserOrganizations(context.Context, *ListAuthenticatedUserOrganizationsRequest) (*ListAuthenticatedUserOrganizationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuthenticatedUserOrganizations not implemented")
}
func (UnimplementedOrganizationServiceServer) GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) GetOrganizationPermissions(context.Context, *GetOrganizationPermissionsRequest) (*GetOrganizationPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrganizationPermissions not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateOrganization(context.Context, *CreateOrganizationRequest) (*CreateOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateOrganization(context.Context, *UpdateOrganizationRequest) (*UpdateOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationMembers not implemented")
}
func (UnimplementedOrganizationServiceServer) ListOrganizationInvitees(context.Context, *ListOrganizationInviteesRequest) (*ListOrganizationInviteesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationInvitees not implemented")
}
func (UnimplementedOrganizationServiceServer) GetUserRoleInOrganization(context.Context, *GetUserRoleInOrganizationRequest) (*GetUserRoleInOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserRoleInOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) InviteMembers(context.Context, *InviteMembersRequest) (*InviteMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InviteMembers not implemented")
}
func (UnimplementedOrganizationServiceServer) UninviteOrganizationMember(context.Context, *UninviteOrganizationMemberRequest) (*UninviteOrganizationMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UninviteOrganizationMember not implemented")
}
func (UnimplementedOrganizationServiceServer) RemoveOrganizationMember(context.Context, *RemoveOrganizationMemberRequest) (*RemoveOrganizationMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveOrganizationMember not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateOrganizationMemberRole(context.Context, *UpdateOrganizationMemberRoleRequest) (*UpdateOrganizationMemberRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrganizationMemberRole not implemented")
}
func (UnimplementedOrganizationServiceServer) JoinOrganization(context.Context, *JoinOrganizationRequest) (*JoinOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) RejectOrganization(context.Context, *RejectOrganizationRequest) (*RejectOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectOrganization not implemented")
}
func (UnimplementedOrganizationServiceServer) ListOrganizationAPIKeys(context.Context, *ListOrganizationAPIKeysRequest) (*ListOrganizationAPIKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationAPIKeys not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateOrganizationAPIKey(context.Context, *CreateOrganizationAPIKeyRequest) (*CreateOrganizationAPIKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrganizationAPIKey not implemented")
}
func (UnimplementedOrganizationServiceServer) ListWorkspaceAPIKeys(context.Context, *ListWorkspaceAPIKeysRequest) (*ListWorkspaceAPIKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceAPIKeys not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateWorkspaceAPIKey(context.Context, *CreateWorkspaceAPIKeyRequest) (*CreateWorkspaceAPIKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkspaceAPIKey not implemented")
}
func (UnimplementedOrganizationServiceServer) GetAuditLogs(context.Context, *GetAuditLogsRequest) (*GetAuditLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuditLogs not implemented")
}
func (UnimplementedOrganizationServiceServer) ListAuditLogsArchives(context.Context, *ListAuditLogsArchivesRequest) (*ListAuditLogsArchivesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuditLogsArchives not implemented")
}
func (UnimplementedOrganizationServiceServer) GetAuditLogsInCSV(*GetAuditLogsInCSVRequest, OrganizationService_GetAuditLogsInCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method GetAuditLogsInCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) GetCustomerDetails(context.Context, *GetCustomerDetailsRequest) (*GetCustomerDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerDetails not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateBillingDetails(context.Context, *UpdateBillingDetailsRequest) (*UpdateBillingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBillingDetails not implemented")
}
func (UnimplementedOrganizationServiceServer) BillingCheckout(context.Context, *BillingCheckoutRequest) (*BillingCheckoutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillingCheckout not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubscription not implemented")
}
func (UnimplementedOrganizationServiceServer) ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailablePlans not implemented")
}
func (UnimplementedOrganizationServiceServer) GetAvailableAddons(context.Context, *GetAvailableAddonsRequest) (*GetAvailableAddonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableAddons not implemented")
}
func (UnimplementedOrganizationServiceServer) GetSSOConfiguration(context.Context, *GetSSOConfigurationRequest) (*GetSSOConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSSOConfiguration not implemented")
}
func (UnimplementedOrganizationServiceServer) EnsureSSOConfiguration(context.Context, *EnsureSSOConfigurationRequest) (*EnsureSSOConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnsureSSOConfiguration not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteSSOConfiguration(context.Context, *DeleteSSOConfigurationRequest) (*DeleteSSOConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSSOConfiguration not implemented")
}
func (UnimplementedOrganizationServiceServer) GetFeatureStatuses(context.Context, *GetFeatureStatusesRequest) (*GetFeatureStatusesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeatureStatuses not implemented")
}
func (UnimplementedOrganizationServiceServer) GetOIDCMap(context.Context, *GetOIDCMapRequest) (*GetOIDCMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOIDCMap not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateOIDCMap(context.Context, *UpdateOIDCMapRequest) (*UpdateOIDCMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOIDCMap not implemented")
}
func (UnimplementedOrganizationServiceServer) GetTeamOIDCMap(context.Context, *GetTeamOIDCMapRequest) (*GetTeamOIDCMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeamOIDCMap not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateTeamOIDCMap(context.Context, *UpdateTeamOIDCMapRequest) (*UpdateTeamOIDCMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTeamOIDCMap not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateCustomRole(context.Context, *CreateCustomRoleRequest) (*CreateCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateCustomRole(context.Context, *UpdateCustomRoleRequest) (*UpdateCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) GetCustomRole(context.Context, *GetCustomRoleRequest) (*GetCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) ListCustomRoles(context.Context, *ListCustomRolesRequest) (*ListCustomRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomRoles not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteCustomRole(context.Context, *DeleteCustomRoleRequest) (*DeleteCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateWorkspaceCustomRole(context.Context, *CreateWorkspaceCustomRoleRequest) (*CreateWorkspaceCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkspaceCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateWorkspaceCustomRole(context.Context, *UpdateWorkspaceCustomRoleRequest) (*UpdateWorkspaceCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspaceCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) GetWorkspaceCustomRole(context.Context, *GetWorkspaceCustomRoleRequest) (*GetWorkspaceCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceCustomRoles not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteWorkspaceCustomRole(context.Context, *DeleteWorkspaceCustomRoleRequest) (*DeleteWorkspaceCustomRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspaceCustomRole not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateTeam(context.Context, *CreateTeamRequest) (*CreateTeamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTeam not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateTeam(context.Context, *UpdateTeamRequest) (*UpdateTeamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTeam not implemented")
}
func (UnimplementedOrganizationServiceServer) GetTeam(context.Context, *GetTeamRequest) (*GetTeamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeam not implemented")
}
func (UnimplementedOrganizationServiceServer) ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeams not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteTeam(context.Context, *DeleteTeamRequest) (*DeleteTeamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTeam not implemented")
}
func (UnimplementedOrganizationServiceServer) AddTeamMember(context.Context, *AddTeamMemberRequest) (*AddTeamMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTeamMember not implemented")
}
func (UnimplementedOrganizationServiceServer) GetTeamMember(context.Context, *GetTeamMemberRequest) (*GetTeamMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeamMember not implemented")
}
func (UnimplementedOrganizationServiceServer) ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeamMembers not implemented")
}
func (UnimplementedOrganizationServiceServer) RemoveTeamMember(context.Context, *RemoveTeamMemberRequest) (*RemoveTeamMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTeamMember not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateArgocdInstancesQuota(context.Context, *UpdateArgocdInstancesQuotaRequest) (*UpdateArgocdInstancesQuotaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateArgocdInstancesQuota not implemented")
}
func (UnimplementedOrganizationServiceServer) ListArgocdInstancesQuota(context.Context, *ListArgocdInstancesQuotaRequest) (*ListArgocdInstancesQuotaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListArgocdInstancesQuota not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateKargoInstancesQuota(context.Context, *UpdateKargoInstancesQuotaRequest) (*UpdateKargoInstancesQuotaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKargoInstancesQuota not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKargoInstancesQuota(context.Context, *ListKargoInstancesQuotaRequest) (*ListKargoInstancesQuotaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKargoInstancesQuota not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkspace not implemented")
}
func (UnimplementedOrganizationServiceServer) ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaces not implemented")
}
func (UnimplementedOrganizationServiceServer) GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspace not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateWorkspace(context.Context, *UpdateWorkspaceRequest) (*UpdateWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspace not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteWorkspace(context.Context, *DeleteWorkspaceRequest) (*DeleteWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkspace not implemented")
}
func (UnimplementedOrganizationServiceServer) AddWorkspaceMember(context.Context, *AddWorkspaceMemberRequest) (*AddWorkspaceMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWorkspaceMember not implemented")
}
func (UnimplementedOrganizationServiceServer) ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceMembers not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateWorkspaceMembers(context.Context, *UpdateWorkspaceMembersRequest) (*UpdateWorkspaceMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspaceMembers not implemented")
}
func (UnimplementedOrganizationServiceServer) GetWorkspaceMember(context.Context, *GetWorkspaceMemberRequest) (*GetWorkspaceMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceMember not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateWorkspaceMember(context.Context, *UpdateWorkspaceMemberRequest) (*UpdateWorkspaceMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspaceMember not implemented")
}
func (UnimplementedOrganizationServiceServer) RemoveWorkspaceMember(context.Context, *RemoveWorkspaceMemberRequest) (*RemoveWorkspaceMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveWorkspaceMember not implemented")
}
func (UnimplementedOrganizationServiceServer) CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSubscription not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesResourceTypes(context.Context, *ListKubernetesResourceTypesRequest) (*ListKubernetesResourceTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesResourceTypes not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesResources(context.Context, *ListKubernetesResourcesRequest) (*ListKubernetesResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesResources not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesResourcesToCSV(*ListKubernetesResourcesRequest, OrganizationService_ListKubernetesResourcesToCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method ListKubernetesResourcesToCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) SpotlightSearchKubernetesResources(context.Context, *SpotlightSearchKubernetesResourcesRequest) (*SpotlightSearchKubernetesResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpotlightSearchKubernetesResources not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesResourceDetail(context.Context, *GetKubernetesResourceDetailRequest) (*GetKubernetesResourceDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesResourceDetail not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesContainer(context.Context, *GetKubernetesContainerRequest) (*GetKubernetesContainerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesContainer not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesNamespaces(context.Context, *ListKubernetesNamespacesRequest) (*ListKubernetesNamespacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesNamespaces not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesImages(context.Context, *ListKubernetesImagesRequest) (*ListKubernetesImagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesImages not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesImagesToCSV(*ListKubernetesImagesRequest, OrganizationService_ListKubernetesImagesToCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method ListKubernetesImagesToCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesImageDetail(context.Context, *GetKubernetesImageDetailRequest) (*GetKubernetesImageDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesImageDetail not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesContainers(context.Context, *ListKubernetesContainersRequest) (*ListKubernetesContainersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesContainers not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesContainersToCSV(*ListKubernetesContainersRequest, OrganizationService_ListKubernetesContainersToCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method ListKubernetesContainersToCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesEnabledClusters(context.Context, *ListKubernetesEnabledClustersRequest) (*ListKubernetesEnabledClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesEnabledClusters not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesManifest(context.Context, *GetKubernetesManifestRequest) (*GetKubernetesManifestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesManifest not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteKubernetesResource(context.Context, *DeleteKubernetesResourceRequest) (*DeleteKubernetesResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKubernetesResource not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesLogs(*GetKubernetesLogsRequest, OrganizationService_GetKubernetesLogsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetKubernetesLogs not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesEvents(context.Context, *GetKubernetesEventsRequest) (*GetKubernetesEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesEvents not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesAuditLogs(context.Context, *ListKubernetesAuditLogsRequest) (*ListKubernetesAuditLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesAuditLogs not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesNodes(context.Context, *ListKubernetesNodesRequest) (*ListKubernetesNodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesNodes not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesNode(context.Context, *GetKubernetesNodeRequest) (*GetKubernetesNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesNode not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesNamespacesDetails(context.Context, *ListKubernetesNamespacesDetailsRequest) (*ListKubernetesNamespacesDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesNamespacesDetails not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesNamespaceDetail(context.Context, *GetKubernetesNamespaceDetailRequest) (*GetKubernetesNamespaceDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesNamespaceDetail not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesClusterDetail(context.Context, *GetKubernetesClusterDetailRequest) (*GetKubernetesClusterDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesClusterDetail not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesSummary(context.Context, *GetKubernetesSummaryRequest) (*GetKubernetesSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesSummary not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesPods(context.Context, *ListKubernetesPodsRequest) (*ListKubernetesPodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesPods not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesPod(context.Context, *GetKubernetesPodRequest) (*GetKubernetesPodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesPod not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesDeprecatedAPIs(context.Context, *ListKubernetesDeprecatedAPIsRequest) (*ListKubernetesDeprecatedAPIsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesDeprecatedAPIs not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesDeprecatedAPIsToCSV(*ListKubernetesDeprecatedAPIsRequest, OrganizationService_ListKubernetesDeprecatedAPIsToCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method ListKubernetesDeprecatedAPIsToCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubernetesAssistantSuggestion(context.Context, *GetKubernetesAssistantSuggestionRequest) (*GetKubernetesAssistantSuggestionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubernetesAssistantSuggestion not implemented")
}
func (UnimplementedOrganizationServiceServer) ResolveKubernetesAssistantConversation(context.Context, *ResolveKubernetesAssistantConversationRequest) (*ResolveKubernetesAssistantConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveKubernetesAssistantConversation not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesTimelineEvents(context.Context, *ListKubernetesTimelineEventsRequest) (*ListKubernetesTimelineEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesTimelineEvents not implemented")
}
func (UnimplementedOrganizationServiceServer) ListKubernetesTimelineResources(context.Context, *ListKubernetesTimelineResourcesRequest) (*ListKubernetesTimelineResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKubernetesTimelineResources not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubeVisionUsage not implemented")
}
func (UnimplementedOrganizationServiceServer) GetKubeVisionUsageToCSV(*GetKubeVisionUsageRequest, OrganizationService_GetKubeVisionUsageToCSVServer) error {
	return status.Errorf(codes.Unimplemented, "method GetKubeVisionUsageToCSV not implemented")
}
func (UnimplementedOrganizationServiceServer) ListNotificationConfigs(context.Context, *ListNotificationConfigsRequest) (*ListNotificationConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotificationConfigs not implemented")
}
func (UnimplementedOrganizationServiceServer) GetNotificationConfig(context.Context, *GetNotificationConfigRequest) (*GetNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotificationConfig not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateNotificationConfig(context.Context, *CreateNotificationConfigRequest) (*CreateNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNotificationConfig not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateNotificationConfig(context.Context, *UpdateNotificationConfigRequest) (*UpdateNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNotificationConfig not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteNotificationConfig(context.Context, *DeleteNotificationConfigRequest) (*DeleteNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNotificationConfig not implemented")
}
func (UnimplementedOrganizationServiceServer) ListNotificationDeliveryHistory(context.Context, *ListNotificationDeliveryHistoryRequest) (*ListNotificationDeliveryHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotificationDeliveryHistory not implemented")
}
func (UnimplementedOrganizationServiceServer) GetNotificationDeliveryHistoryDetail(context.Context, *GetNotificationDeliveryHistoryDetailRequest) (*GetNotificationDeliveryHistoryDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotificationDeliveryHistoryDetail not implemented")
}
func (UnimplementedOrganizationServiceServer) PingNotificationConfig(context.Context, *PingNotificationConfigRequest) (*PingNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PingNotificationConfig not implemented")
}
func (UnimplementedOrganizationServiceServer) RedeliverNotification(context.Context, *RedeliverNotificationRequest) (*RedeliverNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedeliverNotification not implemented")
}
func (UnimplementedOrganizationServiceServer) ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationDomains not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteOrganizationDomain(context.Context, *DeleteOrganizationDomainRequest) (*DeleteOrganizationDomainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOrganizationDomain not implemented")
}
func (UnimplementedOrganizationServiceServer) VerifyOrganizationDomains(context.Context, *VerifyOrganizationDomainsRequest) (*VerifyOrganizationDomainsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyOrganizationDomains not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateAIConversation(context.Context, *CreateAIConversationRequest) (*CreateAIConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAIConversation not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateIncident(context.Context, *CreateIncidentRequest) (*CreateIncidentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIncident not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateAIConversation(context.Context, *UpdateAIConversationRequest) (*UpdateAIConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAIConversation not implemented")
}
func (UnimplementedOrganizationServiceServer) DeleteAIConversation(context.Context, *DeleteAIConversationRequest) (*DeleteAIConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAIConversation not implemented")
}
func (UnimplementedOrganizationServiceServer) GetAIConversation(context.Context, *GetAIConversationRequest) (*GetAIConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAIConversation not implemented")
}
func (UnimplementedOrganizationServiceServer) GetAIConversationStream(*GetAIConversationStreamRequest, OrganizationService_GetAIConversationStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GetAIConversationStream not implemented")
}
func (UnimplementedOrganizationServiceServer) ListAIConversations(context.Context, *ListAIConversationsRequest) (*ListAIConversationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAIConversations not implemented")
}
func (UnimplementedOrganizationServiceServer) CreateAIMessage(context.Context, *CreateAIMessageRequest) (*CreateAIMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAIMessage not implemented")
}
func (UnimplementedOrganizationServiceServer) ListUsersMFAStatus(context.Context, *ListUsersMFAStatusRequest) (*ListUsersMFAStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUsersMFAStatus not implemented")
}
func (UnimplementedOrganizationServiceServer) RequestMFAReset(context.Context, *RequestMFAResetRequest) (*RequestMFAResetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestMFAReset not implemented")
}
func (UnimplementedOrganizationServiceServer) ListAIConversationSuggestions(context.Context, *ListAIConversationSuggestionsRequest) (*ListAIConversationSuggestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAIConversationSuggestions not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateAIMessageFeedback(context.Context, *UpdateAIMessageFeedbackRequest) (*UpdateAIMessageFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAIMessageFeedback not implemented")
}
func (UnimplementedOrganizationServiceServer) UpdateAIConversationFeedback(context.Context, *UpdateAIConversationFeedbackRequest) (*UpdateAIConversationFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAIConversationFeedback not implemented")
}
func (UnimplementedOrganizationServiceServer) mustEmbedUnimplementedOrganizationServiceServer() {}

// UnsafeOrganizationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrganizationServiceServer will
// result in compilation errors.
type UnsafeOrganizationServiceServer interface {
	mustEmbedUnimplementedOrganizationServiceServer()
}

func RegisterOrganizationServiceServer(s grpc.ServiceRegistrar, srv OrganizationServiceServer) {
	s.RegisterService(&OrganizationService_ServiceDesc, srv)
}

func _OrganizationService_ListAuthenticatedUserOrganizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuthenticatedUserOrganizationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListAuthenticatedUserOrganizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListAuthenticatedUserOrganizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListAuthenticatedUserOrganizations(ctx, req.(*ListAuthenticatedUserOrganizationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetOrganization(ctx, req.(*GetOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetOrganizationPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrganizationPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetOrganizationPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetOrganizationPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetOrganizationPermissions(ctx, req.(*GetOrganizationPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateOrganization(ctx, req.(*CreateOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateOrganization(ctx, req.(*UpdateOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteOrganization(ctx, req.(*DeleteOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListOrganizationMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListOrganizationMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListOrganizationMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListOrganizationMembers(ctx, req.(*ListOrganizationMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListOrganizationInvitees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationInviteesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListOrganizationInvitees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListOrganizationInvitees_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListOrganizationInvitees(ctx, req.(*ListOrganizationInviteesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetUserRoleInOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRoleInOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetUserRoleInOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetUserRoleInOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetUserRoleInOrganization(ctx, req.(*GetUserRoleInOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_InviteMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InviteMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).InviteMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_InviteMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).InviteMembers(ctx, req.(*InviteMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UninviteOrganizationMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UninviteOrganizationMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UninviteOrganizationMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UninviteOrganizationMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UninviteOrganizationMember(ctx, req.(*UninviteOrganizationMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RemoveOrganizationMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveOrganizationMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RemoveOrganizationMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RemoveOrganizationMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RemoveOrganizationMember(ctx, req.(*RemoveOrganizationMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateOrganizationMemberRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrganizationMemberRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateOrganizationMemberRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateOrganizationMemberRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateOrganizationMemberRole(ctx, req.(*UpdateOrganizationMemberRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_JoinOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).JoinOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_JoinOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).JoinOrganization(ctx, req.(*JoinOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RejectOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RejectOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RejectOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RejectOrganization(ctx, req.(*RejectOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListOrganizationAPIKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationAPIKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListOrganizationAPIKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListOrganizationAPIKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListOrganizationAPIKeys(ctx, req.(*ListOrganizationAPIKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateOrganizationAPIKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrganizationAPIKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateOrganizationAPIKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateOrganizationAPIKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateOrganizationAPIKey(ctx, req.(*CreateOrganizationAPIKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListWorkspaceAPIKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceAPIKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListWorkspaceAPIKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListWorkspaceAPIKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListWorkspaceAPIKeys(ctx, req.(*ListWorkspaceAPIKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateWorkspaceAPIKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceAPIKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateWorkspaceAPIKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateWorkspaceAPIKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateWorkspaceAPIKey(ctx, req.(*CreateWorkspaceAPIKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetAuditLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuditLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetAuditLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetAuditLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetAuditLogs(ctx, req.(*GetAuditLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListAuditLogsArchives_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuditLogsArchivesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListAuditLogsArchives(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListAuditLogsArchives_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListAuditLogsArchives(ctx, req.(*ListAuditLogsArchivesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetAuditLogsInCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetAuditLogsInCSVRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).GetAuditLogsInCSV(m, &organizationServiceGetAuditLogsInCSVServer{stream})
}

type OrganizationService_GetAuditLogsInCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceGetAuditLogsInCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceGetAuditLogsInCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_GetCustomerDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetCustomerDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetCustomerDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetCustomerDetails(ctx, req.(*GetCustomerDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateBillingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBillingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateBillingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateBillingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateBillingDetails(ctx, req.(*UpdateBillingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_BillingCheckout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillingCheckoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).BillingCheckout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_BillingCheckout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).BillingCheckout(ctx, req.(*BillingCheckoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateSubscription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateSubscription(ctx, req.(*UpdateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListAvailablePlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailablePlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListAvailablePlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListAvailablePlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListAvailablePlans(ctx, req.(*ListAvailablePlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetAvailableAddons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableAddonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetAvailableAddons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetAvailableAddons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetAvailableAddons(ctx, req.(*GetAvailableAddonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetSSOConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSSOConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetSSOConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetSSOConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetSSOConfiguration(ctx, req.(*GetSSOConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_EnsureSSOConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnsureSSOConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).EnsureSSOConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_EnsureSSOConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).EnsureSSOConfiguration(ctx, req.(*EnsureSSOConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteSSOConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSSOConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteSSOConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteSSOConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteSSOConfiguration(ctx, req.(*DeleteSSOConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetFeatureStatuses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeatureStatusesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetFeatureStatuses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetFeatureStatuses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetFeatureStatuses(ctx, req.(*GetFeatureStatusesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetOIDCMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOIDCMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetOIDCMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetOIDCMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetOIDCMap(ctx, req.(*GetOIDCMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateOIDCMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOIDCMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateOIDCMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateOIDCMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateOIDCMap(ctx, req.(*UpdateOIDCMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetTeamOIDCMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamOIDCMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetTeamOIDCMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetTeamOIDCMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetTeamOIDCMap(ctx, req.(*GetTeamOIDCMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateTeamOIDCMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTeamOIDCMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateTeamOIDCMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateTeamOIDCMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateTeamOIDCMap(ctx, req.(*UpdateTeamOIDCMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateCustomRole(ctx, req.(*CreateCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateCustomRole(ctx, req.(*UpdateCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetCustomRole(ctx, req.(*GetCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListCustomRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListCustomRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListCustomRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListCustomRoles(ctx, req.(*ListCustomRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteCustomRole(ctx, req.(*DeleteCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateWorkspaceCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateWorkspaceCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateWorkspaceCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateWorkspaceCustomRole(ctx, req.(*CreateWorkspaceCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateWorkspaceCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkspaceCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateWorkspaceCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateWorkspaceCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateWorkspaceCustomRole(ctx, req.(*UpdateWorkspaceCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetWorkspaceCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetWorkspaceCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetWorkspaceCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetWorkspaceCustomRole(ctx, req.(*GetWorkspaceCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListWorkspaceCustomRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceCustomRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListWorkspaceCustomRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListWorkspaceCustomRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListWorkspaceCustomRoles(ctx, req.(*ListWorkspaceCustomRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteWorkspaceCustomRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspaceCustomRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteWorkspaceCustomRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteWorkspaceCustomRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteWorkspaceCustomRole(ctx, req.(*DeleteWorkspaceCustomRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateTeam(ctx, req.(*CreateTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateTeam(ctx, req.(*UpdateTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetTeam(ctx, req.(*GetTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListTeams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListTeams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListTeams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListTeams(ctx, req.(*ListTeamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteTeam(ctx, req.(*DeleteTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_AddTeamMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTeamMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).AddTeamMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_AddTeamMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).AddTeamMember(ctx, req.(*AddTeamMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetTeamMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetTeamMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetTeamMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetTeamMember(ctx, req.(*GetTeamMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListTeamMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListTeamMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListTeamMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListTeamMembers(ctx, req.(*ListTeamMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RemoveTeamMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTeamMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RemoveTeamMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RemoveTeamMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RemoveTeamMember(ctx, req.(*RemoveTeamMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateArgocdInstancesQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateArgocdInstancesQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateArgocdInstancesQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateArgocdInstancesQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateArgocdInstancesQuota(ctx, req.(*UpdateArgocdInstancesQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListArgocdInstancesQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListArgocdInstancesQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListArgocdInstancesQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListArgocdInstancesQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListArgocdInstancesQuota(ctx, req.(*ListArgocdInstancesQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateKargoInstancesQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKargoInstancesQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateKargoInstancesQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateKargoInstancesQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateKargoInstancesQuota(ctx, req.(*UpdateKargoInstancesQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKargoInstancesQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKargoInstancesQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKargoInstancesQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKargoInstancesQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKargoInstancesQuota(ctx, req.(*ListKargoInstancesQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateWorkspace(ctx, req.(*CreateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListWorkspaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListWorkspaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListWorkspaces_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListWorkspaces(ctx, req.(*ListWorkspacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetWorkspace(ctx, req.(*GetWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateWorkspace(ctx, req.(*UpdateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteWorkspace(ctx, req.(*DeleteWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_AddWorkspaceMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWorkspaceMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).AddWorkspaceMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_AddWorkspaceMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).AddWorkspaceMember(ctx, req.(*AddWorkspaceMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListWorkspaceMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListWorkspaceMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListWorkspaceMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListWorkspaceMembers(ctx, req.(*ListWorkspaceMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateWorkspaceMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkspaceMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateWorkspaceMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateWorkspaceMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateWorkspaceMembers(ctx, req.(*UpdateWorkspaceMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetWorkspaceMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetWorkspaceMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetWorkspaceMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetWorkspaceMember(ctx, req.(*GetWorkspaceMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateWorkspaceMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkspaceMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateWorkspaceMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateWorkspaceMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateWorkspaceMember(ctx, req.(*UpdateWorkspaceMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RemoveWorkspaceMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveWorkspaceMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RemoveWorkspaceMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RemoveWorkspaceMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RemoveWorkspaceMember(ctx, req.(*RemoveWorkspaceMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CancelSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CancelSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CancelSubscription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CancelSubscription(ctx, req.(*CancelSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesResourceTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesResourceTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesResourceTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesResourceTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesResourceTypes(ctx, req.(*ListKubernetesResourceTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesResources(ctx, req.(*ListKubernetesResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesResourcesToCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ListKubernetesResourcesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).ListKubernetesResourcesToCSV(m, &organizationServiceListKubernetesResourcesToCSVServer{stream})
}

type OrganizationService_ListKubernetesResourcesToCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceListKubernetesResourcesToCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceListKubernetesResourcesToCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_SpotlightSearchKubernetesResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SpotlightSearchKubernetesResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).SpotlightSearchKubernetesResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_SpotlightSearchKubernetesResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).SpotlightSearchKubernetesResources(ctx, req.(*SpotlightSearchKubernetesResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesResourceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesResourceDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesResourceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesResourceDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesResourceDetail(ctx, req.(*GetKubernetesResourceDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesContainer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesContainerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesContainer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesContainer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesContainer(ctx, req.(*GetKubernetesContainerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesNamespaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesNamespacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesNamespaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesNamespaces_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesNamespaces(ctx, req.(*ListKubernetesNamespacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesImages(ctx, req.(*ListKubernetesImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesImagesToCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ListKubernetesImagesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).ListKubernetesImagesToCSV(m, &organizationServiceListKubernetesImagesToCSVServer{stream})
}

type OrganizationService_ListKubernetesImagesToCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceListKubernetesImagesToCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceListKubernetesImagesToCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_GetKubernetesImageDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesImageDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesImageDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesImageDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesImageDetail(ctx, req.(*GetKubernetesImageDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesContainers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesContainersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesContainers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesContainers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesContainers(ctx, req.(*ListKubernetesContainersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesContainersToCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ListKubernetesContainersRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).ListKubernetesContainersToCSV(m, &organizationServiceListKubernetesContainersToCSVServer{stream})
}

type OrganizationService_ListKubernetesContainersToCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceListKubernetesContainersToCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceListKubernetesContainersToCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_ListKubernetesEnabledClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesEnabledClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesEnabledClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesEnabledClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesEnabledClusters(ctx, req.(*ListKubernetesEnabledClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesManifestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesManifest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesManifest(ctx, req.(*GetKubernetesManifestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteKubernetesResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKubernetesResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteKubernetesResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteKubernetesResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteKubernetesResource(ctx, req.(*DeleteKubernetesResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesLogs_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetKubernetesLogsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).GetKubernetesLogs(m, &organizationServiceGetKubernetesLogsServer{stream})
}

type OrganizationService_GetKubernetesLogsServer interface {
	Send(*GetKubernetesLogsResponse) error
	grpc.ServerStream
}

type organizationServiceGetKubernetesLogsServer struct {
	grpc.ServerStream
}

func (x *organizationServiceGetKubernetesLogsServer) Send(m *GetKubernetesLogsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_GetKubernetesEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesEvents(ctx, req.(*GetKubernetesEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesAuditLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesAuditLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesAuditLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesAuditLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesAuditLogs(ctx, req.(*ListKubernetesAuditLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesNodes(ctx, req.(*ListKubernetesNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesNode(ctx, req.(*GetKubernetesNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesNamespacesDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesNamespacesDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesNamespacesDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesNamespacesDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesNamespacesDetails(ctx, req.(*ListKubernetesNamespacesDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesNamespaceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesNamespaceDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesNamespaceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesNamespaceDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesNamespaceDetail(ctx, req.(*GetKubernetesNamespaceDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesClusterDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesClusterDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesClusterDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesClusterDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesClusterDetail(ctx, req.(*GetKubernetesClusterDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesSummary(ctx, req.(*GetKubernetesSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesPods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesPodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesPods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesPods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesPods(ctx, req.(*ListKubernetesPodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubernetesPod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesPodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesPod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesPod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesPod(ctx, req.(*GetKubernetesPodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesDeprecatedAPIs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesDeprecatedAPIsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesDeprecatedAPIs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesDeprecatedAPIs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesDeprecatedAPIs(ctx, req.(*ListKubernetesDeprecatedAPIsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesDeprecatedAPIsToCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ListKubernetesDeprecatedAPIsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).ListKubernetesDeprecatedAPIsToCSV(m, &organizationServiceListKubernetesDeprecatedAPIsToCSVServer{stream})
}

type OrganizationService_ListKubernetesDeprecatedAPIsToCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceListKubernetesDeprecatedAPIsToCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceListKubernetesDeprecatedAPIsToCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_GetKubernetesAssistantSuggestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubernetesAssistantSuggestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubernetesAssistantSuggestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubernetesAssistantSuggestion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubernetesAssistantSuggestion(ctx, req.(*GetKubernetesAssistantSuggestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ResolveKubernetesAssistantConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveKubernetesAssistantConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ResolveKubernetesAssistantConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ResolveKubernetesAssistantConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ResolveKubernetesAssistantConversation(ctx, req.(*ResolveKubernetesAssistantConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesTimelineEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesTimelineEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesTimelineEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesTimelineEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesTimelineEvents(ctx, req.(*ListKubernetesTimelineEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListKubernetesTimelineResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKubernetesTimelineResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListKubernetesTimelineResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListKubernetesTimelineResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListKubernetesTimelineResources(ctx, req.(*ListKubernetesTimelineResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubeVisionUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubeVisionUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetKubeVisionUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetKubeVisionUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetKubeVisionUsage(ctx, req.(*GetKubeVisionUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetKubeVisionUsageToCSV_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetKubeVisionUsageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).GetKubeVisionUsageToCSV(m, &organizationServiceGetKubeVisionUsageToCSVServer{stream})
}

type OrganizationService_GetKubeVisionUsageToCSVServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type organizationServiceGetKubeVisionUsageToCSVServer struct {
	grpc.ServerStream
}

func (x *organizationServiceGetKubeVisionUsageToCSVServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_ListNotificationConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNotificationConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListNotificationConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListNotificationConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListNotificationConfigs(ctx, req.(*ListNotificationConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetNotificationConfig(ctx, req.(*GetNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateNotificationConfig(ctx, req.(*CreateNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateNotificationConfig(ctx, req.(*UpdateNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteNotificationConfig(ctx, req.(*DeleteNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListNotificationDeliveryHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNotificationDeliveryHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListNotificationDeliveryHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListNotificationDeliveryHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListNotificationDeliveryHistory(ctx, req.(*ListNotificationDeliveryHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetNotificationDeliveryHistoryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationDeliveryHistoryDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetNotificationDeliveryHistoryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetNotificationDeliveryHistoryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetNotificationDeliveryHistoryDetail(ctx, req.(*GetNotificationDeliveryHistoryDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_PingNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).PingNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_PingNotificationConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).PingNotificationConfig(ctx, req.(*PingNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RedeliverNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeliverNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RedeliverNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RedeliverNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RedeliverNotification(ctx, req.(*RedeliverNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListOrganizationDomains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationDomainsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListOrganizationDomains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListOrganizationDomains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListOrganizationDomains(ctx, req.(*ListOrganizationDomainsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteOrganizationDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOrganizationDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteOrganizationDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteOrganizationDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteOrganizationDomain(ctx, req.(*DeleteOrganizationDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_VerifyOrganizationDomains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyOrganizationDomainsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).VerifyOrganizationDomains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_VerifyOrganizationDomains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).VerifyOrganizationDomains(ctx, req.(*VerifyOrganizationDomainsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateAIConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateAIConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateAIConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateAIConversation(ctx, req.(*CreateAIConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateIncident_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIncidentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateIncident(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateIncident_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateIncident(ctx, req.(*CreateIncidentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateAIConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateAIConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateAIConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateAIConversation(ctx, req.(*UpdateAIConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_DeleteAIConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAIConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).DeleteAIConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_DeleteAIConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).DeleteAIConversation(ctx, req.(*DeleteAIConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetAIConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).GetAIConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_GetAIConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).GetAIConversation(ctx, req.(*GetAIConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_GetAIConversationStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetAIConversationStreamRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OrganizationServiceServer).GetAIConversationStream(m, &organizationServiceGetAIConversationStreamServer{stream})
}

type OrganizationService_GetAIConversationStreamServer interface {
	Send(*GetAIConversationStreamResponse) error
	grpc.ServerStream
}

type organizationServiceGetAIConversationStreamServer struct {
	grpc.ServerStream
}

func (x *organizationServiceGetAIConversationStreamServer) Send(m *GetAIConversationStreamResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _OrganizationService_ListAIConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAIConversationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListAIConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListAIConversations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListAIConversations(ctx, req.(*ListAIConversationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_CreateAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).CreateAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_CreateAIMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).CreateAIMessage(ctx, req.(*CreateAIMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListUsersMFAStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUsersMFAStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListUsersMFAStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListUsersMFAStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListUsersMFAStatus(ctx, req.(*ListUsersMFAStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RequestMFAReset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestMFAResetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RequestMFAReset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RequestMFAReset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RequestMFAReset(ctx, req.(*RequestMFAResetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ListAIConversationSuggestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAIConversationSuggestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ListAIConversationSuggestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ListAIConversationSuggestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ListAIConversationSuggestions(ctx, req.(*ListAIConversationSuggestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateAIMessageFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIMessageFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateAIMessageFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateAIMessageFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateAIMessageFeedback(ctx, req.(*UpdateAIMessageFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_UpdateAIConversationFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIConversationFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).UpdateAIConversationFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_UpdateAIConversationFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).UpdateAIConversationFeedback(ctx, req.(*UpdateAIConversationFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OrganizationService_ServiceDesc is the grpc.ServiceDesc for OrganizationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrganizationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "akuity.organization.v1.OrganizationService",
	HandlerType: (*OrganizationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAuthenticatedUserOrganizations",
			Handler:    _OrganizationService_ListAuthenticatedUserOrganizations_Handler,
		},
		{
			MethodName: "GetOrganization",
			Handler:    _OrganizationService_GetOrganization_Handler,
		},
		{
			MethodName: "GetOrganizationPermissions",
			Handler:    _OrganizationService_GetOrganizationPermissions_Handler,
		},
		{
			MethodName: "CreateOrganization",
			Handler:    _OrganizationService_CreateOrganization_Handler,
		},
		{
			MethodName: "UpdateOrganization",
			Handler:    _OrganizationService_UpdateOrganization_Handler,
		},
		{
			MethodName: "DeleteOrganization",
			Handler:    _OrganizationService_DeleteOrganization_Handler,
		},
		{
			MethodName: "ListOrganizationMembers",
			Handler:    _OrganizationService_ListOrganizationMembers_Handler,
		},
		{
			MethodName: "ListOrganizationInvitees",
			Handler:    _OrganizationService_ListOrganizationInvitees_Handler,
		},
		{
			MethodName: "GetUserRoleInOrganization",
			Handler:    _OrganizationService_GetUserRoleInOrganization_Handler,
		},
		{
			MethodName: "InviteMembers",
			Handler:    _OrganizationService_InviteMembers_Handler,
		},
		{
			MethodName: "UninviteOrganizationMember",
			Handler:    _OrganizationService_UninviteOrganizationMember_Handler,
		},
		{
			MethodName: "RemoveOrganizationMember",
			Handler:    _OrganizationService_RemoveOrganizationMember_Handler,
		},
		{
			MethodName: "UpdateOrganizationMemberRole",
			Handler:    _OrganizationService_UpdateOrganizationMemberRole_Handler,
		},
		{
			MethodName: "JoinOrganization",
			Handler:    _OrganizationService_JoinOrganization_Handler,
		},
		{
			MethodName: "RejectOrganization",
			Handler:    _OrganizationService_RejectOrganization_Handler,
		},
		{
			MethodName: "ListOrganizationAPIKeys",
			Handler:    _OrganizationService_ListOrganizationAPIKeys_Handler,
		},
		{
			MethodName: "CreateOrganizationAPIKey",
			Handler:    _OrganizationService_CreateOrganizationAPIKey_Handler,
		},
		{
			MethodName: "ListWorkspaceAPIKeys",
			Handler:    _OrganizationService_ListWorkspaceAPIKeys_Handler,
		},
		{
			MethodName: "CreateWorkspaceAPIKey",
			Handler:    _OrganizationService_CreateWorkspaceAPIKey_Handler,
		},
		{
			MethodName: "GetAuditLogs",
			Handler:    _OrganizationService_GetAuditLogs_Handler,
		},
		{
			MethodName: "ListAuditLogsArchives",
			Handler:    _OrganizationService_ListAuditLogsArchives_Handler,
		},
		{
			MethodName: "GetCustomerDetails",
			Handler:    _OrganizationService_GetCustomerDetails_Handler,
		},
		{
			MethodName: "UpdateBillingDetails",
			Handler:    _OrganizationService_UpdateBillingDetails_Handler,
		},
		{
			MethodName: "BillingCheckout",
			Handler:    _OrganizationService_BillingCheckout_Handler,
		},
		{
			MethodName: "UpdateSubscription",
			Handler:    _OrganizationService_UpdateSubscription_Handler,
		},
		{
			MethodName: "ListAvailablePlans",
			Handler:    _OrganizationService_ListAvailablePlans_Handler,
		},
		{
			MethodName: "GetAvailableAddons",
			Handler:    _OrganizationService_GetAvailableAddons_Handler,
		},
		{
			MethodName: "GetSSOConfiguration",
			Handler:    _OrganizationService_GetSSOConfiguration_Handler,
		},
		{
			MethodName: "EnsureSSOConfiguration",
			Handler:    _OrganizationService_EnsureSSOConfiguration_Handler,
		},
		{
			MethodName: "DeleteSSOConfiguration",
			Handler:    _OrganizationService_DeleteSSOConfiguration_Handler,
		},
		{
			MethodName: "GetFeatureStatuses",
			Handler:    _OrganizationService_GetFeatureStatuses_Handler,
		},
		{
			MethodName: "GetOIDCMap",
			Handler:    _OrganizationService_GetOIDCMap_Handler,
		},
		{
			MethodName: "UpdateOIDCMap",
			Handler:    _OrganizationService_UpdateOIDCMap_Handler,
		},
		{
			MethodName: "GetTeamOIDCMap",
			Handler:    _OrganizationService_GetTeamOIDCMap_Handler,
		},
		{
			MethodName: "UpdateTeamOIDCMap",
			Handler:    _OrganizationService_UpdateTeamOIDCMap_Handler,
		},
		{
			MethodName: "CreateCustomRole",
			Handler:    _OrganizationService_CreateCustomRole_Handler,
		},
		{
			MethodName: "UpdateCustomRole",
			Handler:    _OrganizationService_UpdateCustomRole_Handler,
		},
		{
			MethodName: "GetCustomRole",
			Handler:    _OrganizationService_GetCustomRole_Handler,
		},
		{
			MethodName: "ListCustomRoles",
			Handler:    _OrganizationService_ListCustomRoles_Handler,
		},
		{
			MethodName: "DeleteCustomRole",
			Handler:    _OrganizationService_DeleteCustomRole_Handler,
		},
		{
			MethodName: "CreateWorkspaceCustomRole",
			Handler:    _OrganizationService_CreateWorkspaceCustomRole_Handler,
		},
		{
			MethodName: "UpdateWorkspaceCustomRole",
			Handler:    _OrganizationService_UpdateWorkspaceCustomRole_Handler,
		},
		{
			MethodName: "GetWorkspaceCustomRole",
			Handler:    _OrganizationService_GetWorkspaceCustomRole_Handler,
		},
		{
			MethodName: "ListWorkspaceCustomRoles",
			Handler:    _OrganizationService_ListWorkspaceCustomRoles_Handler,
		},
		{
			MethodName: "DeleteWorkspaceCustomRole",
			Handler:    _OrganizationService_DeleteWorkspaceCustomRole_Handler,
		},
		{
			MethodName: "CreateTeam",
			Handler:    _OrganizationService_CreateTeam_Handler,
		},
		{
			MethodName: "UpdateTeam",
			Handler:    _OrganizationService_UpdateTeam_Handler,
		},
		{
			MethodName: "GetTeam",
			Handler:    _OrganizationService_GetTeam_Handler,
		},
		{
			MethodName: "ListTeams",
			Handler:    _OrganizationService_ListTeams_Handler,
		},
		{
			MethodName: "DeleteTeam",
			Handler:    _OrganizationService_DeleteTeam_Handler,
		},
		{
			MethodName: "AddTeamMember",
			Handler:    _OrganizationService_AddTeamMember_Handler,
		},
		{
			MethodName: "GetTeamMember",
			Handler:    _OrganizationService_GetTeamMember_Handler,
		},
		{
			MethodName: "ListTeamMembers",
			Handler:    _OrganizationService_ListTeamMembers_Handler,
		},
		{
			MethodName: "RemoveTeamMember",
			Handler:    _OrganizationService_RemoveTeamMember_Handler,
		},
		{
			MethodName: "UpdateArgocdInstancesQuota",
			Handler:    _OrganizationService_UpdateArgocdInstancesQuota_Handler,
		},
		{
			MethodName: "ListArgocdInstancesQuota",
			Handler:    _OrganizationService_ListArgocdInstancesQuota_Handler,
		},
		{
			MethodName: "UpdateKargoInstancesQuota",
			Handler:    _OrganizationService_UpdateKargoInstancesQuota_Handler,
		},
		{
			MethodName: "ListKargoInstancesQuota",
			Handler:    _OrganizationService_ListKargoInstancesQuota_Handler,
		},
		{
			MethodName: "CreateWorkspace",
			Handler:    _OrganizationService_CreateWorkspace_Handler,
		},
		{
			MethodName: "ListWorkspaces",
			Handler:    _OrganizationService_ListWorkspaces_Handler,
		},
		{
			MethodName: "GetWorkspace",
			Handler:    _OrganizationService_GetWorkspace_Handler,
		},
		{
			MethodName: "UpdateWorkspace",
			Handler:    _OrganizationService_UpdateWorkspace_Handler,
		},
		{
			MethodName: "DeleteWorkspace",
			Handler:    _OrganizationService_DeleteWorkspace_Handler,
		},
		{
			MethodName: "AddWorkspaceMember",
			Handler:    _OrganizationService_AddWorkspaceMember_Handler,
		},
		{
			MethodName: "ListWorkspaceMembers",
			Handler:    _OrganizationService_ListWorkspaceMembers_Handler,
		},
		{
			MethodName: "UpdateWorkspaceMembers",
			Handler:    _OrganizationService_UpdateWorkspaceMembers_Handler,
		},
		{
			MethodName: "GetWorkspaceMember",
			Handler:    _OrganizationService_GetWorkspaceMember_Handler,
		},
		{
			MethodName: "UpdateWorkspaceMember",
			Handler:    _OrganizationService_UpdateWorkspaceMember_Handler,
		},
		{
			MethodName: "RemoveWorkspaceMember",
			Handler:    _OrganizationService_RemoveWorkspaceMember_Handler,
		},
		{
			MethodName: "CancelSubscription",
			Handler:    _OrganizationService_CancelSubscription_Handler,
		},
		{
			MethodName: "ListKubernetesResourceTypes",
			Handler:    _OrganizationService_ListKubernetesResourceTypes_Handler,
		},
		{
			MethodName: "ListKubernetesResources",
			Handler:    _OrganizationService_ListKubernetesResources_Handler,
		},
		{
			MethodName: "SpotlightSearchKubernetesResources",
			Handler:    _OrganizationService_SpotlightSearchKubernetesResources_Handler,
		},
		{
			MethodName: "GetKubernetesResourceDetail",
			Handler:    _OrganizationService_GetKubernetesResourceDetail_Handler,
		},
		{
			MethodName: "GetKubernetesContainer",
			Handler:    _OrganizationService_GetKubernetesContainer_Handler,
		},
		{
			MethodName: "ListKubernetesNamespaces",
			Handler:    _OrganizationService_ListKubernetesNamespaces_Handler,
		},
		{
			MethodName: "ListKubernetesImages",
			Handler:    _OrganizationService_ListKubernetesImages_Handler,
		},
		{
			MethodName: "GetKubernetesImageDetail",
			Handler:    _OrganizationService_GetKubernetesImageDetail_Handler,
		},
		{
			MethodName: "ListKubernetesContainers",
			Handler:    _OrganizationService_ListKubernetesContainers_Handler,
		},
		{
			MethodName: "ListKubernetesEnabledClusters",
			Handler:    _OrganizationService_ListKubernetesEnabledClusters_Handler,
		},
		{
			MethodName: "GetKubernetesManifest",
			Handler:    _OrganizationService_GetKubernetesManifest_Handler,
		},
		{
			MethodName: "DeleteKubernetesResource",
			Handler:    _OrganizationService_DeleteKubernetesResource_Handler,
		},
		{
			MethodName: "GetKubernetesEvents",
			Handler:    _OrganizationService_GetKubernetesEvents_Handler,
		},
		{
			MethodName: "ListKubernetesAuditLogs",
			Handler:    _OrganizationService_ListKubernetesAuditLogs_Handler,
		},
		{
			MethodName: "ListKubernetesNodes",
			Handler:    _OrganizationService_ListKubernetesNodes_Handler,
		},
		{
			MethodName: "GetKubernetesNode",
			Handler:    _OrganizationService_GetKubernetesNode_Handler,
		},
		{
			MethodName: "ListKubernetesNamespacesDetails",
			Handler:    _OrganizationService_ListKubernetesNamespacesDetails_Handler,
		},
		{
			MethodName: "GetKubernetesNamespaceDetail",
			Handler:    _OrganizationService_GetKubernetesNamespaceDetail_Handler,
		},
		{
			MethodName: "GetKubernetesClusterDetail",
			Handler:    _OrganizationService_GetKubernetesClusterDetail_Handler,
		},
		{
			MethodName: "GetKubernetesSummary",
			Handler:    _OrganizationService_GetKubernetesSummary_Handler,
		},
		{
			MethodName: "ListKubernetesPods",
			Handler:    _OrganizationService_ListKubernetesPods_Handler,
		},
		{
			MethodName: "GetKubernetesPod",
			Handler:    _OrganizationService_GetKubernetesPod_Handler,
		},
		{
			MethodName: "ListKubernetesDeprecatedAPIs",
			Handler:    _OrganizationService_ListKubernetesDeprecatedAPIs_Handler,
		},
		{
			MethodName: "GetKubernetesAssistantSuggestion",
			Handler:    _OrganizationService_GetKubernetesAssistantSuggestion_Handler,
		},
		{
			MethodName: "ResolveKubernetesAssistantConversation",
			Handler:    _OrganizationService_ResolveKubernetesAssistantConversation_Handler,
		},
		{
			MethodName: "ListKubernetesTimelineEvents",
			Handler:    _OrganizationService_ListKubernetesTimelineEvents_Handler,
		},
		{
			MethodName: "ListKubernetesTimelineResources",
			Handler:    _OrganizationService_ListKubernetesTimelineResources_Handler,
		},
		{
			MethodName: "GetKubeVisionUsage",
			Handler:    _OrganizationService_GetKubeVisionUsage_Handler,
		},
		{
			MethodName: "ListNotificationConfigs",
			Handler:    _OrganizationService_ListNotificationConfigs_Handler,
		},
		{
			MethodName: "GetNotificationConfig",
			Handler:    _OrganizationService_GetNotificationConfig_Handler,
		},
		{
			MethodName: "CreateNotificationConfig",
			Handler:    _OrganizationService_CreateNotificationConfig_Handler,
		},
		{
			MethodName: "UpdateNotificationConfig",
			Handler:    _OrganizationService_UpdateNotificationConfig_Handler,
		},
		{
			MethodName: "DeleteNotificationConfig",
			Handler:    _OrganizationService_DeleteNotificationConfig_Handler,
		},
		{
			MethodName: "ListNotificationDeliveryHistory",
			Handler:    _OrganizationService_ListNotificationDeliveryHistory_Handler,
		},
		{
			MethodName: "GetNotificationDeliveryHistoryDetail",
			Handler:    _OrganizationService_GetNotificationDeliveryHistoryDetail_Handler,
		},
		{
			MethodName: "PingNotificationConfig",
			Handler:    _OrganizationService_PingNotificationConfig_Handler,
		},
		{
			MethodName: "RedeliverNotification",
			Handler:    _OrganizationService_RedeliverNotification_Handler,
		},
		{
			MethodName: "ListOrganizationDomains",
			Handler:    _OrganizationService_ListOrganizationDomains_Handler,
		},
		{
			MethodName: "DeleteOrganizationDomain",
			Handler:    _OrganizationService_DeleteOrganizationDomain_Handler,
		},
		{
			MethodName: "VerifyOrganizationDomains",
			Handler:    _OrganizationService_VerifyOrganizationDomains_Handler,
		},
		{
			MethodName: "CreateAIConversation",
			Handler:    _OrganizationService_CreateAIConversation_Handler,
		},
		{
			MethodName: "CreateIncident",
			Handler:    _OrganizationService_CreateIncident_Handler,
		},
		{
			MethodName: "UpdateAIConversation",
			Handler:    _OrganizationService_UpdateAIConversation_Handler,
		},
		{
			MethodName: "DeleteAIConversation",
			Handler:    _OrganizationService_DeleteAIConversation_Handler,
		},
		{
			MethodName: "GetAIConversation",
			Handler:    _OrganizationService_GetAIConversation_Handler,
		},
		{
			MethodName: "ListAIConversations",
			Handler:    _OrganizationService_ListAIConversations_Handler,
		},
		{
			MethodName: "CreateAIMessage",
			Handler:    _OrganizationService_CreateAIMessage_Handler,
		},
		{
			MethodName: "ListUsersMFAStatus",
			Handler:    _OrganizationService_ListUsersMFAStatus_Handler,
		},
		{
			MethodName: "RequestMFAReset",
			Handler:    _OrganizationService_RequestMFAReset_Handler,
		},
		{
			MethodName: "ListAIConversationSuggestions",
			Handler:    _OrganizationService_ListAIConversationSuggestions_Handler,
		},
		{
			MethodName: "UpdateAIMessageFeedback",
			Handler:    _OrganizationService_UpdateAIMessageFeedback_Handler,
		},
		{
			MethodName: "UpdateAIConversationFeedback",
			Handler:    _OrganizationService_UpdateAIConversationFeedback_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetAuditLogsInCSV",
			Handler:       _OrganizationService_GetAuditLogsInCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ListKubernetesResourcesToCSV",
			Handler:       _OrganizationService_ListKubernetesResourcesToCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ListKubernetesImagesToCSV",
			Handler:       _OrganizationService_ListKubernetesImagesToCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ListKubernetesContainersToCSV",
			Handler:       _OrganizationService_ListKubernetesContainersToCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetKubernetesLogs",
			Handler:       _OrganizationService_GetKubernetesLogs_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ListKubernetesDeprecatedAPIsToCSV",
			Handler:       _OrganizationService_ListKubernetesDeprecatedAPIsToCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetKubeVisionUsageToCSV",
			Handler:       _OrganizationService_GetKubeVisionUsageToCSV_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetAIConversationStream",
			Handler:       _OrganizationService_GetAIConversationStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "organization/v1/organization.proto",
}
